<?php
require_once 'dashboard/includes/database.php';

try {
    $db = connectDB();
    echo 'Connected to database successfully!' . PHP_EOL;
    
    // Check if product_categories table exists
    $tables = $db->query("SHOW TABLES LIKE 'product_categories'")->fetchAll();
    if (empty($tables)) {
        echo 'product_categories table does not exist!' . PHP_EOL;
        
        // Check what tables exist
        echo 'Available tables:' . PHP_EOL;
        $allTables = $db->query("SHOW TABLES")->fetchAll();
        foreach ($allTables as $table) {
            echo '- ' . array_values($table)[0] . PHP_EOL;
        }
    } else {
        echo 'product_categories table exists!' . PHP_EOL;
        
        // Show table structure
        echo 'Table structure:' . PHP_EOL;
        $structure = $db->query("DESCRIBE product_categories")->fetchAll();
        foreach ($structure as $column) {
            echo "- {$column['Field']} ({$column['Type']}) - Null: {$column['Null']}, Key: {$column['Key']}" . PHP_EOL;
        }
        
        // Show sample data
        echo PHP_EOL . 'Sample data:' . PHP_EOL;
        $sample = $db->query("SELECT * FROM product_categories LIMIT 3")->fetchAll();
        foreach ($sample as $row) {
            echo '- ' . json_encode($row) . PHP_EOL;
        }
    }
    
    // Also check categories table
    echo PHP_EOL . 'Checking categories table:' . PHP_EOL;
    $catTables = $db->query("SHOW TABLES LIKE 'categories'")->fetchAll();
    if (!empty($catTables)) {
        echo 'categories table exists!' . PHP_EOL;
        $catStructure = $db->query("DESCRIBE categories")->fetchAll();
        foreach ($catStructure as $column) {
            echo "- {$column['Field']} ({$column['Type']})" . PHP_EOL;
        }
    }
    
} catch (Exception $e) {
    echo 'Error: ' . $e->getMessage() . PHP_EOL;
}
?>
