<?php
// Drag & Drop Page Builder
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/auth.php';

global $db;
if (!isset($db)) {
    $db = connectDB();
}

$user = getCurrentUser();
$mode = $_GET['mode'] ?? 'create';
$pageId = $_GET['id'] ?? null;
$templateId = $_GET['template'] ?? null;

// Load existing page data if editing
$pageData = null;
if ($mode === 'edit' && $pageId) {
    try {
        $stmt = $db->prepare("SELECT * FROM landing_pages WHERE id = ? AND (created_by = ? OR ? = 'admin')");
        $stmt->execute([$pageId, $user['id'], $user['role']]);
        $pageData = $stmt->fetch();
    } catch (Exception $e) {
        error_log("Error loading page: " . $e->getMessage());
    }
}

// Load template data if using template
$templateData = null;
if ($mode === 'template' && $templateId) {
    try {
        $stmt = $db->prepare("SELECT * FROM landing_page_templates WHERE id = ?");
        $stmt->execute([$templateId]);
        $templateData = $stmt->fetch();
    } catch (Exception $e) {
        error_log("Error loading template: " . $e->getMessage());
    }
}
?>

<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Constructeur de Page - <?php echo $mode === 'edit' ? 'Édition' : 'Création'; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Sortable.js for drag & drop -->
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>

    <style>
        body {
            margin: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
        }

        .page-builder {
            display: flex;
            height: 100vh;
            overflow: hidden;
        }

        .builder-sidebar {
            width: 300px;
            background: white;
            border-right: 1px solid #e9ecef;
            overflow-y: auto;
            flex-shrink: 0;
        }

        .builder-main {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .builder-toolbar {
            background: white;
            border-bottom: 1px solid #e9ecef;
            padding: 10px 20px;
            display: flex;
            justify-content: between;
            align-items: center;
            flex-shrink: 0;
        }

        .builder-canvas {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
        }

        .canvas-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            min-height: 800px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            position: relative;
        }

        .element-toolbar {
            background: white;
            border-bottom: 1px solid #e9ecef;
            padding: 15px 20px;
            display: none;
        }

        /* Sidebar Sections */
        .sidebar-section {
            border-bottom: 1px solid #e9ecef;
        }

        .sidebar-header {
            padding: 15px 20px;
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
            cursor: pointer;
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .sidebar-content {
            padding: 15px 20px;
        }

        /* Draggable Elements */
        .element-item {
            display: flex;
            align-items: center;
            padding: 10px;
            margin-bottom: 8px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            cursor: grab;
            transition: all 0.2s;
        }

        .element-item:hover {
            background: #e9ecef;
            transform: translateY(-1px);
        }

        .element-item:active {
            cursor: grabbing;
        }

        .element-icon {
            width: 24px;
            height: 24px;
            margin-right: 10px;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Canvas Elements */
        .canvas-element {
            position: relative;
            margin: 10px 0;
            padding: 15px;
            border: 2px dashed transparent;
            border-radius: 4px;
            transition: all 0.2s;
        }

        .canvas-element:hover {
            border-color: #007bff;
            background: rgba(0, 123, 255, 0.05);
        }

        .canvas-element.selected {
            border-color: #007bff;
            background: rgba(0, 123, 255, 0.1);
        }

        .element-controls {
            position: absolute;
            top: -30px;
            right: 0;
            display: none;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .canvas-element:hover .element-controls,
        .canvas-element.selected .element-controls {
            display: flex;
        }

        .control-btn {
            padding: 5px 8px;
            border: none;
            background: none;
            color: #6c757d;
            cursor: pointer;
            font-size: 12px;
        }

        .control-btn:hover {
            background: #f8f9fa;
            color: #495057;
        }

        /* Drop Zones */
        .drop-zone {
            min-height: 60px;
            border: 2px dashed #dee2e6;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            margin: 10px 0;
            transition: all 0.2s;
        }

        .drop-zone.drag-over {
            border-color: #007bff;
            background: rgba(0, 123, 255, 0.05);
            color: #007bff;
        }

        .drop-zone:empty::before {
            content: "Glissez un élément ici";
            font-style: italic;
        }

        /* Element Types */
        .element-title {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
            margin: 20px 0;
            text-align: center;
        }

        .element-text {
            font-size: 1rem;
            line-height: 1.6;
            color: #495057;
            margin: 15px 0;
        }

        .element-image {
            text-align: center;
            margin: 20px 0;
        }

        .element-image img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
        }

        .element-image.placeholder {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            padding: 40px;
            color: #6c757d;
        }

        .element-button {
            text-align: center;
            margin: 20px 0;
        }

        .element-button .btn {
            padding: 12px 30px;
            font-size: 1.1rem;
            border-radius: 25px;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .builder-sidebar {
                width: 250px;
            }

            .canvas-container {
                margin: 0 10px;
            }
        }
    </style>
</head>

<body>
    <div class="page-builder">
        <!-- Sidebar -->
        <div class="builder-sidebar">
            <!-- Page Settings -->
            <div class="sidebar-section">
                <div class="sidebar-header" onclick="toggleSection('page-settings')">
                    <span><i class="fas fa-cog me-2"></i>Paramètres de la page</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="sidebar-content" id="page-settings">
                    <div class="mb-3">
                        <label class="form-label">Titre de la page</label>
                        <input type="text" class="form-control" id="pageTitle" placeholder="Titre de votre page"
                            value="<?php echo htmlspecialchars($pageData['title'] ?? $templateData['name'] ?? ''); ?>">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">URL (slug)</label>
                        <input type="text" class="form-control" id="pageSlug" placeholder="url-de-votre-page"
                            value="<?php echo htmlspecialchars($pageData['slug'] ?? ''); ?>">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea class="form-control" id="pageDescription" rows="3" placeholder="Description pour le SEO"><?php echo htmlspecialchars($pageData['meta_description'] ?? ''); ?></textarea>
                    </div>
                </div>
            </div>

            <!-- Elements -->
            <div class="sidebar-section">
                <div class="sidebar-header" onclick="toggleSection('elements')">
                    <span><i class="fas fa-puzzle-piece me-2"></i>Éléments</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="sidebar-content" id="elements">
                    <div class="element-item" draggable="true" data-type="title">
                        <div class="element-icon"><i class="fas fa-heading"></i></div>
                        <span>Titre</span>
                    </div>
                    <div class="element-item" draggable="true" data-type="text">
                        <div class="element-icon"><i class="fas fa-align-left"></i></div>
                        <span>Texte</span>
                    </div>
                    <div class="element-item" draggable="true" data-type="image">
                        <div class="element-icon"><i class="fas fa-image"></i></div>
                        <span>Image</span>
                    </div>
                    <div class="element-item" draggable="true" data-type="button">
                        <div class="element-icon"><i class="fas fa-mouse-pointer"></i></div>
                        <span>Bouton</span>
                    </div>
                    <div class="element-item" draggable="true" data-type="spacer">
                        <div class="element-icon"><i class="fas fa-arrows-alt-v"></i></div>
                        <span>Espacement</span>
                    </div>
                    <div class="element-item" onclick="openFormBuilder()" style="cursor: pointer;">
                        <div class="element-icon"><i class="fas fa-envelope"></i></div>
                        <span>Formulaire de Contact</span>
                    </div>
                </div>
            </div>

            <!-- E-commerce Elements -->
            <div class="sidebar-section">
                <div class="sidebar-header" onclick="toggleSection('ecommerce-elements')">
                    <span><i class="fas fa-shopping-cart me-2"></i>E-commerce</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="sidebar-content" id="ecommerce-elements">
                    <div class="element-item" draggable="true" data-type="product-card">
                        <div class="element-icon"><i class="fas fa-box"></i></div>
                        <span>Carte Produit</span>
                    </div>
                    <div class="element-item" draggable="true" data-type="buy-button">
                        <div class="element-icon"><i class="fas fa-shopping-cart"></i></div>
                        <span>Bouton Acheter</span>
                    </div>
                    <div class="element-item" draggable="true" data-type="price-display">
                        <div class="element-icon"><i class="fas fa-tag"></i></div>
                        <span>Affichage Prix</span>
                    </div>
                    <div class="element-item" draggable="true" data-type="quantity-selector">
                        <div class="element-icon"><i class="fas fa-plus-minus"></i></div>
                        <span>Sélecteur Quantité</span>
                    </div>
                    <div class="element-item" draggable="true" data-type="product-gallery">
                        <div class="element-icon"><i class="fas fa-images"></i></div>
                        <span>Galerie Produit</span>
                    </div>
                    <div class="element-item" draggable="true" data-type="cart-widget">
                        <div class="element-icon"><i class="fas fa-shopping-bag"></i></div>
                        <span>Widget Panier</span>
                    </div>
                    <div class="element-item" draggable="true" data-type="product-list">
                        <div class="element-icon"><i class="fas fa-list"></i></div>
                        <span>Liste Produits</span>
                    </div>
                </div>
            </div>

            <!-- AI Assistant -->
            <div class="sidebar-section">
                <div class="sidebar-header" onclick="toggleSection('ai-assistant')">
                    <span><i class="fas fa-robot me-2"></i>Assistant IA</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="sidebar-content" id="ai-assistant">
                    <button class="btn btn-outline-primary btn-sm w-100 mb-2" onclick="generateAITitle()">
                        <i class="fas fa-heading me-2"></i>Générer un titre
                    </button>
                    <button class="btn btn-outline-primary btn-sm w-100 mb-2" onclick="generateAIContent()">
                        <i class="fas fa-align-left me-2"></i>Générer du contenu
                    </button>
                    <div class="mt-3">
                        <label class="form-label">Prompt personnalisé</label>
                        <textarea class="form-control" id="customPrompt" rows="3" placeholder="Décrivez ce que vous voulez..."></textarea>
                        <button class="btn btn-primary btn-sm w-100 mt-2" onclick="generateCustomAI()">
                            <i class="fas fa-magic me-2"></i>Générer
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Builder Area -->
        <div class="builder-main">
            <!-- Toolbar -->
            <div class="builder-toolbar">
                <div class="d-flex align-items-center">
                    <button class="btn btn-outline-secondary me-2" onclick="goBack()">
                        <i class="fas fa-arrow-left me-2"></i>Retour
                    </button>
                    <h5 class="mb-0 me-3">
                        <?php echo $mode === 'edit' ? 'Édition' : 'Création'; ?> de page
                    </h5>
                    <span class="badge bg-secondary" id="pageStatus">Brouillon</span>
                </div>
                <div class="d-flex align-items-center gap-2">
                    <button class="btn btn-outline-primary" onclick="previewPage()">
                        <i class="fas fa-eye me-2"></i>Aperçu
                    </button>
                    <button class="btn btn-success" onclick="savePage()">
                        <i class="fas fa-save me-2"></i>Sauvegarder
                    </button>
                    <button class="btn btn-primary" onclick="publishPage()">
                        <i class="fas fa-globe me-2"></i>Publier
                    </button>
                </div>
            </div>

            <!-- Element Toolbar (shown when element is selected) -->
            <div class="element-toolbar" id="elementToolbar">
                <div class="d-flex align-items-center gap-3">
                    <span id="selectedElementType"></span>
                    <div class="vr"></div>
                    <button class="btn btn-sm btn-outline-primary" onclick="editElementText()">
                        <i class="fas fa-edit me-1"></i>Modifier le texte
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" onclick="editElementStyle()">
                        <i class="fas fa-palette me-1"></i>Style
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteSelectedElement()">
                        <i class="fas fa-trash me-1"></i>Supprimer
                    </button>
                </div>
            </div>

            <!-- Canvas -->
            <div class="builder-canvas">
                <div class="canvas-container" id="canvas">
                    <div class="drop-zone" id="mainDropZone"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Include Contact Form Builder -->
    <?php include __DIR__ . '/../components/contact-form-builder.php'; ?>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Page Builder JavaScript
        console.log('Page Builder JavaScript loading...');

        let selectedElement = null;
        let elementCounter = 0;

        // Error handling for uncaught errors
        window.addEventListener('error', function(e) {
            console.error('JavaScript Error:', e.error);
            console.error('At line:', e.lineno, 'column:', e.colno);
            console.error('In file:', e.filename);
        });

        // Initialize page builder
        document.addEventListener('DOMContentLoaded', function() {
            initializeDragAndDrop();
            loadPageContent();
        });

        function initializeDragAndDrop() {
            // Make elements draggable
            const elements = document.querySelectorAll('.element-item');
            elements.forEach(element => {
                element.addEventListener('dragstart', handleDragStart);
            });

            // Make canvas droppable
            const canvas = document.getElementById('canvas');
            canvas.addEventListener('dragover', handleDragOver);
            canvas.addEventListener('drop', handleDrop);

            // Make canvas sortable
            new Sortable(canvas, {
                animation: 150,
                ghostClass: 'sortable-ghost',
                chosenClass: 'sortable-chosen',
                dragClass: 'sortable-drag'
            });
        }

        function handleDragStart(e) {
            e.dataTransfer.setData('text/plain', e.target.dataset.type);
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.currentTarget.classList.add('drag-over');
        }

        function handleDrop(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('drag-over');

            const elementType = e.dataTransfer.getData('text/plain');
            if (elementType) {
                createElement(elementType);
            }
        }

        function createElement(type) {
            elementCounter++;
            const elementId = `element-${type}-${elementCounter}`;

            let elementHTML = '';

            switch (type) {
                case 'title':
                    elementHTML = `
                        <div class="canvas-element element-title" id="${elementId}" data-type="title">
                            <div class="element-controls">
                                <button class="control-btn" onclick="editElement('${elementId}')"><i class="fas fa-edit"></i></button>
                                <button class="control-btn" onclick="deleteElement('${elementId}')"><i class="fas fa-trash"></i></button>
                            </div>
                            <h1 contenteditable="true">Votre titre ici</h1>
                        </div>
                    `;
                    break;
                case 'text':
                    elementHTML = `
                        <div class="canvas-element element-text" id="${elementId}" data-type="text">
                            <div class="element-controls">
                                <button class="control-btn" onclick="editElement('${elementId}')"><i class="fas fa-edit"></i></button>
                                <button class="control-btn" onclick="deleteElement('${elementId}')"><i class="fas fa-trash"></i></button>
                            </div>
                            <p contenteditable="true">Votre texte ici. Vous pouvez le modifier directement en cliquant dessus.</p>
                        </div>
                    `;
                    break;
                case 'image':
                    elementHTML = `
                        <div class="canvas-element element-image" id="${elementId}" data-type="image">
                            <div class="element-controls">
                                <button class="control-btn" onclick="editElement('${elementId}')"><i class="fas fa-edit"></i></button>
                                <button class="control-btn" onclick="deleteElement('${elementId}')"><i class="fas fa-trash"></i></button>
                            </div>
                            <div class="placeholder" onclick="selectImage('${elementId}')">
                                <i class="fas fa-image fa-3x mb-3"></i>
                                <p>Cliquez pour ajouter une image</p>
                            </div>
                        </div>
                    `;
                    break;
                case 'button':
                    elementHTML = `
                        <div class="canvas-element element-button" id="${elementId}" data-type="button">
                            <div class="element-controls">
                                <button class="control-btn" onclick="editElement('${elementId}')"><i class="fas fa-edit"></i></button>
                                <button class="control-btn" onclick="deleteElement('${elementId}')"><i class="fas fa-trash"></i></button>
                            </div>
                            <button class="btn btn-primary" contenteditable="true">Cliquez ici</button>
                        </div>
                    `;
                    break;
                case 'spacer':
                    elementHTML = `
                        <div class="canvas-element" id="${elementId}" data-type="spacer" style="height: 40px;">
                            <div class="element-controls">
                                <button class="control-btn" onclick="deleteElement('${elementId}')"><i class="fas fa-trash"></i></button>
                            </div>
                        </div>
                    `;
                    break;
            }

            // Insert before the main drop zone
            const mainDropZone = document.getElementById('mainDropZone');
            mainDropZone.insertAdjacentHTML('beforebegin', elementHTML);

            // Add click handler for selection
            const newElement = document.getElementById(elementId);
            newElement.addEventListener('click', () => selectElement(elementId));
        }

        function selectElement(elementId) {
            // Remove previous selection
            if (selectedElement) {
                selectedElement.classList.remove('selected');
            }

            // Select new element
            selectedElement = document.getElementById(elementId);
            selectedElement.classList.add('selected');

            // Show element toolbar
            showElementToolbar(selectedElement.dataset.type);
        }

        function showElementToolbar(type) {
            const toolbar = document.getElementById('elementToolbar');
            const typeSpan = document.getElementById('selectedElementType');

            typeSpan.textContent = `Élément sélectionné: ${type}`;
            toolbar.style.display = 'block';
        }

        function deleteElement(elementId) {
            if (confirm('Supprimer cet élément ?')) {
                document.getElementById(elementId).remove();
                hideElementToolbar();
            }
        }

        function deleteSelectedElement() {
            if (selectedElement) {
                deleteElement(selectedElement.id);
            }
        }

        function hideElementToolbar() {
            document.getElementById('elementToolbar').style.display = 'none';
            if (selectedElement) {
                selectedElement.classList.remove('selected');
                selectedElement = null;
            }
        }

        function toggleSection(sectionId) {
            const content = document.getElementById(sectionId);
            const header = content.previousElementSibling;
            const icon = header.querySelector('.fa-chevron-down, .fa-chevron-up');

            if (content.style.display === 'none') {
                content.style.display = 'block';
                icon.className = 'fas fa-chevron-down';
            } else {
                content.style.display = 'none';
                icon.className = 'fas fa-chevron-up';
            }
        }

        function goBack() {
            console.log('goBack function called');
            try {
                if (confirm('Voulez-vous vraiment quitter ? Les modifications non sauvegardées seront perdues.')) {
                    window.location.href = '?page=landing-page-builder';
                }
            } catch (e) {
                console.error('Error in goBack function:', e);
            }
        }

        function savePage() {
            const pageData = {
                title: document.getElementById('pageTitle').value,
                slug: document.getElementById('pageSlug').value,
                description: document.getElementById('pageDescription').value,
                content: document.getElementById('canvas').innerHTML,
                status: 'draft'
            };

            console.log('Saving page:', pageData);
            alert('Page sauvegardée ! (Fonctionnalité en développement)');
        }

        function publishPage() {
            if (confirm('Publier cette page ?')) {
                savePage();
                alert('Page publiée ! (Fonctionnalité en développement)');
            }
        }

        function previewPage() {
            console.log('previewPage function called');
            try {
                // Get page content
                const pageData = {
                    title: document.getElementById('pageTitle').value || 'Aperçu de la page',
                    content: document.getElementById('canvas').innerHTML
                };

                // Create preview window
                const previewWindow = window.open('', '_blank', 'width=1200,height=800,scrollbars=yes');

                // Generate preview HTML
                const previewHTML = `
                <!DOCTYPE html>
                <html lang="fr">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>${pageData.title}</title>
                    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
                    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
                    <style>
                        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
                        .preview-header {
                            background: #007bff;
                            color: white;
                            padding: 10px 20px;
                            position: fixed;
                            top: 0;
                            left: 0;
                            right: 0;
                            z-index: 1000;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                        }
                        .preview-content {
                            margin-top: 60px;
                            padding: 20px;
                        }
                        .canvas-element {
                            border: none !important;
                            background: transparent !important;
                        }
                        .element-controls { display: none !important; }
                        .drop-zone { display: none !important; }
                        .element-title h1 { margin: 20px 0; }
                        .element-text p { margin: 15px 0; }
                        .element-image { margin: 20px 0; }
                        .element-button { margin: 20px 0; }
                    </style>
                </head>
                <body>
                    <div class="preview-header">
                        <h5 class="mb-0"><i class="fas fa-eye me-2"></i>Aperçu: ${pageData.title}</h5>
                        <button class="btn btn-light btn-sm" onclick="window.close()">
                            <i class="fas fa-times me-1"></i>Fermer
                        </button>
                    </div>
                    <div class="preview-content">
                        ${pageData.content.replace(/contenteditable="true"/g, '')}
                    </div>
                    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"><\/script>
</body>

</html>
`;

                previewWindow.document.write(previewHTML);
                previewWindow.document.close();
            } catch (e) {
                console.error('Error in previewPage function:', e);
                alert('Erreur lors de l\'aperçu de la page');
            }
        }

        function loadPageContent() {
            <?php if ($pageData && !empty($pageData['content'])): ?>
                try {
                    document.getElementById('canvas').innerHTML = <?php echo json_encode($pageData['content'], JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP); ?>;
                } catch (e) {
                    console.error('Error loading page content:', e);
                }
            <?php elseif ($templateData && !empty($templateData['content'])): ?>
                try {
                    document.getElementById('canvas').innerHTML = <?php echo json_encode($templateData['content'], JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP); ?>;
                } catch (e) {
                    console.error('Error loading template content:', e);
                }
            <?php endif; ?>
        }

        // AI Functions (placeholder)
        function generateAITitle() {
            alert('Génération de titre IA (Fonctionnalité en développement)');
        }

        function generateAIContent() {
            alert('Génération de contenu IA (Fonctionnalité en développement)');
        }

        function generateCustomAI() {
            const prompt = document.getElementById('customPrompt').value;
            if (prompt.trim()) {
                alert(`Génération IA pour: "${prompt}" (Fonctionnalité en développement)`);
            }
        }

        function selectImage(elementId) {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const element = document.getElementById(elementId);
                        element.innerHTML = `
<div class="element-controls">
    <button class="control-btn" onclick="editElement('${elementId}')"><i class="fas fa-edit"></i></button>
    <button class="control-btn" onclick="deleteElement('${elementId}')"><i class="fas fa-trash"></i></button>
</div>
<img src="${e.target.result}" alt="Image" onclick="selectImage('${elementId}')">
`;
                    };
                    reader.readAsDataURL(file);
                }
            };
            input.click();
        }

        // Click outside to deselect
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.canvas-element') && !e.target.closest('.element-toolbar')) {
                hideElementToolbar();
            }
        });

        // Contact Form Builder Integration
        function openFormBuilder() {
            if (typeof window.openFormBuilder === 'function') {
                window.openFormBuilder();
            } else {
                console.error('Contact form builder not loaded');
                alert('Le générateur de formulaires n\'est pas disponible. Veuillez recharger la page.');
            }
        }

        // Function to insert form element into page builder
        function insertElementIntoBuilder(elementType, formData) {
            if (elementType === 'contact-form') {
                elementCounter++;
                const elementId = `element-form-${elementCounter}`;

                const elementHTML = `
<div class="canvas-element element-contact-form" id="${elementId}" data-type="contact-form">
    <div class="element-controls">
        <button class="control-btn" onclick="editElement('${elementId}')"><i class="fas fa-edit"></i></button>
        <button class="control-btn" onclick="deleteElement('${elementId}')"><i class="fas fa-trash"></i></button>
    </div>
    <div class="form-container">
        ${formData.html}
    </div>
</div>
`;

                // Insert before the main drop zone
                const mainDropZone = document.getElementById('mainDropZone');
                mainDropZone.insertAdjacentHTML('beforebegin', elementHTML);

                // Add CSS to head
                if (formData.css) {
                    const style = document.createElement('style');
                    style.textContent = formData.css;
                    document.head.appendChild(style);
                }

                // Execute JS if provided
                if (formData.js) {
                    try {
                        eval(formData.js);
                    } catch (error) {
                        console.error('Error executing form JS:', error);
                    }
                }

                // Add click handler for selection
                const newElement = document.getElementById(elementId);
                newElement.addEventListener('click', () => selectElement(elementId));

                console.log('Contact form inserted successfully');
            }
        }

        // Global notification function for forms
        function showNotification(message, type) {
            const alert = document.createElement('div');
            alert.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
            alert.style.top = '20px';
            alert.style.right = '20px';
            alert.style.zIndex = '9999';
            alert.innerHTML = `
${message}
<button type="button" class="btn-close" data-bs-dismiss="alert"></button>
`;

            document.body.appendChild(alert);

            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 5000);
        }

        // Functions are already defined above
        console.log('Page Builder JavaScript fully loaded');
        console.log('Available functions: previewPage, goBack, savePage, loadPageContent');
    </script>
</body>

</html>
