<?php
require_once 'dashboard/includes/database.php';

try {
    $db = connectDB();
    $storeId = 7;
    $page = 'products';
    
    echo "=== Testing products page logic ===\n";
    
    // Test the exact same logic as in store-frontend.php
    if ($page === 'products') {
        echo "Page is 'products', calling getStoreProducts...\n";
        
        // Copy the exact getStoreProducts function from store-frontend.php
        function getStoreProducts($storeId, $filters = [])
        {
            global $db;

            $where = ["p.store_id = ?"];
            $params = [$storeId];

            // Category filter
            if (!empty($filters['category_id'])) {
                $where[] = "pc.category_id = ?";
                $params[] = $filters['category_id'];
            }

            // Search filter
            if (!empty($filters['search'])) {
                $where[] = "(p.name LIKE ? OR p.description LIKE ?)";
                $params[] = '%' . $filters['search'] . '%';
                $params[] = '%' . $filters['search'] . '%';
            }

            // Price range filter
            if (!empty($filters['min_price'])) {
                $where[] = "p.price >= ?";
                $params[] = $filters['min_price'];
            }

            if (!empty($filters['max_price'])) {
                $where[] = "p.price <= ?";
                $params[] = $filters['max_price'];
            }

            // Build query (removed product_reviews table that doesn't exist)
            $query = "
                SELECT DISTINCT p.*,
                       GROUP_CONCAT(DISTINCT c.name) as categories
                FROM products p
                LEFT JOIN product_categories pc ON p.id = pc.product_id
                LEFT JOIN categories c ON pc.category_id = c.id
                WHERE " . implode(' AND ', $where) . " AND p.status = 'active'
                GROUP BY p.id
            ";

            // Add sorting
            $sortBy = $filters['sort'] ?? 'newest';
            switch ($sortBy) {
                case 'name':
                    $query .= " ORDER BY p.name ASC";
                    break;
                case 'price_asc':
                    $query .= " ORDER BY p.price ASC";
                    break;
                case 'price_desc':
                    $query .= " ORDER BY p.price DESC";
                    break;
                case 'popular':
                    $query .= " ORDER BY p.created_at DESC"; // Fallback since review_count doesn't exist
                    break;
                case 'newest':
                default:
                    $query .= " ORDER BY p.created_at DESC";
                    break;
            }

            // Add pagination
            $page = (int)($filters['page'] ?? 1);
            $perPage = (int)($filters['per_page'] ?? 24);
            $offset = ($page - 1) * $perPage;
            $query .= " LIMIT ? OFFSET ?";
            $params[] = $perPage;
            $params[] = $offset;

            echo "Final query: $query\n";
            echo "Params: " . print_r($params, true) . "\n";

            $stmt = $db->prepare($query);
            $stmt->execute($params);

            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
        
        $products = getStoreProducts($storeId, $_GET);
        echo "Products returned: " . count($products) . "\n";
        
        if (count($products) > 0) {
            echo "First product name: " . $products[0]['name'] . "\n";
            echo "First product price: " . $products[0]['price'] . "\n";
        }
    } else {
        echo "Page is not 'products'\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
