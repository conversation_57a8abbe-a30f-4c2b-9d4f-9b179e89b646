<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Commande Express - Sans Inscription</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .checkout-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .checkout-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 0;
            margin-bottom: 30px;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        
        .step {
            display: flex;
            align-items: center;
            margin: 0 15px;
        }
        
        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .step.active .step-number {
            background: #007bff;
            color: white;
        }
        
        .step.completed .step-number {
            background: #28a745;
            color: white;
        }
        
        .guest-option {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .guest-option:hover {
            border-color: #007bff;
            background: #e3f2fd;
        }
        
        .guest-option.selected {
            border-color: #007bff;
            background: #e3f2fd;
        }
        
        .order-summary {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            position: sticky;
            top: 20px;
        }
        
        .form-section {
            background: white;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .account-creation-option {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .tracking-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="checkout-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1><i class="fas fa-shopping-cart me-2"></i>Commande Express</h1>
                    <p class="mb-0">Commandez rapidement sans créer de compte</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="/login.html" class="btn btn-outline-light">
                        <i class="fas fa-user me-1"></i>J'ai déjà un compte
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="checkout-container">
        <!-- Step Indicator -->
        <div class="step-indicator">
            <div class="step active" id="step1">
                <div class="step-number">1</div>
                <span>Informations</span>
            </div>
            <div class="step" id="step2">
                <div class="step-number">2</div>
                <span>Livraison</span>
            </div>
            <div class="step" id="step3">
                <div class="step-number">3</div>
                <span>Paiement</span>
            </div>
            <div class="step" id="step4">
                <div class="step-number">4</div>
                <span>Confirmation</span>
            </div>
        </div>

        <div class="row">
            <!-- Main Form -->
            <div class="col-lg-8">
                <!-- Checkout Options -->
                <div class="form-section">
                    <h3><i class="fas fa-user-check me-2"></i>Comment souhaitez-vous commander ?</h3>
                    
                    <div class="guest-option selected" onclick="selectCheckoutType('guest')">
                        <div class="d-flex align-items-center">
                            <input type="radio" name="checkoutType" value="guest" checked class="me-3">
                            <div>
                                <h5 class="mb-1"><i class="fas fa-bolt me-2 text-warning"></i>Commande Express (Invité)</h5>
                                <p class="mb-0 text-muted">Commandez rapidement sans créer de compte. Idéal pour un achat ponctuel.</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="guest-option" onclick="selectCheckoutType('register')">
                        <div class="d-flex align-items-center">
                            <input type="radio" name="checkoutType" value="register" class="me-3">
                            <div>
                                <h5 class="mb-1"><i class="fas fa-user-plus me-2 text-success"></i>Créer un compte</h5>
                                <p class="mb-0 text-muted">Créez un compte pour suivre vos commandes et bénéficier d'avantages exclusifs.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Customer Information Form -->
                <div class="form-section">
                    <h4><i class="fas fa-user me-2"></i>Vos informations</h4>
                    
                    <form id="checkoutForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="firstName" class="form-label">Prénom *</label>
                                    <input type="text" class="form-control" id="firstName" name="firstName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="lastName" class="form-label">Nom *</label>
                                    <input type="text" class="form-control" id="lastName" name="lastName" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email *</label>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                    <div class="form-text">Nous vous enverrons la confirmation de commande</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">Téléphone *</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" required>
                                    <div class="form-text">Pour vous contacter concernant la livraison</div>
                                </div>
                            </div>
                        </div>

                        <!-- Account Creation Option (shown for guest checkout) -->
                        <div id="accountCreationOption" class="account-creation-option">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="createAccount" name="createAccount">
                                <label class="form-check-label" for="createAccount">
                                    <strong>Créer un compte pour cette commande</strong>
                                </label>
                            </div>
                            <p class="mb-2 mt-2 text-muted small">
                                <i class="fas fa-check-circle text-success me-1"></i>Suivez vos commandes en temps réel<br>
                                <i class="fas fa-check-circle text-success me-1"></i>Accédez à votre historique d'achats<br>
                                <i class="fas fa-check-circle text-success me-1"></i>Contactez directement les vendeurs<br>
                                <i class="fas fa-check-circle text-success me-1"></i>Bénéficiez d'offres exclusives
                            </p>
                            
                            <div id="passwordFields" style="display: none;">
                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <label for="password" class="form-label">Mot de passe</label>
                                        <input type="password" class="form-control" id="password" name="password">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="confirmPassword" class="form-label">Confirmer le mot de passe</label>
                                        <input type="password" class="form-control" id="confirmPassword" name="confirmPassword">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Shipping Address -->
                        <h5 class="mt-4"><i class="fas fa-truck me-2"></i>Adresse de livraison</h5>
                        
                        <div class="mb-3">
                            <label for="address" class="form-label">Adresse complète *</label>
                            <textarea class="form-control" id="address" name="address" rows="2" required 
                                placeholder="Numéro, rue, quartier..."></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="city" class="form-label">Ville *</label>
                                    <select class="form-select" id="city" name="city" required>
                                        <option value="">Sélectionnez une ville</option>
                                        <option value="Alger">Alger</option>
                                        <option value="Oran">Oran</option>
                                        <option value="Constantine">Constantine</option>
                                        <option value="Annaba">Annaba</option>
                                        <option value="Blida">Blida</option>
                                        <option value="Batna">Batna</option>
                                        <option value="Djelfa">Djelfa</option>
                                        <option value="Sétif">Sétif</option>
                                        <option value="Sidi Bel Abbès">Sidi Bel Abbès</option>
                                        <option value="Biskra">Biskra</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="postalCode" class="form-label">Code postal</label>
                                    <input type="text" class="form-control" id="postalCode" name="postalCode">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="country" class="form-label">Pays</label>
                                    <input type="text" class="form-control" id="country" name="country" value="Algérie" readonly>
                                </div>
                            </div>
                        </div>

                        <!-- Order Notes -->
                        <div class="mb-3">
                            <label for="orderNotes" class="form-label">Notes de commande (optionnel)</label>
                            <textarea class="form-control" id="orderNotes" name="orderNotes" rows="2" 
                                placeholder="Instructions spéciales pour la livraison..."></textarea>
                        </div>

                        <!-- Tracking Information -->
                        <div class="tracking-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Suivi de votre commande</h6>
                            <p class="mb-2">
                                <strong>Pour les commandes sans compte :</strong><br>
                                • Vous recevrez un email de confirmation avec un lien de suivi<br>
                                • Conservez votre numéro de commande pour le suivi<br>
                                • Vous pourrez créer un compte plus tard pour un suivi complet
                            </p>
                        </div>

                        <div class="d-flex justify-content-between mt-4">
                            <button type="button" class="btn btn-outline-secondary" onclick="goBack()">
                                <i class="fas fa-arrow-left me-1"></i>Retour
                            </button>
                            <button type="button" class="btn btn-primary" onclick="proceedToShipping()">
                                Continuer vers la livraison
                                <i class="fas fa-arrow-right ms-1"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Order Summary -->
            <div class="col-lg-4">
                <div class="order-summary">
                    <h4><i class="fas fa-receipt me-2"></i>Résumé de la commande</h4>
                    
                    <div id="orderItems">
                        <!-- Order items will be populated by JavaScript -->
                        <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                            <div>
                                <h6 class="mb-0">Produit exemple</h6>
                                <small class="text-muted">Quantité: 1</small>
                            </div>
                            <span class="fw-bold">2500 DZD</span>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <div class="d-flex justify-content-between">
                            <span>Sous-total:</span>
                            <span id="subtotal">2500 DZD</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Livraison:</span>
                            <span id="shipping">À calculer</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between fw-bold">
                            <span>Total:</span>
                            <span id="total">À calculer</span>
                        </div>
                    </div>
                    
                    <div class="mt-3 p-3 bg-light rounded">
                        <small class="text-muted">
                            <i class="fas fa-shield-alt me-1"></i>
                            Paiement sécurisé • Livraison garantie
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let checkoutType = 'guest';
        
        function selectCheckoutType(type) {
            checkoutType = type;
            
            // Update radio buttons
            document.querySelectorAll('input[name="checkoutType"]').forEach(radio => {
                radio.checked = radio.value === type;
            });
            
            // Update visual selection
            document.querySelectorAll('.guest-option').forEach(option => {
                option.classList.remove('selected');
            });
            event.currentTarget.classList.add('selected');
            
            // Show/hide account creation option
            const accountOption = document.getElementById('accountCreationOption');
            if (type === 'guest') {
                accountOption.style.display = 'block';
            } else {
                accountOption.style.display = 'none';
            }
        }
        
        // Handle create account checkbox
        document.getElementById('createAccount').addEventListener('change', function() {
            const passwordFields = document.getElementById('passwordFields');
            const passwordInput = document.getElementById('password');
            const confirmPasswordInput = document.getElementById('confirmPassword');
            
            if (this.checked) {
                passwordFields.style.display = 'block';
                passwordInput.required = true;
                confirmPasswordInput.required = true;
            } else {
                passwordFields.style.display = 'none';
                passwordInput.required = false;
                confirmPasswordInput.required = false;
            }
        });
        
        function proceedToShipping() {
            // Validate form
            const form = document.getElementById('checkoutForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }
            
            // Check password confirmation if account creation is selected
            const createAccount = document.getElementById('createAccount').checked;
            if (createAccount) {
                const password = document.getElementById('password').value;
                const confirmPassword = document.getElementById('confirmPassword').value;
                
                if (password !== confirmPassword) {
                    alert('Les mots de passe ne correspondent pas');
                    return;
                }
            }
            
            // Store form data
            const formData = new FormData(form);
            sessionStorage.setItem('checkoutData', JSON.stringify(Object.fromEntries(formData)));
            sessionStorage.setItem('checkoutType', checkoutType);
            
            // Proceed to shipping
            window.location.href = '/templates/checkout/shipping-options.html';
        }
        
        function goBack() {
            history.back();
        }
        
        // Load order data from session storage
        document.addEventListener('DOMContentLoaded', function() {
            // Load cart data if available
            const cartData = sessionStorage.getItem('cartData');
            if (cartData) {
                const cart = JSON.parse(cartData);
                // Update order summary with cart data
                updateOrderSummary(cart);
            }
        });
        
        function updateOrderSummary(cart) {
            // Implementation to update order summary
            // This would be populated with actual cart data
        }
    </script>
</body>
</html>
