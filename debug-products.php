<?php
require_once 'dashboard/includes/database.php';

try {
    $db = connectDB();
    
    echo "=== Checking database structure ===\n";
    
    // Check if products table exists and its structure
    $result = $db->query("SHOW TABLES LIKE 'products'");
    if ($result->rowCount() > 0) {
        echo "✅ Products table exists\n";
        $columns = $db->query("DESCRIBE products")->fetchAll(PDO::FETCH_ASSOC);
        echo "Products table columns:\n";
        foreach ($columns as $col) {
            echo "  - {$col['Field']} ({$col['Type']})\n";
        }
    } else {
        echo "❌ Products table does not exist\n";
    }
    
    echo "\n=== Checking store_id=7 ===\n";
    $storeCheck = $db->prepare("SELECT * FROM stores WHERE id = 7");
    $storeCheck->execute();
    $store = $storeCheck->fetch(PDO::FETCH_ASSOC);
    if ($store) {
        echo "✅ Store 7 exists: {$store['store_name']}\n";
        echo "   Merchant ID: {$store['merchant_id']}\n";
    } else {
        echo "❌ Store 7 does not exist\n";
    }
    
    echo "\n=== Checking products for store_id=7 ===\n";
    $productCheck = $db->prepare("SELECT COUNT(*) as count FROM products WHERE store_id = 7");
    $productCheck->execute();
    $count = $productCheck->fetch(PDO::FETCH_ASSOC);
    echo "Products count for store 7: {$count['count']}\n";
    
    if ($count['count'] > 0) {
        $products = $db->prepare("SELECT id, name, price, status FROM products WHERE store_id = 7 LIMIT 5");
        $products->execute();
        $productList = $products->fetchAll(PDO::FETCH_ASSOC);
        echo "Sample products:\n";
        foreach ($productList as $product) {
            echo "  - ID: {$product['id']}, Name: {$product['name']}, Price: {$product['price']}, Status: {$product['status']}\n";
        }
    }
    
    echo "\n=== Testing getStoreProducts function ===\n";
    
    // Test the getStoreProducts function
    function getStoreProducts($storeId, $filters = [])
    {
        global $db;

        $where = ["p.store_id = ?"];
        $params = [$storeId];

        // Category filter
        if (!empty($filters['category_id'])) {
            $where[] = "pc.category_id = ?";
            $params[] = $filters['category_id'];
        }

        // Search filter
        if (!empty($filters['search'])) {
            $where[] = "(p.name LIKE ? OR p.description LIKE ?)";
            $params[] = '%' . $filters['search'] . '%';
            $params[] = '%' . $filters['search'] . '%';
        }

        // Price range filter
        if (!empty($filters['min_price'])) {
            $where[] = "p.price >= ?";
            $params[] = $filters['min_price'];
        }

        if (!empty($filters['max_price'])) {
            $where[] = "p.price <= ?";
            $params[] = $filters['max_price'];
        }

        // Build query (removed product_reviews table that doesn't exist)
        $query = "
            SELECT DISTINCT p.*,
                   GROUP_CONCAT(DISTINCT c.name) as categories
            FROM products p
            LEFT JOIN product_categories pc ON p.id = pc.product_id
            LEFT JOIN categories c ON pc.category_id = c.id
            WHERE " . implode(' AND ', $where) . " AND p.status = 'active'
            GROUP BY p.id
        ";

        // Add sorting
        $sortBy = $filters['sort'] ?? 'newest';
        switch ($sortBy) {
            case 'name':
                $query .= " ORDER BY p.name ASC";
                break;
            case 'price_asc':
                $query .= " ORDER BY p.price ASC";
                break;
            case 'price_desc':
                $query .= " ORDER BY p.price DESC";
                break;
            case 'popular':
                $query .= " ORDER BY p.created_at DESC"; // Fallback since review_count doesn't exist
                break;
            case 'newest':
            default:
                $query .= " ORDER BY p.created_at DESC";
                break;
        }

        // Add pagination
        $page = $filters['page'] ?? 1;
        $perPage = $filters['per_page'] ?? 24;
        $offset = ($page - 1) * $perPage;
        $query .= " LIMIT ? OFFSET ?";
        $params[] = $perPage;
        $params[] = $offset;

        echo "Query: $query\n";
        echo "Params: " . implode(', ', $params) . "\n";

        $stmt = $db->prepare($query);
        $stmt->execute($params);

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    $products = getStoreProducts(7, $_GET);
    echo "Products returned: " . count($products) . "\n";
    
    if (count($products) > 0) {
        echo "First product: " . print_r($products[0], true) . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
