<?php
echo "<!-- DEBUG: Starting test -->";

require_once 'dashboard/includes/database.php';

echo "<!-- DEBUG: Database included -->";

// Test the exact same logic as store-frontend.php
$storeIdentifier = 'medical-software-solutions';
$page = 'products';

echo "<!-- DEBUG: Variables set -->";

try {
    $db = connectDB();
    echo "<!-- DEBUG: Database connected -->";
    
    // Get store information
    $storeId = 7; // Store <NAME_EMAIL>
    
    $storeQuery = "
        SELECT s.*,
               COUNT(DISTINCT p.id) as product_count,
               COUNT(DISTINCT c.id) as category_count,
               AVG(sr.rating) as average_rating,
               COUNT(DISTINCT sr.id) as total_ratings
        FROM stores s
        LEFT JOIN products p ON s.id = p.store_id AND p.status = 'active'
        LEFT JOIN categories c ON s.id = c.store_id AND c.status = 'active'
        LEFT JOIN seller_ratings sr ON s.merchant_id = sr.seller_id
        WHERE s.id = ? AND s.status = 'active'
        GROUP BY s.id
    ";

    $stmt = $db->prepare($storeQuery);
    $stmt->execute([$storeId]);
    $store = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<!-- DEBUG: Store query executed -->";
    
    if (!$store) {
        echo "<!-- DEBUG: Store not found -->";
        exit;
    }
    
    echo "<!-- DEBUG: Store found: {$store['store_name']} -->";
    
    // Initialize products array
    $products = [];
    
    // Load products for products page
    if ($page === 'products') {
        echo "<!-- DEBUG: Loading products -->";
        
        // Simple products query
        $productsQuery = "SELECT * FROM products WHERE store_id = ? AND status = 'active' LIMIT 5";
        $productsStmt = $db->prepare($productsQuery);
        $productsStmt->execute([$storeId]);
        $products = $productsStmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<!-- DEBUG: Products loaded: " . count($products) . " -->";
    }
    
    echo "<!-- DEBUG: About to display products -->";
    
    echo "<h1>Test Results</h1>";
    echo "<p>Store: " . htmlspecialchars($store['store_name']) . "</p>";
    echo "<p>Products count: " . count($products) . "</p>";
    
    if (count($products) > 0) {
        echo "<h2>Products:</h2>";
        foreach ($products as $product) {
            echo "<div>";
            echo "<h3>" . htmlspecialchars($product['name']) . "</h3>";
            echo "<p>Price: " . $product['price'] . " DZD</p>";
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<!-- DEBUG: Error: " . $e->getMessage() . " -->";
    echo "<h1>Error: " . htmlspecialchars($e->getMessage()) . "</h1>";
}
?>
