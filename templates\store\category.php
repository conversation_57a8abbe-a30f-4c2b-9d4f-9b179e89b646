<!DOCTYPE html>
<html lang="fr" dir="ltr">
<?php
// Définir le slug pour ce store (medical-software-solutions)
$store['slug'] = 'medical-software-solutions';

// Get category from URL
$categorySlug = $_GET['category'] ?? '';
$categoryName = urldecode($categorySlug);

// Get category details and products
$categoryProducts = [];
$categoryInfo = null;

if ($categorySlug) {
    // Get merchant_id for this store
    $merchantQuery = "SELECT merchant_id FROM stores WHERE id = ?";
    $merchantStmt = $db->prepare($merchantQuery);
    $merchantStmt->execute([$store['id']]);
    $storeData = $merchantStmt->fetch(PDO::FETCH_ASSOC);
    $merchantId = $storeData['merchant_id'];

    // Get category info
    $categoryQuery = "SELECT * FROM categories WHERE name = ? AND merchant_id = ? AND status = 'active'";
    $categoryStmt = $db->prepare($categoryQuery);
    $categoryStmt->execute([$categoryName, $merchantId]);
    $categoryInfo = $categoryStmt->fetch(PDO::FETCH_ASSOC);

    if ($categoryInfo) {
        // Get products in this category
        $productsQuery = "
            SELECT DISTINCT p.*
            FROM products p
            JOIN product_categories pc ON p.id = pc.product_id
            WHERE pc.category_id = ? AND p.status = 'active'
            ORDER BY p.created_at DESC
        ";
        $productsStmt = $db->prepare($productsQuery);
        $productsStmt->execute([$categoryInfo['id']]);
        $categoryProducts = $productsStmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
?>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($categoryName); ?> - <?php echo htmlspecialchars($store['store_name']); ?></title>
    <meta name="description" content="Découvrez nos produits dans la catégorie <?php echo htmlspecialchars($categoryName); ?> - <?php echo htmlspecialchars($store['description'] ?? ''); ?>">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        :root {
            --primary-color: <?php echo $customization['primary_color'] ?? '#007bff'; ?>;
            --secondary-color: <?php echo $customization['secondary_color'] ?? '#6c757d'; ?>;
        }

        .product-card {
            transition: transform 0.2s, box-shadow 0.2s;
            height: 100%;
            border: 1px solid #e9ecef;
        }

        .product-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
            border-color: var(--primary-color);
        }

        .product-image {
            height: 180px;
            object-fit: cover;
            background: #f8f9fa;
        }

        .product-card .card-body {
            padding: 1rem;
        }

        .product-card .card-title {
            font-size: 0.95rem;
            font-weight: 600;
            line-height: 1.3;
            height: 2.6rem;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        .category-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 3rem 0;
            margin-bottom: 2rem;
        }

        .breadcrumb {
            background: none;
            padding: 0;
        }

        .breadcrumb-item+.breadcrumb-item::before {
            color: #6c757d;
        }
    </style>
</head>

<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold" href="/store-frontend.php?store=<?php echo $store['slug']; ?>">
                <?php echo htmlspecialchars($store['store_name']); ?>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <?php
                    $currentPage = $_GET['page'] ?? 'category';
                    $storeSlug = 'medical-software-solutions';
                    $storeSettings = json_decode($store['settings'] ?? '{}', true);
                    $pageSettings = $storeSettings['pages'] ?? ['home' => true, 'products' => true, 'about' => true, 'contact' => true];
                    ?>

                    <?php if ($pageSettings['home'] !== false): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="/store-frontend.php?store=<?php echo $storeSlug; ?>">
                                <i class="fas fa-home me-1"></i>Accueil
                            </a>
                        </li>
                    <?php endif; ?>

                    <?php if ($pageSettings['products'] !== false): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="/store-frontend.php?store=<?php echo $storeSlug; ?>&page=products">
                                <i class="fas fa-box me-1"></i>Produits
                            </a>
                        </li>
                    <?php endif; ?>

                    <?php if ($pageSettings['about'] !== false): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="/store-frontend.php?store=<?php echo $storeSlug; ?>&page=about">
                                <i class="fas fa-info-circle me-1"></i>À propos
                            </a>
                        </li>
                    <?php endif; ?>

                    <?php if ($pageSettings['contact'] !== false): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="/store-frontend.php?store=<?php echo $storeSlug; ?>&page=contact">
                                <i class="fas fa-envelope me-1"></i>Contact
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>

                <!-- Search Bar -->
                <div class="d-flex me-3">
                    <form class="d-flex" action="/store-frontend.php" method="GET">
                        <input type="hidden" name="store" value="medical-software-solutions">
                        <input type="hidden" name="page" value="search">
                        <input class="form-control search-bar" type="search" name="q" placeholder="Rechercher des produits...">
                        <button class="btn btn-primary search-btn" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>

                <!-- Cart -->
                <div class="d-flex align-items-center">
                    <button class="btn btn-outline-primary position-relative me-3" onclick="toggleCart()">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="cartCount">
                            0
                        </span>
                    </button>
                    <a href="/login.html" class="btn btn-primary">Connexion</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Category Header -->
    <div class="category-header">
        <div class="container">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb text-white-50 mb-3">
                    <li class="breadcrumb-item">
                        <a href="/store-frontend.php?store=medical-software-solutions" class="text-white text-decoration-none">Accueil</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="/store-frontend.php?store=medical-software-solutions&page=products" class="text-white text-decoration-none">Produits</a>
                    </li>
                    <li class="breadcrumb-item active text-white" aria-current="page">
                        <?php echo htmlspecialchars($categoryName); ?>
                    </li>
                </ol>
            </nav>

            <h1 class="display-5 fw-bold mb-2"><?php echo htmlspecialchars($categoryName); ?></h1>
            <p class="lead mb-0">
                <?php echo count($categoryProducts); ?> produits dans cette catégorie
            </p>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container my-5">
        <?php if (empty($categoryProducts)): ?>
            <!-- No Products Found -->
            <div class="text-center py-5">
                <i class="fas fa-box-open fa-4x text-muted mb-4"></i>
                <h3 class="text-muted">Aucun produit trouvé</h3>
                <p class="text-muted mb-4">Il n'y a actuellement aucun produit dans cette catégorie.</p>
                <a href="/store-frontend.php?store=medical-software-solutions&page=products" class="btn btn-primary">
                    <i class="fas fa-arrow-left me-2"></i>Voir tous les produits
                </a>
            </div>
        <?php else: ?>
            <!-- Products Grid -->
            <div class="row">
                <?php foreach ($categoryProducts as $product): ?>
                    <div class="col-md-4 col-lg-3 mb-4">
                        <div class="card product-card h-100">
                            <div class="position-relative">
                                <?php
                                // Handle both image_url and images fields
                                $imageUrl = '';
                                if (!empty($product['image_url'])) {
                                    $imageUrl = $product['image_url'];
                                } elseif (!empty($product['images'])) {
                                    // If images is JSON, decode and get first image
                                    $images = json_decode($product['images'], true);
                                    if (is_array($images) && !empty($images)) {
                                        $imageUrl = $images[0];
                                    } elseif (is_string($product['images'])) {
                                        $imageUrl = $product['images'];
                                    }
                                }
                                ?>
                                <?php if (!empty($imageUrl)): ?>
                                    <img src="<?php echo htmlspecialchars($imageUrl); ?>"
                                        class="card-img-top product-image" alt="<?php echo htmlspecialchars($product['name']); ?>">
                                <?php else: ?>
                                    <div class="card-img-top product-image d-flex align-items-center justify-content-center">
                                        <i class="fas fa-image fa-3x text-muted"></i>
                                    </div>
                                <?php endif; ?>

                                <div class="position-absolute top-0 end-0 m-2">
                                    <button class="btn btn-sm btn-light rounded-circle" onclick="toggleWishlist(<?php echo $product['id']; ?>)">
                                        <i class="far fa-heart"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="card-body d-flex flex-column">
                                <!-- Compact Product Info -->
                                <h6 class="card-title mb-2"><?php echo htmlspecialchars($product['name']); ?></h6>

                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span class="h6 text-primary mb-0"><?php echo number_format($product['price'], 2); ?> DZD</span>
                                </div>

                                <!-- Compact Action Buttons -->
                                <div class="d-flex gap-2">
                                    <button class="btn btn-outline-primary btn-sm flex-grow-1"
                                        onclick="showProductDetails(<?php echo $product['id']; ?>)">
                                        <i class="fas fa-eye me-1"></i>Voir détail
                                    </button>
                                    <button class="btn btn-primary btn-sm"
                                        onclick="addToCart(<?php echo $product['id']; ?>)"
                                        title="Ajouter au panier">
                                        <i class="fas fa-shopping-cart"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-light py-5 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5><?php echo htmlspecialchars($store['store_name']); ?></h5>
                    <p><?php echo htmlspecialchars($store['description'] ?? ''); ?></p>
                </div>
                <div class="col-md-4">
                    <h6>Liens rapides</h6>
                    <ul class="list-unstyled">
                        <?php if ($pageSettings['home'] !== false): ?>
                            <li><a href="/store-frontend.php?store=medical-software-solutions" class="text-light text-decoration-none">Accueil</a></li>
                        <?php endif; ?>
                        <?php if ($pageSettings['products'] !== false): ?>
                            <li><a href="/store-frontend.php?store=medical-software-solutions&page=products" class="text-light text-decoration-none">Produits</a></li>
                        <?php endif; ?>
                        <?php if ($pageSettings['about'] !== false): ?>
                            <li><a href="/store-frontend.php?store=medical-software-solutions&page=about" class="text-light text-decoration-none">À propos</a></li>
                        <?php endif; ?>
                        <?php if ($pageSettings['contact'] !== false): ?>
                            <li><a href="/store-frontend.php?store=medical-software-solutions&page=contact" class="text-light text-decoration-none">Contact</a></li>
                        <?php endif; ?>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h6>Contact</h6>
                    <p class="mb-1"><i class="fas fa-envelope me-2"></i><EMAIL></p>
                    <p class="mb-1"><i class="fas fa-phone me-2"></i>+213 XX XX XX XX</p>
                    <p><i class="fas fa-map-marker-alt me-2"></i>Alger, Algérie</p>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p class="mb-0">&copy; 2025 <?php echo htmlspecialchars($store['store_name']); ?>. Tous droits réservés.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Shopping cart functionality
        let cart = JSON.parse(localStorage.getItem('cart_<?php echo $store['id']; ?>') || '[]');

        function updateCartCount() {
            document.getElementById('cartCount').textContent = cart.length;
        }

        function addToCart(productId) {
            cart.push({
                id: productId,
                quantity: 1,
                store_id: <?php echo $store['id']; ?>
            });

            localStorage.setItem('cart_<?php echo $store['id']; ?>', JSON.stringify(cart));
            updateCartCount();

            alert('Produit ajouté au panier!');
        }

        function toggleWishlist(productId) {
            console.log('Toggle wishlist:', productId);
        }

        function toggleCart() {
            console.log('Toggle cart');
        }

        // Product details functionality
        let currentProductId = null;
        let productDetailsModal = null;

        function showProductDetails(productId) {
            currentProductId = productId;

            // Initialize modal if not already done
            if (!productDetailsModal) {
                const modalElement = document.getElementById('productDetailsModal');
                if (modalElement) {
                    productDetailsModal = new bootstrap.Modal(modalElement);
                } else {
                    console.error('Modal element not found');
                    return;
                }
            }

            // Show modal with loading state
            productDetailsModal.show();

            // Load product details
            loadProductDetails(productId);
        }

        async function loadProductDetails(productId) {
            try {
                // Find product in current category products array
                const categoryProducts = <?php echo json_encode($categoryProducts ?? []); ?>;
                const product = categoryProducts.find(p => p.id == productId);

                if (!product) {
                    throw new Error('Produit non trouvé');
                }

                // Handle image URL
                let imageUrl = '';
                if (product.image_url) {
                    imageUrl = product.image_url;
                } else if (product.images) {
                    const images = typeof product.images === 'string' ? JSON.parse(product.images) : product.images;
                    if (Array.isArray(images) && images.length > 0) {
                        imageUrl = images[0];
                    } else if (typeof product.images === 'string') {
                        imageUrl = product.images;
                    }
                }

                // Build product details HTML
                const detailsHtml = `
                    <div class="row">
                        <div class="col-md-6">
                            ${imageUrl ?
                                `<img src="${imageUrl}" class="img-fluid rounded" alt="${product.name}">` :
                                `<div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 300px;">
                                    <i class="fas fa-image fa-4x text-muted"></i>
                                </div>`
                            }
                        </div>
                        <div class="col-md-6">
                            <h4>${product.name}</h4>
                            <p class="text-muted"><?php echo htmlspecialchars($categoryName); ?></p>
                            <h5 class="text-primary">${parseFloat(product.price).toLocaleString('fr-FR', {minimumFractionDigits: 2})} DZD</h5>

                            <div class="mt-3">
                                <h6>Description:</h6>
                                <div class="description-content">
                                    ${product.description || 'Aucune description disponible'}
                                </div>
                            </div>

                            ${product.sku ? `<p class="mt-3"><strong>SKU:</strong> ${product.sku}</p>` : ''}
                            ${product.stock ? `<p><strong>Stock:</strong> ${product.stock} unités disponibles</p>` : ''}
                        </div>
                    </div>
                `;

                document.getElementById('productDetailsContent').innerHTML = detailsHtml;

                // Update add to cart button in modal
                document.getElementById('addToCartFromModal').onclick = () => {
                    addToCart(productId);
                    productDetailsModal.hide();
                };

            } catch (error) {
                console.error('Error loading product details:', error);
                document.getElementById('productDetailsContent').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Erreur lors du chargement des détails du produit: ${error.message}
                    </div>
                `;
            }
        }

        // Initialize cart count on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateCartCount();
        });
    </script>

    <!-- Product Details Modal -->
    <div class="modal fade" id="productDetailsModal" tabindex="-1" aria-labelledby="productDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="productDetailsModalLabel">Détails du produit</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="productDetailsContent">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                    <button type="button" class="btn btn-primary" id="addToCartFromModal">
                        <i class="fas fa-shopping-cart me-1"></i>Ajouter au panier
                    </button>
                </div>
            </div>
        </div>
    </div>

</body>

</html>
