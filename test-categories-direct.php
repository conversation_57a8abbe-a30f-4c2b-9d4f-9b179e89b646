<!DOCTYPE html>
<html>
<head>
    <title>Test Categories Direct</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>Direct Categories Test</h2>
        
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <h5>Catégories</h5>
                        <h3 id="totalCategories">0</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <h5>Produits</h5>
                        <h3 id="totalProducts">0</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <h5>Plus populaire</h5>
                        <h3 id="mostPopular">-</h3>
                    </div>
                </div>
            </div>
        </div>
        
        <div id="categoriesList">Loading...</div>
        <div id="debugInfo"></div>
    </div>

    <script>
        console.log('🔧 Test script loaded');
        
        async function testCategoriesAPI() {
            try {
                console.log('🚀 Testing categories API...');
                
                const apiUrl = '/api/categories.php?action=user-categories&store_id=7';
                console.log('📡 API URL:', apiUrl);
                
                const response = await fetch(apiUrl);
                console.log('📡 Response status:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                console.log('📦 API Data:', data);
                
                if (data.success) {
                    // Update statistics
                    if (data.stats) {
                        document.getElementById('totalCategories').textContent = data.stats.total_categories || 0;
                        document.getElementById('totalProducts').textContent = data.stats.total_products || 0;
                        document.getElementById('mostPopular').textContent = data.stats.most_popular || '-';
                        
                        console.log('✅ Stats updated:', data.stats);
                    }
                    
                    // Display categories
                    const categoriesList = document.getElementById('categoriesList');
                    if (data.categories && data.categories.length > 0) {
                        let html = '<h4>Categories:</h4><ul>';
                        data.categories.forEach(category => {
                            html += `<li>${category.name} (${category.products_count} products)</li>`;
                        });
                        html += '</ul>';
                        categoriesList.innerHTML = html;
                    } else {
                        categoriesList.innerHTML = '<p>No categories found</p>';
                    }
                    
                    // Debug info
                    document.getElementById('debugInfo').innerHTML = `
                        <h4>Debug Info:</h4>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                    
                } else {
                    throw new Error(data.error || 'API returned success: false');
                }
                
            } catch (error) {
                console.error('❌ Error:', error);
                document.getElementById('categoriesList').innerHTML = `<div class="alert alert-danger">Error: ${error.message}</div>`;
            }
        }
        
        // Test immediately and on DOM ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', testCategoriesAPI);
        } else {
            testCategoriesAPI();
        }
    </script>
</body>
</html>
