<?php
// Customer Dashboard - Simple customer interface
global $db;
if (!isset($db)) {
    $db = connectDB();
}

// Get current user info
$currentUser = getCurrentUser();
$customerId = $currentUser['id'];

// Customer data - use real user data when available
$customerData = [
    'name' => $currentUser['display_name'] ?? $currentUser['first_name'] . ' ' . $currentUser['last_name'] ?? 'Client Demo',
    'email' => $currentUser['email'],
    'phone' => $currentUser['phone'] ?? '+213 555 123 456',
    'address' => $currentUser['address'] ?? '123 Rue de la Liberté, Alger',
    'member_since' => $currentUser['created_at'] ?? '2024-01-15',
    'total_orders' => rand(5, 25), // TODO: Get from database
    'total_spent' => rand(5000, 50000), // TODO: Get from database
    'loyalty_points' => rand(100, 1000) // TODO: Get from database
];

// Mock recent orders
$recentOrders = [
    [
        'id' => 'ORD-001',
        'date' => '2024-01-20',
        'total' => 2500,
        'status' => 'delivered',
        'items' => 3
    ],
    [
        'id' => 'ORD-002',
        'date' => '2024-01-18',
        'total' => 1800,
        'status' => 'shipped',
        'items' => 2
    ],
    [
        'id' => 'ORD-003',
        'date' => '2024-01-15',
        'total' => 3200,
        'status' => 'processing',
        'items' => 4
    ]
];
?>

<div class="customer-dashboard">
    <!-- Page Header -->
    <div class="page-header mb-4">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1><i class="fas fa-user me-2"></i>Mon Compte</h1>
                <p class="text-muted">Bienvenue, <?php echo htmlspecialchars($customerData['name']); ?></p>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-primary" onclick="editProfile()">
                    <i class="fas fa-edit me-2"></i>Modifier le profil
                </button>
                <button class="btn btn-primary" onclick="browseProducts()">
                    <i class="fas fa-shopping-bag me-2"></i>Continuer mes achats
                </button>
            </div>
        </div>
    </div>

    <!-- Customer Stats -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="customer-stat-card bg-primary">
                <div class="stat-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="stat-content">
                    <h3><?php echo $customerData['total_orders']; ?></h3>
                    <p>Commandes</p>
                    <small>Total passées</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="customer-stat-card bg-success">
                <div class="stat-icon">
                    <i class="fas fa-coins"></i>
                </div>
                <div class="stat-content">
                    <h3><?php echo number_format($customerData['total_spent']); ?> DA</h3>
                    <p>Dépensé</p>
                    <small>Montant total</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="customer-stat-card bg-warning">
                <div class="stat-icon">
                    <i class="fas fa-star"></i>
                </div>
                <div class="stat-content">
                    <h3><?php echo $customerData['loyalty_points']; ?></h3>
                    <p>Points fidélité</p>
                    <small>Disponibles</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="customer-stat-card bg-info">
                <div class="stat-icon">
                    <i class="fas fa-calendar"></i>
                </div>
                <div class="stat-content">
                    <h3><?php echo date('Y', strtotime($customerData['member_since'])); ?></h3>
                    <p>Membre depuis</p>
                    <small><?php echo date('d/m/Y', strtotime($customerData['member_since'])); ?></small>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions & Order Tracking -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-bolt me-2"></i>Actions rapides</h5>
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-primary w-100" onclick="browseProducts()">
                                <i class="fas fa-shopping-bag me-2"></i>Parcourir les produits
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-primary w-100" onclick="trackOrder()">
                                <i class="fas fa-search me-2"></i>Suivre une commande
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-primary w-100" onclick="contactSupport()">
                                <i class="fas fa-headset me-2"></i>Support client
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-success w-100" onclick="showPriceComparison()">
                                <i class="fas fa-balance-scale me-2"></i>Comparer les prix
                            </button>
                        </div>
                    </div>

                    <!-- Real-time Order Tracking -->
                    <div class="row mt-3">
                        <div class="col-md-8">
                            <div class="input-group">
                                <input type="text" class="form-control" id="trackingNumber"
                                    placeholder="Numéro de commande ou email pour suivi rapide">
                                <button class="btn btn-outline-secondary" onclick="quickTrackOrder()">
                                    <i class="fas fa-search me-1"></i>Suivre
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-info w-100" onclick="showMessaging()">
                                <i class="fas fa-comments me-2"></i>Messages vendeurs
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Tabs -->
    <div class="row">
        <div class="col-md-12">
            <ul class="nav nav-tabs mb-4" id="customerTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="orders-tab" data-bs-toggle="tab" data-bs-target="#orders" type="button" role="tab">
                        <i class="fas fa-shopping-cart me-2"></i>Mes commandes
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile" type="button" role="tab">
                        <i class="fas fa-user me-2"></i>Mon profil
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="subscription-tab" data-bs-toggle="tab" data-bs-target="#subscription" type="button" role="tab">
                        <i class="fas fa-crown me-2"></i>Mon abonnement
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="support-tab" data-bs-toggle="tab" data-bs-target="#support" type="button" role="tab">
                        <i class="fas fa-headset me-2"></i>Support
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="customerTabsContent">
                <!-- Orders Tab -->
                <div class="tab-pane fade show active" id="orders" role="tabpanel">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5><i class="fas fa-shopping-cart me-2"></i>Historique des commandes</h5>
                            <button class="btn btn-primary btn-sm" onclick="browseProducts()">
                                <i class="fas fa-plus me-2"></i>Nouvelle commande
                            </button>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($recentOrders)): ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Commande</th>
                                                <th>Date</th>
                                                <th>Articles</th>
                                                <th>Total</th>
                                                <th>Statut</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recentOrders as $order): ?>
                                                <tr>
                                                    <td><strong><?php echo $order['id']; ?></strong></td>
                                                    <td><?php echo date('d/m/Y', strtotime($order['date'])); ?></td>
                                                    <td><?php echo $order['items']; ?> article(s)</td>
                                                    <td><?php echo number_format($order['total']); ?> DA</td>
                                                    <td>
                                                        <?php
                                                        $statusClass = '';
                                                        $statusText = '';
                                                        switch ($order['status']) {
                                                            case 'delivered':
                                                                $statusClass = 'success';
                                                                $statusText = 'Livré';
                                                                break;
                                                            case 'shipped':
                                                                $statusClass = 'info';
                                                                $statusText = 'Expédié';
                                                                break;
                                                            case 'processing':
                                                                $statusClass = 'warning';
                                                                $statusText = 'En cours';
                                                                break;
                                                            default:
                                                                $statusClass = 'secondary';
                                                                $statusText = 'Inconnu';
                                                        }
                                                        ?>
                                                        <span class="badge bg-<?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                                                    </td>
                                                    <td>
                                                        <button class="btn btn-sm btn-outline-primary" onclick="viewOrder('<?php echo $order['id']; ?>')">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <?php if ($order['status'] === 'delivered'): ?>
                                                            <button class="btn btn-sm btn-outline-success" onclick="reorder('<?php echo $order['id']; ?>')">
                                                                <i class="fas fa-redo"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                                    <h5>Aucune commande pour le moment</h5>
                                    <p class="text-muted">Commencez vos achats dès maintenant</p>
                                    <button class="btn btn-primary" onclick="browseProducts()">
                                        <i class="fas fa-shopping-bag me-2"></i>Découvrir nos produits
                                    </button>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Profile Tab -->
                <div class="tab-pane fade" id="profile" role="tabpanel">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-user me-2"></i>Informations personnelles</h5>
                                </div>
                                <div class="card-body">
                                    <form id="profileForm">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="customerName" class="form-label">Nom complet</label>
                                                    <input type="text" class="form-control" id="customerName" value="<?php echo htmlspecialchars($customerData['name']); ?>">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="customerEmail" class="form-label">Email</label>
                                                    <input type="email" class="form-control" id="customerEmail" value="<?php echo htmlspecialchars($customerData['email']); ?>">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="customerPhone" class="form-label">Téléphone</label>
                                                    <input type="tel" class="form-control" id="customerPhone" value="<?php echo htmlspecialchars($customerData['phone']); ?>">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="customerBirthdate" class="form-label">Date de naissance</label>
                                                    <input type="date" class="form-control" id="customerBirthdate">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="customerAddress" class="form-label">Adresse</label>
                                            <textarea class="form-control" id="customerAddress" rows="3"><?php echo htmlspecialchars($customerData['address']); ?></textarea>
                                        </div>
                                        <button type="button" class="btn btn-primary" onclick="updateProfile()">
                                            <i class="fas fa-save me-2"></i>Sauvegarder les modifications
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-shield-alt me-2"></i>Sécurité</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="currentPassword" class="form-label">Mot de passe actuel</label>
                                        <input type="password" class="form-control" id="currentPassword">
                                    </div>
                                    <div class="mb-3">
                                        <label for="newPassword" class="form-label">Nouveau mot de passe</label>
                                        <input type="password" class="form-control" id="newPassword">
                                    </div>
                                    <div class="mb-3">
                                        <label for="confirmPassword" class="form-label">Confirmer le mot de passe</label>
                                        <input type="password" class="form-control" id="confirmPassword">
                                    </div>
                                    <button type="button" class="btn btn-warning w-100" onclick="changePassword()">
                                        <i class="fas fa-key me-2"></i>Changer le mot de passe
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Subscription Tab -->
                <div class="tab-pane fade" id="subscription" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-crown me-2"></i>Mon abonnement</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="subscription-info">
                                        <h6>Plan actuel: <span class="badge bg-primary">Gratuit</span></h6>
                                        <p class="text-muted">Vous utilisez actuellement le plan gratuit. Passez à un plan premium pour débloquer plus de fonctionnalités.</p>

                                        <div class="features-list mt-4">
                                            <h6>Fonctionnalités incluses:</h6>
                                            <ul class="list-unstyled">
                                                <li><i class="fas fa-check text-success me-2"></i>Accès aux produits de base</li>
                                                <li><i class="fas fa-check text-success me-2"></i>Support par email</li>
                                                <li><i class="fas fa-times text-danger me-2"></i>Livraison prioritaire</li>
                                                <li><i class="fas fa-times text-danger me-2"></i>Remises exclusives</li>
                                                <li><i class="fas fa-times text-danger me-2"></i>Support téléphonique</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="upgrade-card">
                                        <h6>Passer au Premium</h6>
                                        <div class="price">
                                            <span class="amount">2,500</span>
                                            <span class="currency">DA/mois</span>
                                        </div>
                                        <button class="btn btn-primary w-100 mt-3" onclick="upgradeSubscription()">
                                            <i class="fas fa-crown me-2"></i>Passer au Premium
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Support Tab -->
                <div class="tab-pane fade" id="support" role="tabpanel">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-headset me-2"></i>Contacter le support</h5>
                                </div>
                                <div class="card-body">
                                    <form id="supportForm">
                                        <div class="mb-3">
                                            <label for="supportSubject" class="form-label">Sujet</label>
                                            <select class="form-select" id="supportSubject">
                                                <option value="">Sélectionner un sujet</option>
                                                <option value="order">Problème avec une commande</option>
                                                <option value="payment">Problème de paiement</option>
                                                <option value="product">Question sur un produit</option>
                                                <option value="account">Problème de compte</option>
                                                <option value="other">Autre</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="supportMessage" class="form-label">Message</label>
                                            <textarea class="form-control" id="supportMessage" rows="5" placeholder="Décrivez votre problème ou votre question..."></textarea>
                                        </div>
                                        <button type="button" class="btn btn-primary" onclick="sendSupportMessage()">
                                            <i class="fas fa-paper-plane me-2"></i>Envoyer le message
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-info-circle me-2"></i>Informations de contact</h5>
                                </div>
                                <div class="card-body">
                                    <div class="contact-info">
                                        <div class="contact-item">
                                            <i class="fas fa-envelope text-primary me-2"></i>
                                            <strong>Email:</strong><br>
                                            <a href="mailto:<EMAIL>"><EMAIL></a>
                                        </div>
                                        <div class="contact-item">
                                            <i class="fas fa-phone text-success me-2"></i>
                                            <strong>Téléphone:</strong><br>
                                            +213 21 123 456
                                        </div>
                                        <div class="contact-item">
                                            <i class="fas fa-clock text-warning me-2"></i>
                                            <strong>Horaires:</strong><br>
                                            Lun-Ven: 9h-18h<br>
                                            Sam: 9h-13h
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Customer Dashboard JavaScript Functions

    // Notification system
    function showNotification(message, type = 'info') {
        // Try to use global notification system first
        if (window.showNotification) {
            window.showNotification(message, type);
            return;
        }

        // Fallback notification system
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'danger' ? 'danger' : type === 'warning' ? 'warning' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        document.body.appendChild(notification);

        // Auto remove after 4 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 4000);
    }

    // Profile management
    function editProfile() {
        const profileTab = document.getElementById('profile-tab');
        if (profileTab) {
            profileTab.click();
        } else {
            // Fallback: scroll to profile section
            const profileSection = document.querySelector('.customer-profile-section');
            if (profileSection) {
                profileSection.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        }
    }

    function updateProfile() {
        const profileData = {
            name: document.getElementById('customerName').value,
            email: document.getElementById('customerEmail').value,
            phone: document.getElementById('customerPhone').value,
            birthdate: document.getElementById('customerBirthdate').value,
            address: document.getElementById('customerAddress').value
        };

        // Mock update - in production, send to API
        console.log('Updating profile:', profileData);
        showNotification('Profil mis à jour avec succès!', 'success');
    }

    function changePassword() {
        const currentPassword = document.getElementById('currentPassword').value;
        const newPassword = document.getElementById('newPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;

        if (!currentPassword || !newPassword || !confirmPassword) {
            showNotification('Veuillez remplir tous les champs', 'warning');
            return;
        }

        if (newPassword !== confirmPassword) {
            showNotification('Les mots de passe ne correspondent pas', 'danger');
            return;
        }

        if (newPassword.length < 6) {
            showNotification('Le mot de passe doit contenir au moins 6 caractères', 'warning');
            return;
        }

        // Mock update - in production, send to API
        console.log('Changing password');
        showNotification('Mot de passe modifié avec succès!', 'success');

        // Clear form
        document.getElementById('currentPassword').value = '';
        document.getElementById('newPassword').value = '';
        document.getElementById('confirmPassword').value = '';
    }

    // Order management
    function viewOrder(orderId) {
        // In a real application, this would open a modal or navigate to order details
        showNotification(`Affichage des détails de la commande ${orderId}`, 'info');

        // Mock order details - in production, fetch from API
        console.log('Fetching order details for:', orderId);

        // Example: Open modal with order details
        // const modal = new bootstrap.Modal(document.getElementById('orderDetailsModal'));
        // modal.show();
    }

    function reorder(orderId) {
        if (confirm(`Voulez-vous repasser la commande ${orderId} ?`)) {
            // In production, this would add items to cart via API
            showNotification(`Commande ${orderId} ajoutée au panier`, 'success');

            // Mock API call
            console.log('Reordering:', orderId);
        }
    }

    function browseProducts() {
        // In production, redirect to actual store/products page
        showNotification('Redirection vers la boutique...', 'info');

        // Example redirect (uncomment in production):
        // window.location.href = '/store';

        // For demo, just show notification
        setTimeout(() => {
            showNotification('Fonctionnalité en cours de développement', 'warning');
        }, 1000);
    }

    // Enhanced customer features
    function quickTrackOrder() {
        const trackingNumber = document.getElementById('trackingNumber').value.trim();
        if (!trackingNumber) {
            showNotification('Veuillez saisir un numéro de commande ou email', 'warning');
            return;
        }

        // Show loading state
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Recherche...';
        btn.disabled = true;

        // Simulate API call for order tracking
        setTimeout(() => {
            // In production, this would call the actual tracking API
            showOrderTrackingModal(trackingNumber);
            btn.innerHTML = originalText;
            btn.disabled = false;
        }, 1000);
    }

    function showPriceComparison() {
        showNotification('Fonctionnalité de comparaison de prix à venir', 'info');
        // In production, this would show a price comparison tool
    }

    function showMessaging() {
        // Redirect to messages page
        window.location.href = '?page=messages';
    }

    function contactSeller() {
        showNotification('Redirection vers la messagerie...', 'info');
        // In production, this would open the messaging interface
    }

    // Subscription management
    function upgradeSubscription() {
        if (confirm('Voulez-vous passer au plan Premium pour 2,500 DA/mois ?')) {
            showNotification('Redirection vers la page de paiement...', 'info');

            // In production, redirect to payment page
            // window.location.href = '/upgrade-subscription';

            // Mock payment process
            setTimeout(() => {
                showNotification('Fonctionnalité de paiement en cours de développement', 'warning');
            }, 1000);
        }
    }

    // Support
    function sendSupportMessage() {
        const subjectEl = document.getElementById('supportSubject');
        const messageEl = document.getElementById('supportMessage');

        if (!subjectEl || !messageEl) {
            showNotification('Formulaire de support non trouvé', 'error');
            return;
        }

        const subject = subjectEl.value.trim();
        const message = messageEl.value.trim();

        if (!subject || !message) {
            showNotification('Veuillez remplir tous les champs', 'warning');
            return;
        }

        if (subject.length < 5) {
            showNotification('Le sujet doit contenir au moins 5 caractères', 'warning');
            return;
        }

        if (message.length < 10) {
            showNotification('Le message doit contenir au moins 10 caractères', 'warning');
            return;
        }

        // Show loading state
        const submitBtn = document.querySelector('[onclick="sendSupportMessage()"]');
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Envoi en cours...';
        }

        // Mock API call - in production, send to actual support API
        setTimeout(() => {
            console.log('Sending support message:', {
                subject,
                message
            });
            showNotification('Message envoyé avec succès! Nous vous répondrons dans les plus brefs délais.', 'success');

            // Clear form
            subjectEl.value = '';
            messageEl.value = '';

            // Reset button
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-paper-plane me-2"></i>Envoyer';
            }
        }, 1500);
    }

    // Form validation helpers
    function validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    }

    function validatePhone(phone) {
        const re = /^[\+]?[0-9\s\-\(\)]{8,}$/;
        return re.test(phone);
    }

    // Initialize dashboard when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        // Add form validation to profile forms
        const profileForm = document.querySelector('.profile-form');
        if (profileForm) {
            profileForm.addEventListener('submit', function(e) {
                e.preventDefault();
                updateProfile();
            });
        }

        // Add real-time validation to email field
        const emailField = document.getElementById('customerEmail');
        if (emailField) {
            emailField.addEventListener('blur', function() {
                if (this.value && !validateEmail(this.value)) {
                    showNotification('Format d\'email invalide', 'warning');
                    this.classList.add('is-invalid');
                } else {
                    this.classList.remove('is-invalid');
                }
            });
        }

        // Add real-time validation to phone field
        const phoneField = document.getElementById('customerPhone');
        if (phoneField) {
            phoneField.addEventListener('blur', function() {
                if (this.value && !validatePhone(this.value)) {
                    showNotification('Format de téléphone invalide', 'warning');
                    this.classList.add('is-invalid');
                } else {
                    this.classList.remove('is-invalid');
                }
            });
        }
    });
</script>

<style>
    .customer-dashboard .customer-stat-card {
        background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-primary) 100%);
        border-radius: 15px;
        padding: 20px;
        color: white;
        display: flex;
        align-items: center;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
    }

    .customer-dashboard .customer-stat-card:hover {
        transform: translateY(-3px);
    }

    .customer-dashboard .customer-stat-card.bg-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .customer-dashboard .customer-stat-card.bg-success {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    .customer-dashboard .customer-stat-card.bg-warning {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }

    .customer-dashboard .customer-stat-card.bg-info {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    .customer-dashboard .stat-icon {
        width: 50px;
        height: 50px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        font-size: 20px;
    }

    .customer-dashboard .stat-content h3 {
        font-size: 1.5rem;
        font-weight: bold;
        margin: 0;
    }

    .customer-dashboard .stat-content p {
        font-size: 1rem;
        margin: 0;
        opacity: 0.9;
    }

    .customer-dashboard .stat-content small {
        font-size: 0.8rem;
        opacity: 0.7;
    }

    .customer-dashboard .nav-tabs .nav-link {
        border: none;
        color: #6c757d;
        font-weight: 500;
        padding: 12px 20px;
    }

    .customer-dashboard .nav-tabs .nav-link.active {
        color: #667eea;
        border-bottom: 3px solid #667eea;
        background: none;
    }

    .customer-dashboard .nav-tabs .nav-link:hover {
        border-color: transparent;
        color: #667eea;
    }

    .customer-dashboard .card {
        border: none;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border-radius: 10px;
    }

    .customer-dashboard .card-header {
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        border-radius: 10px 10px 0 0 !important;
        font-weight: 600;
    }

    .customer-dashboard .table th {
        border-top: none;
        font-weight: 600;
        color: #495057;
        background: #f8f9fa;
    }

    .customer-dashboard .upgrade-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 10px;
        text-align: center;
    }

    .customer-dashboard .upgrade-card .price {
        font-size: 2rem;
        font-weight: bold;
        margin: 10px 0;
    }

    .customer-dashboard .upgrade-card .amount {
        font-size: 2.5rem;
    }

    .customer-dashboard .upgrade-card .currency {
        font-size: 1rem;
        opacity: 0.8;
    }

    .customer-dashboard .contact-info .contact-item {
        padding: 15px 0;
        border-bottom: 1px solid #eee;
    }

    .customer-dashboard .contact-info .contact-item:last-child {
        border-bottom: none;
    }

    .customer-dashboard .features-list ul li {
        padding: 5px 0;
    }

    .customer-dashboard .subscription-info {
        padding: 20px;
        background: #f8f9fa;
        border-radius: 10px;
    }

    .customer-dashboard .page-header h1 {
        color: #2d3748;
        font-weight: 600;
    }

    .customer-dashboard .btn {
        border-radius: 8px;
        font-weight: 500;
    }

    @media (max-width: 768px) {
        .customer-dashboard .customer-stat-card {
            margin-bottom: 15px;
        }

        .customer-dashboard .stat-icon {
            width: 40px;
            height: 40px;
            margin-right: 10px;
            font-size: 16px;
        }

        .customer-dashboard .stat-content h3 {
            font-size: 1.25rem;
        }
    }

    /* Form improvements */
    .customer-dashboard .form-control:focus {
        border-color: var(--bs-primary);
        box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
    }

    .customer-dashboard .form-control.is-invalid {
        border-color: #dc3545;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }

    .customer-dashboard .btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    /* Loading spinner */
    .fa-spinner {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    /* Notification improvements */
    .alert.position-fixed {
        animation: slideInRight 0.3s ease-out;
    }

    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }

        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    /* RTL support */
    [dir="rtl"] .alert.position-fixed {
        right: auto;
        left: 20px;
        animation: slideInLeft 0.3s ease-out;
    }

    @keyframes slideInLeft {
        from {
            transform: translateX(-100%);
            opacity: 0;
        }

        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
</style>
