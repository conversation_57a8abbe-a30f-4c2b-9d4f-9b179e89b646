<?php
// Test the actual API endpoint
$url = 'http://localhost:8000/api/categories.php?action=user-categories&store_id=7';

$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => 'Content-Type: application/json'
    ]
]);

echo 'Testing API endpoint: ' . $url . PHP_EOL;

$response = file_get_contents($url, false, $context);
if ($response === false) {
    echo 'Failed to get response from API' . PHP_EOL;
} else {
    echo 'API Response:' . PHP_EOL;
    echo $response . PHP_EOL;
    
    // Parse JSON response
    $data = json_decode($response, true);
    if ($data) {
        echo PHP_EOL . 'Parsed response:' . PHP_EOL;
        echo 'Success: ' . ($data['success'] ? 'true' : 'false') . PHP_EOL;
        if (isset($data['categories'])) {
            echo 'Categories count: ' . count($data['categories']) . PHP_EOL;
        }
        if (isset($data['error'])) {
            echo 'Error: ' . $data['error'] . PHP_EOL;
        }
    }
}
?>
