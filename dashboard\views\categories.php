<div class="categories-section">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3><i class="fas fa-tags me-2"></i>Catégories</h3>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createCategoryModal">
            <i class="fas fa-plus me-2"></i>Créer une catégorie
        </button>
    </div>

    <!-- Statistiques rapides -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-primary">
                    <i class="fas fa-tags"></i>
                </div>
                <div class="stat-content">
                    <h4>Catégories</h4>
                    <p id="totalCategories"><?php
                                            // Load categories count server-side
                                            try {
                                                require_once __DIR__ . '/../includes/database.php';
                                                $db = connectDB();
                                                $storeId = $_GET['store_id'] ?? 7;

                                                // Get merchant_id
                                                $merchantQuery = "SELECT merchant_id FROM stores WHERE id = ?";
                                                $merchantStmt = $db->prepare($merchantQuery);
                                                $merchantStmt->execute([$storeId]);
                                                $storeData = $merchantStmt->fetch(PDO::FETCH_ASSOC);
                                                $merchantId = $storeData['merchant_id'] ?? 3;

                                                // Count categories with products
                                                $countQuery = "
                            SELECT COUNT(DISTINCT c.id) as count
                            FROM categories c
                            JOIN product_categories pc ON c.id = pc.category_id
                            JOIN products p ON pc.product_id = p.id AND p.status = 'active'
                            WHERE c.merchant_id = ? AND c.status = 'active'
                        ";
                                                $stmt = $db->prepare($countQuery);
                                                $stmt->execute([$merchantId]);
                                                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                                                echo $result['count'] ?? 0;
                                            } catch (Exception $e) {
                                                echo '0';
                                            }
                                            ?></p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-success">
                    <i class="fas fa-layer-group"></i>
                </div>
                <div class="stat-content">
                    <h4>Sous-catégories</h4>
                    <p id="totalSubcategories">0</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-info">
                    <i class="fas fa-box"></i>
                </div>
                <div class="stat-content">
                    <h4>Produits</h4>
                    <p id="totalCategorizedProducts"><?php
                                                        // Load total products count server-side
                                                        try {
                                                            $totalProductsQuery = "SELECT COUNT(*) as count FROM products WHERE store_id = ? AND status = 'active'";
                                                            $stmt = $db->prepare($totalProductsQuery);
                                                            $stmt->execute([$storeId]);
                                                            $result = $stmt->fetch(PDO::FETCH_ASSOC);
                                                            echo $result['count'] ?? 0;
                                                        } catch (Exception $e) {
                                                            echo '0';
                                                        }
                                                        ?></p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-warning">
                    <i class="fas fa-eye"></i>
                </div>
                <div class="stat-content">
                    <h4>Plus populaire</h4>
                    <p id="mostPopularCategory"><?php
                                                // Load most popular category server-side
                                                try {
                                                    $popularQuery = "
                            SELECT c.name, COUNT(DISTINCT pc.product_id) as products_count
                            FROM categories c
                            JOIN product_categories pc ON c.id = pc.category_id
                            JOIN products p ON pc.product_id = p.id AND p.status = 'active'
                            WHERE c.merchant_id = ? AND c.status = 'active'
                            GROUP BY c.id, c.name
                            ORDER BY products_count DESC
                            LIMIT 1
                        ";
                                                    $stmt = $db->prepare($popularQuery);
                                                    $stmt->execute([$merchantId]);
                                                    $result = $stmt->fetch(PDO::FETCH_ASSOC);
                                                    echo htmlspecialchars($result['name'] ?? '-');
                                                } catch (Exception $e) {
                                                    echo '-';
                                                }
                                                ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtres -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="input-group">
                <span class="input-group-text"><i class="fas fa-search"></i></span>
                <input type="text" class="form-control" placeholder="Rechercher une catégorie..." id="searchCategories">
            </div>
        </div>
        <div class="col-md-3">
            <select class="form-select" id="filterType">
                <option value="">Tous types</option>
                <option value="parent">Catégories principales</option>
                <option value="child">Sous-catégories</option>
            </select>
        </div>
        <div class="col-md-3">
            <select class="form-select" id="sortBy">
                <option value="name">Trier par nom</option>
                <option value="products_count">Trier par nb produits</option>
                <option value="created_at">Trier par date</option>
            </select>
        </div>
    </div>

    <!-- Liste des catégories -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">Mes Catégories</h5>
        </div>
        <div class="card-body">
            <div id="categoriesContainer">
                <?php
                // Load categories server-side for reliable display
                try {
                    // Get categories with product counts
                    $categoriesQuery = "
                        SELECT
                            c.id,
                            COALESCE(c.name_fr, c.name) as name,
                            c.slug,
                            c.description,
                            c.status,
                            c.created_at,
                            COUNT(DISTINCT pc.product_id) as products_count
                        FROM categories c
                        LEFT JOIN product_categories pc ON c.id = pc.category_id
                        LEFT JOIN products p ON pc.product_id = p.id AND p.status = 'active'
                        WHERE c.merchant_id = ? AND c.status = 'active'
                        GROUP BY c.id, c.name, c.name_fr, c.slug, c.description, c.status, c.created_at
                        HAVING products_count > 0
                        ORDER BY c.name ASC
                    ";

                    $stmt = $db->prepare($categoriesQuery);
                    $stmt->execute([$merchantId]);
                    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

                    if (!empty($categories)):
                ?>
                        <div class="row">
                            <?php foreach ($categories as $category): ?>
                                <div class="col-md-6 mb-3">
                                    <div class="card category-card h-100">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <h6 class="card-title mb-0"><?php echo htmlspecialchars($category['name']); ?></h6>
                                                <span class="badge bg-primary"><?php echo $category['products_count']; ?> produits</span>
                                            </div>

                                            <?php if (!empty($category['description'])): ?>
                                                <p class="card-text text-muted small"><?php echo htmlspecialchars($category['description']); ?></p>
                                            <?php endif; ?>

                                            <div class="d-flex gap-2 mt-3">
                                                <button class="btn btn-outline-primary btn-sm" onclick="editCategory(<?php echo $category['id']; ?>)">
                                                    <i class="fas fa-edit me-1"></i>Modifier
                                                </button>
                                                <a href="/store-frontend.php?store=medical-software-solutions&page=category&category=<?php echo urlencode($category['name']); ?>"
                                                    class="btn btn-outline-success btn-sm" target="_blank">
                                                    <i class="fas fa-eye me-1"></i>Voir
                                                </a>
                                                <button class="btn btn-outline-danger btn-sm" onclick="deleteCategory(<?php echo $category['id']; ?>)">
                                                    <i class="fas fa-trash me-1"></i>Supprimer
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Aucune catégorie trouvée</h5>
                            <p class="text-muted">Créez votre première catégorie pour organiser vos produits.</p>
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createCategoryModal">
                                <i class="fas fa-plus me-2"></i>Créer une catégorie
                            </button>
                        </div>
                <?php
                    endif;
                } catch (Exception $e) {
                    echo '<div class="alert alert-danger">Erreur lors du chargement des catégories: ' . htmlspecialchars($e->getMessage()) . '</div>';
                }
                ?>
            </div>
        </div>
    </div>
</div>

<!-- Modal Créer/Modifier Catégorie -->
<div class="modal fade" id="createCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Créer une nouvelle catégorie</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="categoryForm">
                    <div class="mb-3">
                        <label for="categoryName" class="form-label">Nom de la catégorie</label>
                        <input type="text" class="form-control" id="categoryName" name="name" required>
                    </div>

                    <div class="mb-3">
                        <label for="categorySlug" class="form-label">Slug (URL)</label>
                        <input type="text" class="form-control" id="categorySlug" name="slug" required>
                        <div class="form-text">Généré automatiquement à partir du nom</div>
                    </div>

                    <div class="mb-3">
                        <label for="categoryDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="categoryDescription" name="description" rows="3"></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="parentCategory" class="form-label">Catégorie parente</label>
                        <select class="form-select" id="parentCategory" name="parent_id">
                            <option value="">Aucune (catégorie principale)</option>
                        </select>
                        <div class="form-text">Laissez vide pour créer une catégorie principale</div>
                    </div>

                    <div class="mb-3">
                        <label for="categoryImage" class="form-label">Image de la catégorie (URL)</label>
                        <input type="url" class="form-control" id="categoryImage" placeholder="https://exemple.com/image.jpg">
                    </div>

                    <div class="mb-3">
                        <label for="categoryColor" class="form-label">Couleur</label>
                        <input type="color" class="form-control form-control-color" id="categoryColor" value="#667eea">
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="categoryOrder" class="form-label">Ordre d'affichage</label>
                                <input type="number" class="form-control" id="categoryOrder" min="0" value="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="categoryStatus" class="form-label">Statut</label>
                                <select class="form-select" id="categoryStatus">
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- SEO -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">Paramètres SEO</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="metaTitle" class="form-label">Meta Title</label>
                                <input type="text" class="form-control" id="metaTitle">
                            </div>
                            <div class="mb-3">
                                <label for="metaDescription" class="form-label">Meta Description</label>
                                <textarea class="form-control" id="metaDescription" rows="2"></textarea>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" id="saveCategory">Créer la catégorie</button>
            </div>
        </div>
    </div>
</div>

<script>
    console.log('🔧 Categories page script loaded');
    // Variable globale pour stocker les catégories
    let userCategories = [];

    // Charger les catégories au chargement de la section
    function initializeCategoriesPage() {
        if (document.querySelector('.categories-section')) {
            console.log('🚀 Initializing categories page...');
            loadUserCategories();
            initCategoriesEventListeners();
        } else {
            console.warn('⚠️ Categories section not found, retrying in 100ms...');
            setTimeout(initializeCategoriesPage, 100);
        }
    }

    // Try to initialize immediately, or wait for DOM
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeCategoriesPage);
    } else {
        initializeCategoriesPage();
    }

    function initCategoriesEventListeners() {
        // Save category button event
        document.getElementById('saveCategory').addEventListener('click', function() {
            saveCategoryForm();
        });

        // Auto-generate slug from name
        document.getElementById('categoryName').addEventListener('input', function() {
            const slug = this.value.toLowerCase()
                .replace(/[^a-z0-9\s-]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .trim('-');
            document.getElementById('categorySlug').value = slug;
        });

        // Search functionality
        document.getElementById('searchCategories').addEventListener('input', function() {
            filterCategories();
        });

        // Filter functionality
        document.getElementById('filterType').addEventListener('change', function() {
            filterCategories();
        });

        // Sort functionality
        document.getElementById('sortBy').addEventListener('change', function() {
            sortCategories();
        });

        // Load categories on page initialization
        console.log('🚀 Categories page initialized, loading categories...');
        loadUserCategories();
    }

    async function loadUserCategories() {
        try {
            // Get parameters from URL
            const urlParams = new URLSearchParams(window.location.search);
            const merchantEmail = urlParams.get('email') || '';
            const storeId = urlParams.get('store_id') || '';

            // Build API URL with available parameters
            let apiUrl = '/api/categories.php?action=user-categories';
            if (merchantEmail) {
                apiUrl += '&email=' + encodeURIComponent(merchantEmail);
            }
            if (storeId) {
                apiUrl += '&store_id=' + encodeURIComponent(storeId);
            }

            console.log('🏷️ Loading categories for merchant:', merchantEmail || 'current user', 'store:', storeId);
            console.log('🌐 Categories API URL:', apiUrl);

            const response = await fetch(apiUrl);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('📋 Categories API Response:', data);

            if (data.success) {
                // Store categories globally for use in other functions
                userCategories = data.categories || [];
                console.log('✅ Categories loaded successfully:', userCategories.length, 'categories');
                displayCategories(userCategories);
                updateCategoriesStats(data.stats || {});
                populateParentCategories(userCategories);
            } else {
                console.error('❌ Categories API Error:', data.error || 'Unknown error');
                displayCategories([]); // Show empty state
                showError('Erreur lors du chargement des catégories');
            }
        } catch (error) {
            console.error('💥 Categories loading error:', error);
            displayCategories([]); // Show empty state
            showError('Erreur de connexion');
        }
    }

    function displayCategories(categories) {
        const container = document.getElementById('categoriesContainer');

        // Add null/undefined check
        if (!categories || !Array.isArray(categories) || categories.length === 0) {
            container.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                <h5>Aucune catégorie créée</h5>
                <p class="text-muted">Organisez vos produits en créant des catégories</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createCategoryModal">
                    <i class="fas fa-plus me-2"></i>Créer ma première catégorie
                </button>
            </div>
        `;
            return;
        }

        // Organiser les catégories par hiérarchie
        const parentCategories = categories.filter(cat => !cat.parent_id);
        const childCategories = categories.filter(cat => cat.parent_id);

        let html = '';

        parentCategories.forEach(parent => {
            const children = childCategories.filter(child => child.parent_id === parent.id);

            html += `
            <div class="category-group mb-4">
                <div class="category-item parent-category">
                    <div class="d-flex align-items-center">
                        <div class="category-color" style="background-color: ${parent.color || '#667eea'}"></div>
                        <div class="category-info flex-grow-1">
                            <h6 class="mb-1">${parent.name}</h6>
                            <p class="text-muted mb-0">${parent.description || 'Aucune description'}</p>
                        </div>
                        <div class="category-stats me-3">
                            <span class="badge bg-primary">${parent.products_count || 0} produits</span>
                        </div>
                        <div class="category-actions">
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="editCategory(${parent.id})">
                                        <i class="fas fa-edit me-2"></i>Modifier
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="addSubcategory(${parent.id})">
                                        <i class="fas fa-plus me-2"></i>Ajouter sous-catégorie
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item text-danger" href="#" onclick="deleteCategory(${parent.id})">
                                        <i class="fas fa-trash me-2"></i>Supprimer
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                ${children.length > 0 ? `
                    <div class="subcategories ms-4">
                        ${children.map(child => `
                            <div class="category-item subcategory">
                                <div class="d-flex align-items-center">
                                    <div class="category-color small" style="background-color: ${child.color || '#667eea'}"></div>
                                    <div class="category-info flex-grow-1">
                                        <h6 class="mb-1">${child.name}</h6>
                                        <p class="text-muted mb-0 small">${child.description || 'Aucune description'}</p>
                                    </div>
                                    <div class="category-stats me-3">
                                        <span class="badge bg-secondary">${child.products_count || 0} produits</span>
                                    </div>
                                    <div class="category-actions">
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="dropdown">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#" onclick="editCategory(${child.id})">
                                                    <i class="fas fa-edit me-2"></i>Modifier
                                                </a></li>
                                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteCategory(${child.id})">
                                                    <i class="fas fa-trash me-2"></i>Supprimer
                                                </a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                ` : ''}
            </div>
        `;
        });

        container.innerHTML = html;
    }

    function populateParentCategories(categories) {
        const parentSelect = document.getElementById('parentCategory');
        parentSelect.innerHTML = '<option value="">Aucune (catégorie principale)</option>';

        // Add null/undefined check
        if (!categories || !Array.isArray(categories)) {
            return;
        }

        const parentCategories = categories.filter(cat => cat && !cat.parent_id);
        parentCategories.forEach(category => {
            if (category && category.name && category.id) {
                const option = new Option(category.name, category.id);
                parentSelect.add(option);
            }
        });
    }

    function updateCategoriesStats(stats) {
        console.log('📊 Updating categories stats:', stats);
        if (stats) {
            const totalCategoriesEl = document.getElementById('totalCategories');
            const totalSubcategoriesEl = document.getElementById('totalSubcategories');
            const totalProductsEl = document.getElementById('totalCategorizedProducts');
            const mostPopularEl = document.getElementById('mostPopularCategory');

            if (totalCategoriesEl) totalCategoriesEl.textContent = stats.total_categories || 0;
            if (totalSubcategoriesEl) totalSubcategoriesEl.textContent = stats.total_subcategories || 0;
            if (totalProductsEl) totalProductsEl.textContent = stats.total_products || 0;
            if (mostPopularEl) mostPopularEl.textContent = stats.most_popular || '-';

            console.log('✅ Stats updated - Categories:', stats.total_categories, 'Products:', stats.total_products, 'Popular:', stats.most_popular);
        } else {
            console.warn('⚠️ No stats data provided');
        }
    }

    function filterCategories() {
        // Implement filtering logic
        console.log('Filtering categories...');
    }

    function sortCategories() {
        // Implement sorting logic
        console.log('Sorting categories...');
    }

    function editCategory(id) {
        // Find the category to edit (flatten subcategories for search)
        let allCategories = [];
        userCategories.forEach(cat => {
            allCategories.push(cat);
            if (cat.subcategories) {
                allCategories = allCategories.concat(cat.subcategories);
            }
        });

        const category = allCategories.find(cat => cat.id === id);
        if (!category) {
            showNotification('Catégorie non trouvée', 'error');
            return;
        }

        // Fill the form with category data
        document.getElementById('categoryName').value = category.name;
        document.getElementById('categoryDescription').value = category.description || '';
        document.getElementById('categorySlug').value = category.slug || '';
        document.getElementById('parentCategory').value = category.parent_id || '';

        // Update modal title and button
        document.querySelector('#createCategoryModal .modal-title').textContent = 'Modifier la catégorie';
        document.querySelector('#createCategoryModal .btn-primary').textContent = 'Mettre à jour';

        // Store the category ID for update
        document.getElementById('createCategoryModal').setAttribute('data-edit-id', id);

        // Show the modal
        const modal = new bootstrap.Modal(document.getElementById('createCategoryModal'));
        modal.show();
    }

    function addSubcategory(parentId) {
        // Find parent category name for display
        const parentCategory = userCategories.find(cat => cat.id === parentId);
        const parentName = parentCategory ? parentCategory.name : 'Catégorie inconnue';

        // Reset form
        document.getElementById('categoryForm').reset();

        // Pre-fill parent category
        document.getElementById('parentCategory').value = parentId;

        // Update modal title
        document.querySelector('#createCategoryModal .modal-title').textContent = `Ajouter une sous-catégorie à "${parentName}"`;
        document.querySelector('#createCategoryModal .btn-primary').textContent = 'Créer la sous-catégorie';

        // Remove edit ID if present
        document.getElementById('createCategoryModal').removeAttribute('data-edit-id');

        // Show the modal
        const modal = new bootstrap.Modal(document.getElementById('createCategoryModal'));
        modal.show();
    }

    function deleteCategory(id) {
        if (confirm('Êtes-vous sûr de vouloir supprimer cette catégorie ?')) {
            // Implement delete functionality
            console.log('Delete category:', id);
            showNotification('Catégorie supprimée avec succès', 'success');
            loadUserCategories(); // Reload categories
        }
    }

    function saveCategoryForm() {
        const form = document.getElementById('categoryForm');
        const formData = new FormData(form);
        const editId = document.getElementById('createCategoryModal').getAttribute('data-edit-id');

        const categoryData = {
            name: formData.get('name'),
            description: formData.get('description'),
            slug: formData.get('slug'),
            parent_id: formData.get('parent_id') || null
        };

        if (editId) {
            // Update existing category
            console.log('Updating category:', editId, categoryData);
            showNotification('Catégorie mise à jour avec succès', 'success');
        } else {
            // Create new category
            console.log('Creating new category:', categoryData);
            showNotification('Catégorie créée avec succès', 'success');
        }

        // Close modal and reload categories
        bootstrap.Modal.getInstance(document.getElementById('createCategoryModal')).hide();
        loadUserCategories();
    }

    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 4000);
    }

    function showError(message) {
        showNotification(message, 'danger');
        console.error(message);
    }
</script>

<style>
    .category-item {
        padding: 1rem;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        margin-bottom: 0.5rem;
        background: white;
    }

    .parent-category {
        border-left: 4px solid var(--primary-color);
    }

    .subcategory {
        background: #f8f9fa;
        border-left: 3px solid #dee2e6;
    }

    .category-color {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        margin-right: 1rem;
    }

    .category-color.small {
        width: 15px;
        height: 15px;
    }

    .category-info h6 {
        color: var(--dark-color);
    }

    .subcategories {
        margin-top: 1rem;
    }
</style>
