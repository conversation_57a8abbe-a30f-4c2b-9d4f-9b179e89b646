<?php
// Detect user role for proper page filtering
$userRole = $_SESSION['user_role'] ?? 'customer';
$storeId = $_GET['store_id'] ?? null;

// If store_id is provided, this is store pages management
if ($storeId) {
    // Include the store pages management view
    include __DIR__ . '/store-pages.php';
    return;
}

$pageTitle = $userRole === 'admin' ? 'Toutes les Pages' : 'Mes Pages';
?>

<div class="pages-section">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3><i class="fas fa-file-alt me-2"></i><?php echo $pageTitle; ?></h3>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createPageModal">
            <i class="fas fa-plus me-2"></i>Créer une page
        </button>
    </div>

    <!-- Filtres et recherche -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="input-group">
                <span class="input-group-text"><i class="fas fa-search"></i></span>
                <input type="text" class="form-control" placeholder="Rechercher une page..." id="searchPages">
            </div>
        </div>
        <div class="col-md-3">
            <select class="form-select" id="filterStatus">
                <option value="">Tous les statuts</option>
                <option value="published">Publié</option>
                <option value="draft">Brouillon</option>
                <option value="archived">Archivé</option>
            </select>
        </div>
        <div class="col-md-3">
            <select class="form-select" id="filterTemplate">
                <option value="">Tous les templates</option>
                <option value="ecommerce">E-commerce</option>
                <option value="portfolio">Portfolio</option>
                <option value="service">Service</option>
                <option value="restaurant">Restaurant</option>
            </select>
        </div>
    </div>

    <!-- Statistiques rapides -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-primary">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="stat-content">
                    <h4>Total Pages</h4>
                    <p id="totalPages">0</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-success">
                    <i class="fas fa-eye"></i>
                </div>
                <div class="stat-content">
                    <h4>Vues Totales</h4>
                    <p id="totalViews">0</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-warning">
                    <i class="fas fa-mouse-pointer"></i>
                </div>
                <div class="stat-content">
                    <h4>Conversions</h4>
                    <p id="totalConversions">0</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-info">
                    <i class="fas fa-percentage"></i>
                </div>
                <div class="stat-content">
                    <h4>Taux Conversion</h4>
                    <p id="conversionRate">0%</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Liste des pages -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">Mes Landing Pages</h5>
        </div>
        <div class="card-body">
            <div id="pagesContainer" class="row">
                <!-- Les pages seront chargées ici dynamiquement -->
                <div class="col-12 text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                    <p class="mt-2 text-muted">Chargement de vos pages...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Créer/Modifier Page -->
<div class="modal fade" id="createPageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Créer une nouvelle page</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="pageForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="pageTitle" class="form-label">Titre de la page</label>
                                <input type="text" class="form-control" id="pageTitle" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="pageSlug" class="form-label">URL (slug)</label>
                                <input type="text" class="form-control" id="pageSlug" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="pageTemplate" class="form-label">Template</label>
                                <select class="form-select" id="pageTemplate" required>
                                    <option value="">Choisir un template</option>
                                    <option value="ecommerce">E-commerce</option>
                                    <option value="portfolio">Portfolio</option>
                                    <option value="service">Service</option>
                                    <option value="restaurant">Restaurant</option>
                                    <option value="saas">SaaS</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="pageStatus" class="form-label">Statut</label>
                                <select class="form-select" id="pageStatus">
                                    <option value="draft">Brouillon</option>
                                    <option value="published">Publié</option>
                                    <option value="archived">Archivé</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="pageDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="pageDescription" rows="3"></textarea>
                    </div>

                    <!-- SEO -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">Paramètres SEO</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="metaTitle" class="form-label">Meta Title</label>
                                <input type="text" class="form-control" id="metaTitle">
                            </div>
                            <div class="mb-3">
                                <label for="metaDescription" class="form-label">Meta Description</label>
                                <textarea class="form-control" id="metaDescription" rows="2"></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="metaKeywords" class="form-label">Mots-clés</label>
                                <input type="text" class="form-control" id="metaKeywords" placeholder="mot1, mot2, mot3">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" id="savePage">Créer la page</button>
            </div>
        </div>
    </div>
</div>

<?php if (!$storeId): ?>
    <script>
        // Charger les pages au chargement de la section
        document.addEventListener('DOMContentLoaded', function() {
            if (document.querySelector('.pages-section')) {
                loadUserPages();
            }
        });

        async function loadUserPages() {
            try {
                // Detect user role from PHP
                const userRole = '<?php echo $userRole; ?>';

                // Choose API endpoint based on user role
                let apiUrl = '/api/landing-pages.php?action=user-pages';
                if (userRole === 'admin') {
                    // Admin sees all pages from all merchants
                    apiUrl = '/api/landing-pages.php?action=admin-all-pages';
                }

                console.log('🔄 Loading pages for role:', userRole, 'URL:', apiUrl);

                const response = await fetch(apiUrl);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (data.success && data.data) {
                    const pages = data.data.pages || [];
                    displayPages(pages);
                    updatePagesStats(calculateStats(pages));
                } else {
                    showError(data.error || 'Erreur lors du chargement des pages');
                }
            } catch (error) {
                console.error('Erreur:', error);
                showError('Erreur de connexion');
            }
        }

        function displayPages(pages) {
            const container = document.getElementById('pagesContainer');

            if (pages.length === 0) {
                container.innerHTML = `
            <div class="col-12 text-center py-5">
                <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                <h5>Aucune page créée</h5>
                <p class="text-muted">Créez votre première landing page pour commencer</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createPageModal">
                    <i class="fas fa-plus me-2"></i>Créer ma première page
                </button>
            </div>
        `;
                return;
            }

            container.innerHTML = pages.map(page => `
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card page-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span class="badge bg-${getStatusColor(page.status)}">${getStatusText(page.status)}</span>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="dropdown">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="editPage(${page.id})">
                                <i class="fas fa-edit me-2"></i>Modifier
                            </a></li>
                            <li><a class="dropdown-item" href="/preview.php?id=${page.id}" target="_blank">
                                <i class="fas fa-eye me-2"></i>Prévisualiser
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="duplicatePage(${page.id})">
                                <i class="fas fa-copy me-2"></i>Dupliquer
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="#" onclick="deletePage(${page.id})">
                                <i class="fas fa-trash me-2"></i>Supprimer
                            </a></li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    <h6 class="card-title">${page.title}</h6>
                    <p class="card-text text-muted small">${page.description || 'Aucune description'}</p>
                    <div class="row text-center mt-3">
                        <div class="col-4">
                            <small class="text-muted">Vues</small>
                            <div class="fw-bold">${page.views || 0}</div>
                        </div>
                        <div class="col-4">
                            <small class="text-muted">Conversions</small>
                            <div class="fw-bold">${page.conversions || 0}</div>
                        </div>
                        <div class="col-4">
                            <small class="text-muted">Taux</small>
                            <div class="fw-bold">${page.views > 0 ? ((page.conversions / page.views) * 100).toFixed(1) : 0}%</div>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <small class="text-muted">
                        Créé le ${new Date(page.created_at).toLocaleDateString('fr-FR')}
                    </small>
                </div>
            </div>
        </div>
    `).join('');
        }

        function getStatusColor(status) {
            const colors = {
                'published': 'success',
                'draft': 'warning',
                'archived': 'secondary'
            };
            return colors[status] || 'secondary';
        }

        function getStatusText(status) {
            const texts = {
                'published': 'Publié',
                'draft': 'Brouillon',
                'archived': 'Archivé'
            };
            return texts[status] || status;
        }

        function calculateStats(pages) {
            const total = pages.length;
            const views = pages.reduce((sum, page) => sum + (parseInt(page.views) || 0), 0);
            const conversions = pages.reduce((sum, page) => sum + (parseInt(page.conversions) || 0), 0);
            const conversionRate = views > 0 ? ((conversions / views) * 100).toFixed(1) + '%' : '0%';

            return {
                total,
                views,
                conversions,
                conversion_rate: conversionRate
            };
        }

        function updatePagesStats(stats) {
            if (stats) {
                document.getElementById('totalPages').textContent = stats.total || 0;
                document.getElementById('totalViews').textContent = stats.views || 0;
                document.getElementById('totalConversions').textContent = stats.conversions || 0;
                document.getElementById('conversionRate').textContent = stats.conversion_rate || '0%';
            }
        }

        function showError(message) {
            // Afficher un message d'erreur
            console.error(message);
        }
    </script>
<?php endif; ?>
