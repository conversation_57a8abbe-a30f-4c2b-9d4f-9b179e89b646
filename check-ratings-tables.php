<?php
require_once 'dashboard/includes/database.php';

try {
    $db = connectDB();
    echo 'Checking ratings system tables...' . PHP_EOL;
    
    // Check if seller_ratings table exists
    $tables = $db->query("SHOW TABLES LIKE 'seller_ratings'")->fetchAll();
    if (empty($tables)) {
        echo 'seller_ratings table does not exist. Creating...' . PHP_EOL;
        
        // Create the ratings tables
        require_once 'api/ratings.php';
        createRatingTables($db);
        echo 'Ratings tables created successfully!' . PHP_EOL;
    } else {
        echo 'seller_ratings table exists!' . PHP_EOL;
        
        // Show table structure
        echo 'Table structure:' . PHP_EOL;
        $structure = $db->query("DESCRIBE seller_ratings")->fetchAll();
        foreach ($structure as $column) {
            echo "- {$column['Field']} ({$column['Type']})" . PHP_EOL;
        }
        
        // Show sample data
        echo PHP_EOL . 'Sample ratings data:' . PHP_EOL;
        $sample = $db->query("SELECT * FROM seller_ratings LIMIT 3")->fetchAll();
        if (empty($sample)) {
            echo 'No ratings found.' . PHP_EOL;
        } else {
            foreach ($sample as $rating) {
                echo "- Order: {$rating['order_number']}, Rating: {$rating['rating']}/5, Customer: {$rating['customer_name']}" . PHP_EOL;
            }
        }
    }
    
    // Check seller_rating_summary table
    echo PHP_EOL . 'Checking seller_rating_summary table...' . PHP_EOL;
    $summaryTables = $db->query("SHOW TABLES LIKE 'seller_rating_summary'")->fetchAll();
    if (!empty($summaryTables)) {
        echo 'seller_rating_summary table exists!' . PHP_EOL;
        $summaryData = $db->query("SELECT * FROM seller_rating_summary LIMIT 3")->fetchAll();
        foreach ($summaryData as $summary) {
            echo "- Seller ID: {$summary['seller_id']}, Avg Rating: {$summary['average_rating']}, Total: {$summary['total_ratings']}" . PHP_EOL;
        }
    }
    
    // Check orders table structure for delivery status
    echo PHP_EOL . 'Checking orders table for delivery status...' . PHP_EOL;
    $orderStructure = $db->query("DESCRIBE orders")->fetchAll();
    $hasDeliveredStatus = false;
    foreach ($orderStructure as $column) {
        if ($column['Field'] === 'status') {
            echo "Status column type: {$column['Type']}" . PHP_EOL;
            if (strpos($column['Type'], 'delivered') !== false) {
                $hasDeliveredStatus = true;
            }
        }
    }
    
    if ($hasDeliveredStatus) {
        echo 'Orders table has delivered status - good for rating system!' . PHP_EOL;
    } else {
        echo 'Orders table may need delivered status added.' . PHP_EOL;
    }
    
} catch (Exception $e) {
    echo 'Error: ' . $e->getMessage() . PHP_EOL;
}
?>
