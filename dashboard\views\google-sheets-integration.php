<?php

/**
 * Dashboard pour la gestion des intégrations Google Sheets des marchands
 */

// Vérification de l'authentification
if (!isset($_SESSION['user']) || !in_array($_SESSION['user']['role'], ['merchant', 'admin'])) {
    header('Location: login.php');
    exit;
}

$user = $_SESSION['user'];
$store_id = $_GET['store_id'] ?? null;
?>

<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Intégrations Google Sheets</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/themes/prism.min.css" rel="stylesheet">
    <style>
        .integration-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .integration-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .integration-type-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 1rem;
        }

        .orders-icon {
            background: #28a745;
        }

        .events-icon {
            background: #6f42c1;
        }

        .products-icon {
            background: #fd7e14;
        }

        .customers-icon {
            background: #20c997;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .status-syncing {
            background: #fff3cd;
            color: #856404;
        }

        .status-error {
            background: #f5c6cb;
            color: #721c24;
        }

        .script-preview {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 1rem;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }

        .sync-options {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        /* Help Section Styles */
        .help-steps {
            padding-left: 1.2rem;
        }

        .help-steps li {
            margin-bottom: 0.5rem;
            line-height: 1.5;
        }

        .help-features {
            list-style: none;
            padding-left: 0;
        }

        .help-features li {
            margin-bottom: 0.5rem;
            padding-left: 1.5rem;
            position: relative;
        }

        .help-features li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }

        .help-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            height: 100%;
            border-left: 4px solid #007bff;
        }

        .code-example {
            background: #e9ecef;
            border-radius: 4px;
            padding: 0.5rem;
            margin-top: 0.5rem;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
        }

        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 1rem;
            overflow-x: auto;
        }

        .code-block pre {
            margin: 0;
            color: inherit;
        }

        .max-height-200 {
            max-height: 200px;
        }

        .product-item {
            transition: background-color 0.2s;
        }

        .product-item:hover {
            background-color: #f8f9fa;
        }

        .product-item label {
            cursor: pointer;
            width: 100%;
            margin-bottom: 0;
            padding: 5px;
            border-radius: 3px;
        }

        .code-block code {
            color: inherit;
            background: none;
            padding: 0;
        }

        .troubleshooting .trouble-item {
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #dee2e6;
        }

        .troubleshooting .trouble-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .troubleshooting strong {
            color: #dc3545;
        }
    </style>
</head>

<body>
    <div class="container-fluid">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1><i class="fab fa-google me-2"></i>Intégrations Google Sheets</h1>
                <p class="text-muted">Générez des scripts pour synchroniser vos données avec Google Sheets</p>
            </div>
            <div>
                <button class="btn btn-info me-2" data-bs-toggle="collapse" data-bs-target="#helpSection">
                    <i class="fas fa-question-circle me-2"></i>Aide & Documentation
                </button>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createIntegrationModal">
                    <i class="fas fa-plus me-2"></i>Nouvelle Intégration
                </button>
            </div>
        </div>

        <!-- Help Documentation Section -->
        <div class="collapse mb-4" id="helpSection">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-book me-2"></i>Guide d'utilisation - Intégration Google Sheets</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-rocket me-2 text-primary"></i>Démarrage rapide</h6>
                            <ol class="help-steps">
                                <li><strong>Choisissez un type d'intégration</strong> (Commandes, Événements, Produits, ou Clients)</li>
                                <li><strong>Cliquez sur "Créer"</strong> pour générer automatiquement le script Google Apps Script</li>
                                <li><strong>Téléchargez le fichier .gs</strong> qui contient le code personnalisé</li>
                                <li><strong>Ouvrez Google Sheets</strong> et créez une nouvelle feuille de calcul</li>
                                <li><strong>Accédez à Extensions > Apps Script</strong> dans Google Sheets</li>
                                <li><strong>Collez le code téléchargé</strong> dans l'éditeur Apps Script</li>
                                <li><strong>Sauvegardez et exécutez</strong> la fonction d'initialisation</li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-cogs me-2 text-success"></i>Fonctionnalités avancées</h6>
                            <ul class="help-features">
                                <li><strong>Synchronisation bidirectionnelle</strong> - Les modifications dans Google Sheets sont automatiquement synchronisées</li>
                                <li><strong>Workflow de confirmation</strong> - Colonnes spéciales pour validation par agents</li>
                                <li><strong>Mise à jour en temps réel</strong> - Webhook automatique pour les changements</li>
                                <li><strong>Filtrage intelligent</strong> - Seules les données pertinentes sont synchronisées</li>
                                <li><strong>Historique complet</strong> - Suivi de toutes les modifications</li>
                            </ul>
                        </div>
                    </div>

                    <hr class="my-4">

                    <div class="row">
                        <div class="col-md-4">
                            <div class="help-card">
                                <h6><i class="fas fa-shopping-cart me-2 text-warning"></i>Intégration Commandes</h6>
                                <p class="small text-muted">Synchronise automatiquement toutes les nouvelles commandes avec une colonne de confirmation pour vos agents.</p>
                                <div class="code-example">
                                    <strong>Colonnes générées :</strong><br>
                                    <code>ID, Client, Email, Produit, Montant, Statut, Date, Confirmé</code>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="help-card">
                                <h6><i class="fas fa-calendar me-2 text-info"></i>Intégration Événements</h6>
                                <p class="small text-muted">Gère les événements et rendez-vous avec workflow de validation.</p>
                                <div class="code-example">
                                    <strong>Colonnes générées :</strong><br>
                                    <code>ID, Titre, Date, Lieu, Participants, Statut, Validé</code>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="help-card">
                                <h6><i class="fas fa-box me-2 text-success"></i>Intégration Produits</h6>
                                <p class="small text-muted">Synchronise votre catalogue produits pour gestion centralisée.</p>
                                <div class="code-example">
                                    <strong>Colonnes générées :</strong><br>
                                    <code>ID, Nom, Prix, Stock, Catégorie, Actif</code>
                                </div>
                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-code me-2 text-danger"></i>Exemple de code Apps Script</h6>
                            <div class="code-block">
                                <pre><code>function initializeSheet() {
  // Code généré automatiquement
  const sheet = SpreadsheetApp.getActiveSheet();

  // Configuration des en-têtes
  const headers = ['ID', 'Client', 'Email', 'Montant', 'Confirmé'];
  sheet.getRange(1, 1, 1, headers.length).setValues([headers]);

  // Synchronisation avec votre système
  syncData();
}

function onEdit(e) {
  // Webhook automatique lors des modifications
  if (e.range.getColumn() === 5) { // Colonne "Confirmé"
    updateOrderStatus(e.range.getRow(), e.value);
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-exclamation-triangle me-2 text-warning"></i>Dépannage</h6>
                            <div class="troubleshooting">
                                <div class="trouble-item">
                                    <strong>Erreur d'autorisation :</strong>
                                    <p class="small">Assurez-vous d'autoriser le script à accéder à vos feuilles Google Sheets lors de la première exécution.</p>
                                </div>
                                <div class="trouble-item">
                                    <strong>Synchronisation lente :</strong>
                                    <p class="small">La première synchronisation peut prendre quelques minutes selon le volume de données.</p>
                                </div>
                                <div class="trouble-item">
                                    <strong>Données manquantes :</strong>
                                    <p class="small">Vérifiez que l'URL de webhook est correctement configurée dans le script.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info mt-3">
                        <i class="fas fa-lightbulb me-2"></i>
                        <strong>Conseil :</strong> Commencez par une intégration simple (Produits) pour vous familiariser avec le système avant de passer aux intégrations plus complexes (Commandes avec workflow).
                    </div>
                </div>
            </div>
        </div>

        <!-- Integration Types -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="integration-card text-center">
                    <div class="integration-type-icon orders-icon mx-auto">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <h5>Commandes</h5>
                    <p class="text-muted">Synchronisation des commandes pour confirmation par agents</p>
                    <button class="btn btn-outline-success btn-sm" onclick="createIntegration('orders')">
                        <i class="fas fa-plus me-1"></i>Créer
                    </button>
                </div>
            </div>

            <div class="col-md-3">
                <div class="integration-card text-center">
                    <div class="integration-type-icon events-icon mx-auto">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <h5>Événements</h5>
                    <p class="text-muted">Gestion des inscriptions aux événements</p>
                    <button class="btn btn-outline-primary btn-sm" onclick="createIntegration('events')">
                        <i class="fas fa-plus me-1"></i>Créer
                    </button>
                </div>
            </div>

            <div class="col-md-3">
                <div class="integration-card text-center">
                    <div class="integration-type-icon products-icon mx-auto">
                        <i class="fas fa-box"></i>
                    </div>
                    <h5>Produits</h5>
                    <p class="text-muted">Export et synchronisation du catalogue</p>
                    <button class="btn btn-outline-warning btn-sm" onclick="createIntegration('products')">
                        <i class="fas fa-plus me-1"></i>Créer
                    </button>
                </div>
            </div>

            <div class="col-md-3">
                <div class="integration-card text-center">
                    <div class="integration-type-icon customers-icon mx-auto">
                        <i class="fas fa-users"></i>
                    </div>
                    <h5>Clients</h5>
                    <p class="text-muted">Base de données clients synchronisée</p>
                    <button class="btn btn-outline-info btn-sm" onclick="createIntegration('customers')">
                        <i class="fas fa-plus me-1"></i>Créer
                    </button>
                </div>
            </div>
        </div>

        <!-- Active Integrations -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-link me-2"></i>Intégrations Actives</h5>
            </div>
            <div class="card-body">
                <div id="integrations-list">
                    <!-- Integrations will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Create Integration Modal -->
    <div class="modal fade" id="createIntegrationModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fab fa-google me-2"></i>
                        <span id="integration-modal-title">Nouvelle Intégration Google Sheets</span>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="integration-form">
                        <input type="hidden" id="integration-type" name="integration_type">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Nom de l'intégration *</label>
                                    <input type="text" class="form-control" name="integration_name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Fréquence de synchronisation</label>
                                    <select class="form-select" name="sync_frequency">
                                        <option value="manual">Manuel</option>
                                        <option value="realtime">Temps réel</option>
                                        <option value="hourly">Toutes les heures</option>
                                        <option value="daily">Quotidien</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="sync-options">
                            <h6><i class="fas fa-cog me-2"></i>Options de Synchronisation</h6>
                            <div id="sync-options-content">
                                <!-- Options will be populated based on integration type -->
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">URL Google Sheets (optionnel)</label>
                            <input type="url" class="form-control" name="sheet_url" placeholder="https://docs.google.com/spreadsheets/d/...">
                            <small class="form-text text-muted">Si vous avez déjà créé une feuille Google Sheets</small>
                        </div>
                    </form>

                    <div class="mt-4">
                        <h6><i class="fas fa-code me-2"></i>Aperçu du Script</h6>
                        <div id="script-preview" class="script-preview">
                            <!-- Script preview will appear here -->
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-info" onclick="previewScript()">
                        <i class="fas fa-eye me-2"></i>Aperçu
                    </button>
                    <button type="button" class="btn btn-primary" onclick="generateAndDownloadScript()">
                        <i class="fas fa-download me-2"></i>Générer & Télécharger
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/components/prism-javascript.min.js"></script>
    <script>
        const storeId = <?php echo json_encode($store_id); ?>;
        let currentIntegrationType = null;

        document.addEventListener('DOMContentLoaded', function() {
            loadIntegrations();
        });

        function createIntegration(type) {
            currentIntegrationType = type;
            document.getElementById('integration-type').value = type;

            const titles = {
                'orders': 'Intégration Commandes - Confirmation Agents',
                'events': 'Intégration Événements - Gestion Inscriptions',
                'products': 'Intégration Produits - Synchronisation Catalogue',
                'customers': 'Intégration Clients - Base de Données'
            };

            document.getElementById('integration-modal-title').textContent = titles[type];
            document.querySelector('[name="integration_name"]').value = titles[type];

            populateSyncOptions(type);

            const modal = new bootstrap.Modal(document.getElementById('createIntegrationModal'));
            modal.show();

            // Load products if orders integration
            if (type === 'orders') {
                loadProductsForSelection();
                setupProductSelectionHandlers();
            }
        }

        function populateSyncOptions(type) {
            const container = document.getElementById('sync-options-content');

            const options = {
                'orders': `
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="include-customer-details" checked>
                        <label class="form-check-label" for="include-customer-details">
                            Inclure les détails clients complets
                        </label>
                    </div>
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="include-shipping-info" checked>
                        <label class="form-check-label" for="include-shipping-info">
                            Inclure les informations de livraison
                        </label>
                    </div>
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="agent-confirmation-workflow" checked>
                        <label class="form-check-label" for="agent-confirmation-workflow">
                            <strong>Workflow de confirmation par agents</strong>
                        </label>
                    </div>
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="auto-status-sync" checked>
                        <label class="form-check-label" for="auto-status-sync">
                            Synchronisation automatique des statuts
                        </label>
                    </div>

                    <div class="mt-3">
                        <label class="form-label"><strong>Sélection des produits</strong></label>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="radio" name="product-selection" id="all-products" value="all" checked>
                            <label class="form-check-label" for="all-products">
                                Tous les produits
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="radio" name="product-selection" id="specific-products" value="specific">
                            <label class="form-check-label" for="specific-products">
                                Produits spécifiques
                            </label>
                        </div>
                        <div id="product-selector" class="mt-2" style="display: none;">
                            <div class="border rounded p-3 bg-light">
                                <div class="mb-2">
                                    <input type="text" class="form-control form-control-sm" id="product-search" placeholder="Rechercher un produit...">
                                </div>
                                <div id="products-list" class="max-height-200 overflow-auto">
                                    <!-- Products will be loaded here -->
                                    <div class="text-center py-2">
                                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                                            <span class="visually-hidden">Chargement...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `,
                'events': `
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="include-event-details" checked>
                        <label class="form-check-label" for="include-event-details">
                            Inclure les détails de l'événement
                        </label>
                    </div>
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="agent-confirmation-workflow" checked>
                        <label class="form-check-label" for="agent-confirmation-workflow">
                            <strong>Workflow de confirmation par agents</strong>
                        </label>
                    </div>
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="attendance-tracking" checked>
                        <label class="form-check-label" for="attendance-tracking">
                            Suivi de présence
                        </label>
                    </div>
                `,
                'products': `
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="include-inventory" checked>
                        <label class="form-check-label" for="include-inventory">
                            Inclure les niveaux de stock
                        </label>
                    </div>
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="include-pricing" checked>
                        <label class="form-check-label" for="include-pricing">
                            Inclure les informations de prix
                        </label>
                    </div>
                `,
                'customers': `
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="include-order-history" checked>
                        <label class="form-check-label" for="include-order-history">
                            Inclure l'historique des commandes
                        </label>
                    </div>
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="include-contact-info" checked>
                        <label class="form-check-label" for="include-contact-info">
                            Inclure les informations de contact
                        </label>
                    </div>
                `
            };

            container.innerHTML = options[type] || '';
        }

        async function loadIntegrations() {
            try {
                let url = '../api/google-sheets.php?action=list_integrations';
                if (storeId) {
                    url += `&store_id=${storeId}`;
                }

                const response = await fetch(url);
                const data = await response.json();

                if (data.success) {
                    displayIntegrations(data.data || []);
                }
            } catch (error) {
                console.error('Error loading integrations:', error);
            }
        }

        function displayIntegrations(integrations) {
            const container = document.getElementById('integrations-list');

            if (integrations.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fab fa-google fa-3x text-muted mb-3"></i>
                        <h5>Aucune intégration configurée</h5>
                        <p class="text-muted">Créez votre première intégration Google Sheets</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = integrations.map(integration => `
                <div class="integration-card">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-${getIntegrationIcon(integration.integration_type)} me-2"></i>
                                <h6 class="mb-0">${integration.integration_name}</h6>
                                <span class="status-badge status-${integration.sync_enabled ? 'active' : 'inactive'} ms-2">
                                    ${integration.sync_enabled ? 'Actif' : 'Inactif'}
                                </span>
                            </div>
                            <p class="text-muted mb-2">${getIntegrationDescription(integration.integration_type)}</p>
                            <div class="small text-muted">
                                <i class="fas fa-clock me-1"></i>
                                Dernière sync: ${integration.last_sync_at || 'Jamais'}
                                ${integration.sheet_url ? `<br><i class="fas fa-external-link-alt me-1"></i><a href="${integration.sheet_url}" target="_blank">Voir la feuille</a>` : ''}
                            </div>
                        </div>
                        <div class="text-end">
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="editIntegration(${integration.id})">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-success" onclick="downloadScript(${integration.id})">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="btn btn-outline-warning" onclick="syncNow(${integration.id})">
                                    <i class="fas fa-sync"></i>
                                </button>
                                <button class="btn btn-outline-danger" onclick="deleteIntegration(${integration.id})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function getIntegrationIcon(type) {
            const icons = {
                'orders': 'shopping-cart',
                'events': 'calendar-alt',
                'products': 'box',
                'customers': 'users'
            };
            return icons[type] || 'file';
        }

        function getIntegrationDescription(type) {
            const descriptions = {
                'orders': 'Synchronisation des commandes avec workflow de confirmation par agents',
                'events': 'Gestion des inscriptions aux événements et suivi de présence',
                'products': 'Export et synchronisation du catalogue produits',
                'customers': 'Base de données clients avec historique des commandes'
            };
            return descriptions[type] || 'Intégration personnalisée';
        }

        async function previewScript() {
            const form = document.getElementById('integration-form');
            const formData = new FormData(form);

            const config = {
                integration_type: formData.get('integration_type'),
                integration_name: formData.get('integration_name'),
                sync_frequency: formData.get('sync_frequency'),
                options: getSelectedOptions()
            };

            try {
                let url = '../api/google-sheets.php?action=preview_script';
                if (storeId) {
                    url += `&store_id=${storeId}`;
                }

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer demo_token'
                    },
                    body: JSON.stringify(config)
                });

                const data = await response.json();

                if (data.success) {
                    document.getElementById('script-preview').innerHTML =
                        `<pre><code class="language-javascript">${escapeHtml(data.script_content)}</code></pre>`;

                    // Highlight syntax
                    Prism.highlightAll();
                } else {
                    showNotification(data.message, 'error');
                }
            } catch (error) {
                console.error('Error previewing script:', error);
                showNotification('Erreur lors de l\'aperçu', 'error');
            }
        }

        async function generateAndDownloadScript() {
            const form = document.getElementById('integration-form');
            const formData = new FormData(form);

            const config = {
                integration_type: formData.get('integration_type'),
                integration_name: formData.get('integration_name'),
                sync_frequency: formData.get('sync_frequency'),
                sheet_url: formData.get('sheet_url'),
                options: getSelectedOptions()
            };

            try {
                let url = '../api/google-sheets.php?action=create_integration';
                if (storeId) {
                    url += `&store_id=${storeId}`;
                }

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer demo_token'
                    },
                    body: JSON.stringify(config)
                });

                const data = await response.json();

                if (data.success) {
                    // Télécharger le script
                    downloadScriptFile(data.script_content, config.integration_name);

                    // Fermer le modal et recharger la liste
                    bootstrap.Modal.getInstance(document.getElementById('createIntegrationModal')).hide();
                    loadIntegrations();

                    showNotification('Intégration créée et script téléchargé avec succès!', 'success');
                } else {
                    showNotification(data.message, 'error');
                }
            } catch (error) {
                console.error('Error generating script:', error);
                showNotification('Erreur lors de la génération', 'error');
            }
        }

        function getSelectedOptions() {
            const options = {};
            const checkboxes = document.querySelectorAll('#sync-options-content input[type="checkbox"]');

            checkboxes.forEach(checkbox => {
                options[checkbox.id] = checkbox.checked;
            });

            // Add product selection data for orders integration
            if (currentIntegrationType === 'orders') {
                const productSelection = getSelectedProducts();
                options.product_selection = productSelection.selection;
                options.selected_products = productSelection.products;
            }

            return options;
        }

        function downloadScriptFile(content, name) {
            const element = document.createElement('a');
            element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(content));
            element.setAttribute('download', `${name.replace(/\s+/g, '_')}_sync_script.gs`);
            element.style.display = 'none';
            document.body.appendChild(element);
            element.click();
            document.body.removeChild(element);
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function showNotification(message, type = 'info') {
            const alertClass = type === 'error' ? 'alert-danger' :
                type === 'success' ? 'alert-success' :
                type === 'warning' ? 'alert-warning' : 'alert-info';

            const alert = document.createElement('div');
            alert.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
            alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alert);

            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 5000);
        }

        async function editIntegration(integrationId) {
            // Load integration data and populate form for editing
            showNotification('Fonction d\'édition en cours de développement', 'info');
        }

        async function downloadScript(integrationId) {
            try {
                const response = await fetch(`../api/google-sheets.php?action=get_script&id=${integrationId}`);
                const data = await response.json();

                if (data.success) {
                    downloadScriptFile(data.script_content, `integration_${integrationId}`);
                    showNotification('Script téléchargé avec succès', 'success');
                } else {
                    showNotification(data.message, 'error');
                }
            } catch (error) {
                console.error('Error downloading script:', error);
                showNotification('Erreur lors du téléchargement', 'error');
            }
        }

        async function syncNow(integrationId) {
            try {
                const response = await fetch(`../api/google-sheets.php?action=sync_now&id=${integrationId}`, {
                    method: 'POST'
                });
                const data = await response.json();

                if (data.success) {
                    showNotification('Synchronisation lancée avec succès', 'success');
                    // Reload integrations to show updated sync status
                    setTimeout(() => loadIntegrations(), 2000);
                } else {
                    showNotification(data.message, 'error');
                }
            } catch (error) {
                console.error('Error syncing:', error);
                showNotification('Erreur lors de la synchronisation', 'error');
            }
        }

        async function deleteIntegration(integrationId) {
            if (!confirm('Êtes-vous sûr de vouloir supprimer cette intégration ?')) {
                return;
            }

            try {
                const response = await fetch(`../api/google-sheets.php?action=delete_integration&id=${integrationId}`, {
                    method: 'DELETE'
                });
                const data = await response.json();

                if (data.success) {
                    showNotification('Intégration supprimée avec succès', 'success');
                    loadIntegrations();
                } else {
                    showNotification(data.message, 'error');
                }
            } catch (error) {
                console.error('Error deleting integration:', error);
                showNotification('Erreur lors de la suppression', 'error');
            }
        }

        // Product Selection Functions
        function setupProductSelectionHandlers() {
            // Handle product selection radio buttons
            document.querySelectorAll('input[name="product-selection"]').forEach(radio => {
                radio.addEventListener('change', function() {
                    const productSelector = document.getElementById('product-selector');
                    if (this.value === 'specific') {
                        productSelector.style.display = 'block';
                    } else {
                        productSelector.style.display = 'none';
                    }
                });
            });

            // Handle product search
            const searchInput = document.getElementById('product-search');
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    filterProducts(this.value);
                });
            }
        }

        async function loadProductsForSelection() {
            try {
                const storeId = getStoreId();
                const response = await fetch(`/api/products.php?action=list&store_id=${storeId}`, {
                    headers: {
                        'Authorization': 'Bearer demo_token'
                    }
                });

                const data = await response.json();

                if (data.success) {
                    displayProductsForSelection(data.products);
                } else {
                    document.getElementById('products-list').innerHTML =
                        '<div class="text-center py-2 text-muted">Aucun produit trouvé</div>';
                }
            } catch (error) {
                console.error('Error loading products:', error);
                document.getElementById('products-list').innerHTML =
                    '<div class="text-center py-2 text-danger">Erreur lors du chargement</div>';
            }
        }

        function displayProductsForSelection(products) {
            const container = document.getElementById('products-list');

            if (products.length === 0) {
                container.innerHTML = '<div class="text-center py-2 text-muted">Aucun produit trouvé</div>';
                return;
            }

            const productsHtml = products.map(product => `
                <div class="form-check mb-1 product-item" data-product-name="${product.name.toLowerCase()}">
                    <input class="form-check-input" type="checkbox" id="product-${product.id}" value="${product.id}">
                    <label class="form-check-label d-flex align-items-center" for="product-${product.id}">
                        <div class="me-2">
                            ${product.image_url ?
                                `<img src="${product.image_url}" alt="${product.name}" class="rounded" style="width: 30px; height: 30px; object-fit: cover;">` :
                                '<div class="bg-light rounded d-flex align-items-center justify-content-center" style="width: 30px; height: 30px;"><i class="fas fa-image text-muted small"></i></div>'
                            }
                        </div>
                        <div class="flex-grow-1">
                            <div class="fw-bold small">${product.name}</div>
                            <div class="text-muted small">${formatPrice(product.price)} DZD</div>
                        </div>
                    </label>
                </div>
            `).join('');

            container.innerHTML = productsHtml;
        }

        function filterProducts(searchTerm) {
            const productItems = document.querySelectorAll('.product-item');
            const term = searchTerm.toLowerCase();

            productItems.forEach(item => {
                const productName = item.dataset.productName;
                if (productName.includes(term)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        function getSelectedProducts() {
            const productSelection = document.querySelector('input[name="product-selection"]:checked');
            if (!productSelection) return {
                selection: 'all',
                products: []
            };

            if (productSelection.value === 'all') {
                return {
                    selection: 'all',
                    products: []
                };
            } else {
                const selectedProducts = [];
                document.querySelectorAll('#products-list input[type="checkbox"]:checked').forEach(checkbox => {
                    selectedProducts.push(parseInt(checkbox.value));
                });
                return {
                    selection: 'specific',
                    products: selectedProducts
                };
            }
        }

        function formatPrice(price) {
            return new Intl.NumberFormat('fr-DZ', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(price);
        }
    </script>
</body>

</html>
