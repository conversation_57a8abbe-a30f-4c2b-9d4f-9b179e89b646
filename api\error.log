[02-Aug-2025 09:32:45 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 09:32:45 UTC] Erreur lors de l'exécution de la requête: SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.user_id' in 'on clause'
[02-Aug-2025 09:32:45 UTC] Exception générale: Erreur lors de la récupération des stores: SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.user_id' in 'on clause'
[02-Aug-2025 09:34:04 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 09:34:04 UTC] Erreur lors de l'exécution de la requête: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'm.first_name' in 'field list'
[02-Aug-2025 09:34:04 UTC] Exception générale: <PERSON><PERSON><PERSON> lors de la récupération des stores: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'm.first_name' in 'field list'
[02-Aug-2025 09:35:20 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 09:37:51 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 09:42:20 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 09:42:22 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 09:43:33 UTC] Exception générale: Action non valide
[02-Aug-2025 11:07:51 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:07:52 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:15:11 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:15:11 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:15:12 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:15:12 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:15:21 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:15:21 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:16:11 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:16:11 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:19:56 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:19:56 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 10:20:04 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 10:20:04 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 10:43:03 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 10:43:04 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 11:43:06 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'subscription_plan_id' in 'field list'
[02-Aug-2025 11:43:06 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:43:06 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'subscription_plan_id' in 'field list'
[02-Aug-2025 11:43:06 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:43:10 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'subscription_plan_id' in 'field list'
[02-Aug-2025 11:43:10 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:43:10 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'subscription_plan_id' in 'field list'
[02-Aug-2025 11:43:10 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:43:10 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'subscription_plan_id' in 'field list'
[02-Aug-2025 11:43:11 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:43:11 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'subscription_plan_id' in 'field list'
[02-Aug-2025 11:43:11 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:43:11 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'subscription_plan_id' in 'field list'
[02-Aug-2025 11:43:11 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:43:11 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'subscription_plan_id' in 'field list'
[02-Aug-2025 11:43:11 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:43:12 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'subscription_plan_id' in 'field list'
[02-Aug-2025 11:43:12 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:43:12 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'subscription_plan_id' in 'field list'
[02-Aug-2025 11:43:12 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:43:17 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'subscription_plan_id' in 'field list'
[02-Aug-2025 11:43:17 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:43:17 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'subscription_plan_id' in 'field list'
[02-Aug-2025 11:43:17 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:43:24 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'subscription_plan_id' in 'field list'
[02-Aug-2025 11:43:24 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:43:24 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'subscription_plan_id' in 'field list'
[02-Aug-2025 11:43:24 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.name' in 'field list'
[02-Aug-2025 11:43:43 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'subscription_plan_id' in 'field list'
[02-Aug-2025 10:46:47 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 10:46:47 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 10:46:51 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 10:46:56 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 11:59:09 Africa/Algiers] Token reçu : 
[02-Aug-2025 11:59:09 Africa/Algiers] Token non reconnu, utilisation de l'ID par défaut
[02-Aug-2025 11:59:09 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 11:59:09 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(118): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 11:59:09 Africa/Algiers] Début de getLandingPages avec userId: 1
[02-Aug-2025 11:59:09 Africa/Algiers] Préparation de la requête SQL
[02-Aug-2025 11:59:09 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 11:59:09 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                s.store_name as store_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN stores s ON lp.store_id = s.id
            LEFT JOIN merchants m ON s.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 11:59:09 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.store_id' in 'on clause'
[02-Aug-2025 12:02:55 Africa/Algiers] Token reçu : 
[02-Aug-2025 12:02:56 Africa/Algiers] Token non reconnu, utilisation de l'ID par défaut
[02-Aug-2025 12:02:56 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:02:56 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(118): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:02:56 Africa/Algiers] Début de getLandingPages avec userId: 1
[02-Aug-2025 12:02:56 Africa/Algiers] Préparation de la requête SQL
[02-Aug-2025 12:02:56 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:02:56 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                s.store_name as store_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN stores s ON lp.store_id = s.id
            LEFT JOIN merchants m ON s.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:02:57 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.store_id' in 'on clause'
[02-Aug-2025 12:03:38 Africa/Algiers] Token reçu : demo_token
[02-Aug-2025 12:03:38 Africa/Algiers] Utilisation du token de démo
[02-Aug-2025 12:03:38 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:03:38 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(118): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:03:38 Africa/Algiers] Début de getLandingPages avec userId: 1
[02-Aug-2025 12:03:38 Africa/Algiers] Préparation de la requête SQL
[02-Aug-2025 12:03:38 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:03:38 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                s.store_name as store_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN stores s ON lp.store_id = s.id
            LEFT JOIN merchants m ON s.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:03:38 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.store_id' in 'on clause'
[02-Aug-2025 12:03:38 Africa/Algiers] Token reçu : demo_token
[02-Aug-2025 12:03:38 Africa/Algiers] Utilisation du token de démo
[02-Aug-2025 12:03:38 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:03:38 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(118): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:03:38 Africa/Algiers] Début de getLandingPages avec userId: 1
[02-Aug-2025 12:03:38 Africa/Algiers] Préparation de la requête SQL
[02-Aug-2025 12:03:38 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:03:38 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                s.store_name as store_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN stores s ON lp.store_id = s.id
            LEFT JOIN merchants m ON s.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:03:38 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.store_id' in 'on clause'
[02-Aug-2025 12:06:28 Africa/Algiers] Token reçu : 
[02-Aug-2025 12:06:28 Africa/Algiers] Token non reconnu, utilisation de l'ID par défaut
[02-Aug-2025 12:06:28 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:06:28 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(118): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:06:28 Africa/Algiers] Début de getLandingPages avec userId: 1
[02-Aug-2025 12:06:28 Africa/Algiers] Préparation de la requête SQL
[02-Aug-2025 12:06:28 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:06:28 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                s.store_name as store_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN stores s ON lp.store_id = s.id
            LEFT JOIN merchants m ON s.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:06:28 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.store_id' in 'on clause'
[02-Aug-2025 12:07:02 Africa/Algiers] Token reçu : 
[02-Aug-2025 12:07:02 Africa/Algiers] Token non reconnu, utilisation de l'ID par défaut
[02-Aug-2025 12:07:02 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:07:02 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(118): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:07:02 Africa/Algiers] Début de getLandingPages avec userId: 1
[02-Aug-2025 12:07:02 Africa/Algiers] Préparation de la requête SQL
[02-Aug-2025 12:07:02 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:07:02 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                s.store_name as store_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN stores s ON lp.store_id = s.id
            LEFT JOIN merchants m ON s.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:07:02 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.store_id' in 'on clause'
[02-Aug-2025 12:09:20 Africa/Algiers] Token reçu : 
[02-Aug-2025 12:09:22 Africa/Algiers] Token non reconnu, utilisation de l'ID par défaut
[02-Aug-2025 12:09:22 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:09:22 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(118): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:09:22 Africa/Algiers] Début de getLandingPages avec userId: 1
[02-Aug-2025 12:09:22 Africa/Algiers] Préparation de la requête SQL
[02-Aug-2025 12:09:22 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:09:22 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                s.store_name as store_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN stores s ON lp.store_id = s.id
            LEFT JOIN merchants m ON s.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:09:22 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.store_id' in 'on clause'
[02-Aug-2025 12:09:23 Africa/Algiers] Token reçu : 
[02-Aug-2025 12:09:23 Africa/Algiers] Token non reconnu, utilisation de l'ID par défaut
[02-Aug-2025 12:09:23 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:09:23 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(118): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:09:23 Africa/Algiers] Début de getLandingPages avec userId: 1
[02-Aug-2025 12:09:23 Africa/Algiers] Préparation de la requête SQL
[02-Aug-2025 12:09:23 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:09:23 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                s.store_name as store_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN stores s ON lp.store_id = s.id
            LEFT JOIN merchants m ON s.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:09:23 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.store_id' in 'on clause'
[02-Aug-2025 12:09:25 Africa/Algiers] Token reçu : 
[02-Aug-2025 12:09:25 Africa/Algiers] Token non reconnu, utilisation de l'ID par défaut
[02-Aug-2025 12:09:25 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:09:25 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(118): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:09:25 Africa/Algiers] Début de getLandingPages avec userId: 1
[02-Aug-2025 12:09:25 Africa/Algiers] Préparation de la requête SQL
[02-Aug-2025 12:09:25 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:09:25 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                s.store_name as store_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN stores s ON lp.store_id = s.id
            LEFT JOIN merchants m ON s.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:09:25 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.store_id' in 'on clause'
[02-Aug-2025 12:09:26 Africa/Algiers] Token reçu : 
[02-Aug-2025 12:09:26 Africa/Algiers] Token non reconnu, utilisation de l'ID par défaut
[02-Aug-2025 12:09:26 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:09:26 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(118): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:09:26 Africa/Algiers] Début de getLandingPages avec userId: 1
[02-Aug-2025 12:09:26 Africa/Algiers] Préparation de la requête SQL
[02-Aug-2025 12:09:26 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:09:26 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                s.store_name as store_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN stores s ON lp.store_id = s.id
            LEFT JOIN merchants m ON s.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:09:26 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.store_id' in 'on clause'
[02-Aug-2025 12:09:38 Africa/Algiers] Token reçu : demo_token
[02-Aug-2025 12:09:38 Africa/Algiers] Utilisation du token de démo
[02-Aug-2025 12:09:38 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:09:38 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(118): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:09:38 Africa/Algiers] Début de getLandingPages avec userId: 1
[02-Aug-2025 12:09:38 Africa/Algiers] Préparation de la requête SQL
[02-Aug-2025 12:09:38 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:09:38 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                s.store_name as store_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN stores s ON lp.store_id = s.id
            LEFT JOIN merchants m ON s.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:09:38 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.store_id' in 'on clause'
[02-Aug-2025 12:09:38 Africa/Algiers] Token reçu : demo_token
[02-Aug-2025 12:09:38 Africa/Algiers] Utilisation du token de démo
[02-Aug-2025 12:09:38 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:09:38 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(118): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:09:38 Africa/Algiers] Début de getLandingPages avec userId: 1
[02-Aug-2025 12:09:38 Africa/Algiers] Préparation de la requête SQL
[02-Aug-2025 12:09:38 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:09:38 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                s.store_name as store_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN stores s ON lp.store_id = s.id
            LEFT JOIN merchants m ON s.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:09:38 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.store_id' in 'on clause'
[02-Aug-2025 12:09:50 Africa/Algiers] Token reçu : demo_token
[02-Aug-2025 12:09:50 Africa/Algiers] Utilisation du token de démo
[02-Aug-2025 12:09:50 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:09:50 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(118): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:09:50 Africa/Algiers] Début de getLandingPages avec userId: 1
[02-Aug-2025 12:09:50 Africa/Algiers] Préparation de la requête SQL
[02-Aug-2025 12:09:50 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:09:50 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                s.store_name as store_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN stores s ON lp.store_id = s.id
            LEFT JOIN merchants m ON s.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:09:50 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.store_id' in 'on clause'
[02-Aug-2025 12:09:50 Africa/Algiers] Token reçu : demo_token
[02-Aug-2025 12:09:51 Africa/Algiers] Utilisation du token de démo
[02-Aug-2025 12:09:51 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:09:51 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(118): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:09:51 Africa/Algiers] Début de getLandingPages avec userId: 1
[02-Aug-2025 12:09:51 Africa/Algiers] Préparation de la requête SQL
[02-Aug-2025 12:09:51 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:09:51 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                s.store_name as store_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN stores s ON lp.store_id = s.id
            LEFT JOIN merchants m ON s.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:09:51 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.store_id' in 'on clause'
[02-Aug-2025 11:11:38 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 11:11:40 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:11:43 Africa/Algiers] Token reçu : demo_token
[02-Aug-2025 12:11:43 Africa/Algiers] Utilisation du token de démo
[02-Aug-2025 12:11:43 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:11:43 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(118): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:11:43 Africa/Algiers] Début de getLandingPages avec userId: 1
[02-Aug-2025 12:11:43 Africa/Algiers] Préparation de la requête SQL
[02-Aug-2025 12:11:43 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:11:43 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                s.store_name as store_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN stores s ON lp.store_id = s.id
            LEFT JOIN merchants m ON s.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:11:43 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.store_id' in 'on clause'
[02-Aug-2025 12:11:43 Africa/Algiers] Token reçu : demo_token
[02-Aug-2025 12:11:43 Africa/Algiers] Utilisation du token de démo
[02-Aug-2025 12:11:43 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:11:43 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(118): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:11:43 Africa/Algiers] Début de getLandingPages avec userId: 1
[02-Aug-2025 12:11:43 Africa/Algiers] Préparation de la requête SQL
[02-Aug-2025 12:11:43 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:11:43 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                s.store_name as store_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN stores s ON lp.store_id = s.id
            LEFT JOIN merchants m ON s.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:11:43 Africa/Algiers] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.store_id' in 'on clause'
[02-Aug-2025 12:13:26 Africa/Algiers] Token reçu : 
[02-Aug-2025 12:13:26 Africa/Algiers] Token non reconnu, utilisation de l'ID par défaut
[02-Aug-2025 12:13:26 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:13:26 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(118): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:13:26 Africa/Algiers] Début de getLandingPages avec userId: 1
[02-Aug-2025 12:13:26 Africa/Algiers] Préparation de la requête SQL
[02-Aug-2025 12:13:26 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:13:26 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN merchants m ON lp.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND u.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:13:26 Africa/Algiers] Nombre de pages trouvées: 0
[02-Aug-2025 12:13:26 Africa/Algiers] Requête SQL exécutée avec succès
[02-Aug-2025 12:14:06 Africa/Algiers] === Début getUserIdFromToken ===
[02-Aug-2025 12:14:06 Africa/Algiers] Token reçu : null
[02-Aug-2025 12:14:06 Africa/Algiers] Headers reçus : {"Host":"localhost:8000","Connection":"keep-alive","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Trae\/1.100.3 Chrome\/132.0.6834.210 Electron\/34.5.1 Safari\/537.36","Accept":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","Sec-Fetch-Site":"none","Sec-Fetch-Mode":"navigate","Sec-Fetch-User":"?1","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US"}
[02-Aug-2025 12:14:06 Africa/Algiers] Aucun token fourni, utilisation de l'ID par défaut
[02-Aug-2025 12:14:06 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:14:06 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(128): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:14:06 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:14:06 Africa/Algiers] userId reçu: 1
[02-Aug-2025 12:14:06 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:14:06 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:14:06 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            LEFT JOIN merchants m ON lp.merchant_id = m.id
            LEFT JOIN users u ON m.email = u.email
            WHERE u.id = ? AND u.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:14:06 Africa/Algiers] Nombre de pages trouvées: 0
[02-Aug-2025 12:14:06 Africa/Algiers] Requête SQL exécutée avec succès
[02-Aug-2025 12:16:53 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:16:53 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:16:53 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:16:53 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:16:53 Africa/Algiers] === Début getUserIdFromToken ===
[02-Aug-2025 12:16:53 Africa/Algiers] Token reçu : null
[02-Aug-2025 12:16:53 Africa/Algiers] Headers reçus : {"Host":"localhost:8000","Connection":"keep-alive","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Trae\/1.100.3 Chrome\/132.0.6834.210 Electron\/34.5.1 Safari\/537.36","Accept":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","Sec-Fetch-Site":"none","Sec-Fetch-Mode":"navigate","Sec-Fetch-User":"?1","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US"}
[02-Aug-2025 12:16:53 Africa/Algiers] Aucun token fourni, utilisation de l'ID par défaut
[02-Aug-2025 12:16:53 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:16:53 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(128): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:16:53 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:16:53 Africa/Algiers] userId reçu: 1
[02-Aug-2025 12:16:53 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:16:53 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:16:53 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = ? AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:16:53 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:16:53 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = ? AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:16:53 Africa/Algiers] UserId: 1
[02-Aug-2025 12:16:53 Africa/Algiers] Nombre de pages trouvées: 0
[02-Aug-2025 12:16:53 Africa/Algiers] Aucune page trouvée
[02-Aug-2025 12:16:53 Africa/Algiers] Dernière erreur SQL: ["00000",null,null]
[02-Aug-2025 12:18:16 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:18:16 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:18:16 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:18:16 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:18:16 Africa/Algiers] === Vérification de l'authentification ===
[02-Aug-2025 12:18:16 Africa/Algiers] Headers reçus: {"Host":"localhost:8000","Connection":"keep-alive","sec-ch-ua-platform":"\"Windows\"","Authorization":"Bearer demo_token","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Brave\";v=\"138\"","sec-ch-ua-mobile":"?0","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.6","Sec-Fetch-Site":"same-origin","Sec-Fetch-Mode":"cors","Sec-Fetch-Dest":"empty","Referer":"http:\/\/localhost:8000\/dashboard.html","Accept-Encoding":"gzip, deflate, br, zstd"}
[02-Aug-2025 12:18:16 Africa/Algiers] Token extrait: demo_token
[02-Aug-2025 12:18:16 Africa/Algiers] === Début getUserIdFromToken ===
[02-Aug-2025 12:18:16 Africa/Algiers] Token reçu : demo_token
[02-Aug-2025 12:18:16 Africa/Algiers] Headers reçus : {"Host":"localhost:8000","Connection":"keep-alive","sec-ch-ua-platform":"\"Windows\"","Authorization":"Bearer demo_token","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Brave\";v=\"138\"","sec-ch-ua-mobile":"?0","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.6","Sec-Fetch-Site":"same-origin","Sec-Fetch-Mode":"cors","Sec-Fetch-Dest":"empty","Referer":"http:\/\/localhost:8000\/dashboard.html","Accept-Encoding":"gzip, deflate, br, zstd"}
[02-Aug-2025 12:18:16 Africa/Algiers] Utilisation du token de démo
[02-Aug-2025 12:18:16 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:18:16 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(131): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(38): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:18:16 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:18:16 Africa/Algiers] userId reçu: 1
[02-Aug-2025 12:18:16 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:18:16 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:18:16 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = ? AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:18:16 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:18:16 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = ? AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:18:16 Africa/Algiers] UserId: 1
[02-Aug-2025 12:18:16 Africa/Algiers] Nombre de pages trouvées: 0
[02-Aug-2025 12:18:16 Africa/Algiers] Aucune page trouvée
[02-Aug-2025 12:18:16 Africa/Algiers] Dernière erreur SQL: ["00000",null,null]
[02-Aug-2025 12:18:17 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:18:17 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:18:17 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:18:17 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:18:17 Africa/Algiers] === Vérification de l'authentification ===
[02-Aug-2025 12:18:17 Africa/Algiers] Headers reçus: {"Host":"localhost:8000","Connection":"keep-alive","sec-ch-ua-platform":"\"Windows\"","Authorization":"Bearer demo_token","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Brave\";v=\"138\"","sec-ch-ua-mobile":"?0","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.6","Sec-Fetch-Site":"same-origin","Sec-Fetch-Mode":"cors","Sec-Fetch-Dest":"empty","Referer":"http:\/\/localhost:8000\/dashboard.html","Accept-Encoding":"gzip, deflate, br, zstd"}
[02-Aug-2025 12:18:17 Africa/Algiers] Token extrait: demo_token
[02-Aug-2025 12:18:17 Africa/Algiers] === Début getUserIdFromToken ===
[02-Aug-2025 12:18:17 Africa/Algiers] Token reçu : demo_token
[02-Aug-2025 12:18:17 Africa/Algiers] Headers reçus : {"Host":"localhost:8000","Connection":"keep-alive","sec-ch-ua-platform":"\"Windows\"","Authorization":"Bearer demo_token","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Brave\";v=\"138\"","sec-ch-ua-mobile":"?0","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.6","Sec-Fetch-Site":"same-origin","Sec-Fetch-Mode":"cors","Sec-Fetch-Dest":"empty","Referer":"http:\/\/localhost:8000\/dashboard.html","Accept-Encoding":"gzip, deflate, br, zstd"}
[02-Aug-2025 12:18:17 Africa/Algiers] Utilisation du token de démo
[02-Aug-2025 12:18:17 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:18:17 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(131): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(38): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:18:17 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:18:17 Africa/Algiers] userId reçu: 1
[02-Aug-2025 12:18:17 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:18:17 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:18:17 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = ? AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:18:17 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:18:17 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = ? AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:18:17 Africa/Algiers] UserId: 1
[02-Aug-2025 12:18:17 Africa/Algiers] Nombre de pages trouvées: 0
[02-Aug-2025 12:18:17 Africa/Algiers] Aucune page trouvée
[02-Aug-2025 12:18:17 Africa/Algiers] Dernière erreur SQL: ["00000",null,null]
[02-Aug-2025 12:18:25 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:18:25 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:18:25 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:18:25 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:18:25 Africa/Algiers] === Vérification de l'authentification ===
[02-Aug-2025 12:18:25 Africa/Algiers] Headers reçus: {"Host":"localhost:8000","Connection":"keep-alive","sec-ch-ua-platform":"\"Windows\"","Authorization":"Bearer demo_token","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Brave\";v=\"138\"","sec-ch-ua-mobile":"?0","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.6","Sec-Fetch-Site":"same-origin","Sec-Fetch-Mode":"cors","Sec-Fetch-Dest":"empty","Referer":"http:\/\/localhost:8000\/dashboard.html","Accept-Encoding":"gzip, deflate, br, zstd"}
[02-Aug-2025 12:18:25 Africa/Algiers] Token extrait: demo_token
[02-Aug-2025 12:18:25 Africa/Algiers] === Début getUserIdFromToken ===
[02-Aug-2025 12:18:25 Africa/Algiers] Token reçu : demo_token
[02-Aug-2025 12:18:25 Africa/Algiers] Headers reçus : {"Host":"localhost:8000","Connection":"keep-alive","sec-ch-ua-platform":"\"Windows\"","Authorization":"Bearer demo_token","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Brave\";v=\"138\"","sec-ch-ua-mobile":"?0","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.6","Sec-Fetch-Site":"same-origin","Sec-Fetch-Mode":"cors","Sec-Fetch-Dest":"empty","Referer":"http:\/\/localhost:8000\/dashboard.html","Accept-Encoding":"gzip, deflate, br, zstd"}
[02-Aug-2025 12:18:25 Africa/Algiers] Utilisation du token de démo
[02-Aug-2025 12:18:25 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:18:25 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(131): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(38): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:18:25 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:18:25 Africa/Algiers] userId reçu: 1
[02-Aug-2025 12:18:25 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:18:25 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:18:25 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = ? AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:18:25 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:18:25 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = ? AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:18:25 Africa/Algiers] UserId: 1
[02-Aug-2025 12:18:25 Africa/Algiers] Nombre de pages trouvées: 0
[02-Aug-2025 12:18:25 Africa/Algiers] Aucune page trouvée
[02-Aug-2025 12:18:25 Africa/Algiers] Dernière erreur SQL: ["00000",null,null]
[02-Aug-2025 12:18:25 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:18:25 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:18:25 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:18:25 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:18:25 Africa/Algiers] === Vérification de l'authentification ===
[02-Aug-2025 12:18:25 Africa/Algiers] Headers reçus: {"Host":"localhost:8000","Connection":"keep-alive","sec-ch-ua-platform":"\"Windows\"","Authorization":"Bearer demo_token","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Brave\";v=\"138\"","sec-ch-ua-mobile":"?0","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.6","Sec-Fetch-Site":"same-origin","Sec-Fetch-Mode":"cors","Sec-Fetch-Dest":"empty","Referer":"http:\/\/localhost:8000\/dashboard.html","Accept-Encoding":"gzip, deflate, br, zstd"}
[02-Aug-2025 12:18:25 Africa/Algiers] Token extrait: demo_token
[02-Aug-2025 12:18:25 Africa/Algiers] === Début getUserIdFromToken ===
[02-Aug-2025 12:18:25 Africa/Algiers] Token reçu : demo_token
[02-Aug-2025 12:18:25 Africa/Algiers] Headers reçus : {"Host":"localhost:8000","Connection":"keep-alive","sec-ch-ua-platform":"\"Windows\"","Authorization":"Bearer demo_token","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Brave\";v=\"138\"","sec-ch-ua-mobile":"?0","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.6","Sec-Fetch-Site":"same-origin","Sec-Fetch-Mode":"cors","Sec-Fetch-Dest":"empty","Referer":"http:\/\/localhost:8000\/dashboard.html","Accept-Encoding":"gzip, deflate, br, zstd"}
[02-Aug-2025 12:18:25 Africa/Algiers] Utilisation du token de démo
[02-Aug-2025 12:18:25 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:18:25 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(131): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(38): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:18:25 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:18:25 Africa/Algiers] userId reçu: 1
[02-Aug-2025 12:18:25 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:18:25 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:18:25 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = ? AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:18:25 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:18:25 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = ? AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:18:25 Africa/Algiers] UserId: 1
[02-Aug-2025 12:18:25 Africa/Algiers] Nombre de pages trouvées: 0
[02-Aug-2025 12:18:25 Africa/Algiers] Aucune page trouvée
[02-Aug-2025 12:18:25 Africa/Algiers] Dernière erreur SQL: ["00000",null,null]
[02-Aug-2025 12:19:53 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:19:55 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:19:55 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:19:55 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:19:55 Africa/Algiers] === Vérification de l'authentification ===
[02-Aug-2025 12:19:55 Africa/Algiers] Headers reçus: {"Host":"localhost:8000","Connection":"keep-alive","Pragma":"no-cache","Cache-Control":"no-cache","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Trae\/1.100.3 Chrome\/132.0.6834.210 Electron\/34.5.1 Safari\/537.36","Accept":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","Sec-Fetch-Site":"none","Sec-Fetch-Mode":"navigate","Sec-Fetch-User":"?1","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US"}
[02-Aug-2025 12:19:55 Africa/Algiers] Token extrait: null
[02-Aug-2025 12:19:55 Africa/Algiers] === Début getUserIdFromToken ===
[02-Aug-2025 12:19:55 Africa/Algiers] Token reçu : null
[02-Aug-2025 12:19:55 Africa/Algiers] Headers reçus : {"Host":"localhost:8000","Connection":"keep-alive","Pragma":"no-cache","Cache-Control":"no-cache","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Trae\/1.100.3 Chrome\/132.0.6834.210 Electron\/34.5.1 Safari\/537.36","Accept":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","Sec-Fetch-Site":"none","Sec-Fetch-Mode":"navigate","Sec-Fetch-User":"?1","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US"}
[02-Aug-2025 12:19:55 Africa/Algiers] Aucun token fourni, utilisation de l'ID par défaut
[02-Aug-2025 12:19:55 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:19:55 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(131): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(38): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:19:55 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:19:55 Africa/Algiers] userId reçu: 1
[02-Aug-2025 12:19:55 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:19:55 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:19:55 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = ? AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:19:55 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:19:55 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = ? AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:19:55 Africa/Algiers] UserId: 1
[02-Aug-2025 12:19:55 Africa/Algiers] Nombre de pages trouvées: 0
[02-Aug-2025 12:19:55 Africa/Algiers] Aucune page trouvée
[02-Aug-2025 12:19:55 Africa/Algiers] Dernière erreur SQL: ["00000",null,null]
[02-Aug-2025 12:20:15 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:20:15 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:20:15 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:20:15 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:20:15 Africa/Algiers] === Vérification de l'authentification ===
[02-Aug-2025 12:20:15 Africa/Algiers] Headers reçus: {"Host":"localhost:8000","Connection":"keep-alive","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Trae\/1.100.3 Chrome\/132.0.6834.210 Electron\/34.5.1 Safari\/537.36","Accept":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","Sec-Fetch-Site":"none","Sec-Fetch-Mode":"navigate","Sec-Fetch-User":"?1","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US"}
[02-Aug-2025 12:20:15 Africa/Algiers] Token extrait: null
[02-Aug-2025 12:20:15 Africa/Algiers] === Début getUserIdFromToken ===
[02-Aug-2025 12:20:15 Africa/Algiers] Token reçu : null
[02-Aug-2025 12:20:15 Africa/Algiers] Headers reçus : {"Host":"localhost:8000","Connection":"keep-alive","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Trae\/1.100.3 Chrome\/132.0.6834.210 Electron\/34.5.1 Safari\/537.36","Accept":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","Sec-Fetch-Site":"none","Sec-Fetch-Mode":"navigate","Sec-Fetch-User":"?1","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US"}
[02-Aug-2025 12:20:15 Africa/Algiers] Aucun token fourni, utilisation de l'ID par défaut
[02-Aug-2025 12:20:15 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:20:15 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(131): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(38): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:20:15 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:20:15 Africa/Algiers] userId reçu: 1
[02-Aug-2025 12:20:15 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:20:15 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:20:15 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = 4 AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:20:15 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:20:15 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = 4 AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:20:15 Africa/Algiers] UserId: 1
[02-Aug-2025 12:20:15 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:20:20 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:20:20 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:20:20 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:20:20 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:20:20 Africa/Algiers] === Vérification de l'authentification ===
[02-Aug-2025 12:20:20 Africa/Algiers] Headers reçus: {"Host":"localhost:8000","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/20100101 Firefox\/141.0","Accept":"*\/*","Accept-Language":"fr,fr-FR;q=0.8,en-US;q=0.5,en;q=0.3","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"http:\/\/localhost:8000\/seller-dashboard.html","Authorization":"Bearer demo_token","Connection":"keep-alive","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Priority":"u=4"}
[02-Aug-2025 12:20:20 Africa/Algiers] Token extrait: demo_token
[02-Aug-2025 12:20:20 Africa/Algiers] === Début getUserIdFromToken ===
[02-Aug-2025 12:20:20 Africa/Algiers] Token reçu : demo_token
[02-Aug-2025 12:20:20 Africa/Algiers] Headers reçus : {"Host":"localhost:8000","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/20100101 Firefox\/141.0","Accept":"*\/*","Accept-Language":"fr,fr-FR;q=0.8,en-US;q=0.5,en;q=0.3","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"http:\/\/localhost:8000\/seller-dashboard.html","Authorization":"Bearer demo_token","Connection":"keep-alive","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Priority":"u=4"}
[02-Aug-2025 12:20:20 Africa/Algiers] Utilisation du token de démo
[02-Aug-2025 12:20:20 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:20:20 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(131): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(38): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:25:50 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:25:52 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:25:52 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:25:52 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:25:52 Africa/Algiers] === Vérification de l'authentification ===
[02-Aug-2025 12:25:52 Africa/Algiers] Headers reçus: {"Host":"localhost:8000","Connection":"keep-alive","Pragma":"no-cache","Cache-Control":"no-cache","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Trae\/1.100.3 Chrome\/132.0.6834.210 Electron\/34.5.1 Safari\/537.36","Accept":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","Sec-Fetch-Site":"none","Sec-Fetch-Mode":"navigate","Sec-Fetch-User":"?1","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US"}
[02-Aug-2025 12:25:52 Africa/Algiers] Token extrait: null
[02-Aug-2025 12:25:52 Africa/Algiers] === Début getUserIdFromToken ===
[02-Aug-2025 12:25:52 Africa/Algiers] Token reçu : null
[02-Aug-2025 12:25:52 Africa/Algiers] Headers reçus : {"Host":"localhost:8000","Connection":"keep-alive","Pragma":"no-cache","Cache-Control":"no-cache","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Trae\/1.100.3 Chrome\/132.0.6834.210 Electron\/34.5.1 Safari\/537.36","Accept":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","Sec-Fetch-Site":"none","Sec-Fetch-Mode":"navigate","Sec-Fetch-User":"?1","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US"}
[02-Aug-2025 12:25:52 Africa/Algiers] Aucun token fourni, utilisation de l'ID par défaut
[02-Aug-2025 12:25:52 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:25:52 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(131): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(38): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:25:52 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:25:52 Africa/Algiers] userId reçu: 1
[02-Aug-2025 12:25:52 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:25:52 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:25:52 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = 4 AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:25:52 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:25:52 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = 4 AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:25:52 Africa/Algiers] UserId: 1
[02-Aug-2025 12:25:52 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:26:09 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:26:09 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:26:09 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:26:09 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:26:09 Africa/Algiers] === Vérification de l'authentification ===
[02-Aug-2025 12:26:09 Africa/Algiers] Headers reçus: {"Host":"localhost:8000","Connection":"keep-alive","sec-ch-ua-platform":"\"Windows\"","Authorization":"Bearer demo_token","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Brave\";v=\"138\"","sec-ch-ua-mobile":"?0","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.6","Sec-Fetch-Site":"same-origin","Sec-Fetch-Mode":"cors","Sec-Fetch-Dest":"empty","Referer":"http:\/\/localhost:8000\/dashboard.html","Accept-Encoding":"gzip, deflate, br, zstd"}
[02-Aug-2025 12:26:09 Africa/Algiers] Token extrait: demo_token
[02-Aug-2025 12:26:09 Africa/Algiers] === Début getUserIdFromToken ===
[02-Aug-2025 12:26:09 Africa/Algiers] Token reçu : demo_token
[02-Aug-2025 12:26:09 Africa/Algiers] Headers reçus : {"Host":"localhost:8000","Connection":"keep-alive","sec-ch-ua-platform":"\"Windows\"","Authorization":"Bearer demo_token","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Brave\";v=\"138\"","sec-ch-ua-mobile":"?0","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.6","Sec-Fetch-Site":"same-origin","Sec-Fetch-Mode":"cors","Sec-Fetch-Dest":"empty","Referer":"http:\/\/localhost:8000\/dashboard.html","Accept-Encoding":"gzip, deflate, br, zstd"}
[02-Aug-2025 12:26:09 Africa/Algiers] Utilisation du token de démo
[02-Aug-2025 12:26:09 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:26:09 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(131): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(38): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:26:09 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:26:09 Africa/Algiers] userId reçu: 1
[02-Aug-2025 12:26:09 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:26:09 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:26:09 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = 4 AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:26:09 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:26:09 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = 4 AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:26:09 Africa/Algiers] UserId: 1
[02-Aug-2025 12:26:09 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:26:10 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:26:10 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:26:10 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:26:10 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:26:10 Africa/Algiers] === Vérification de l'authentification ===
[02-Aug-2025 12:26:10 Africa/Algiers] Headers reçus: {"Host":"localhost:8000","Connection":"keep-alive","sec-ch-ua-platform":"\"Windows\"","Authorization":"Bearer demo_token","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Brave\";v=\"138\"","sec-ch-ua-mobile":"?0","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.6","Sec-Fetch-Site":"same-origin","Sec-Fetch-Mode":"cors","Sec-Fetch-Dest":"empty","Referer":"http:\/\/localhost:8000\/dashboard.html","Accept-Encoding":"gzip, deflate, br, zstd"}
[02-Aug-2025 12:26:10 Africa/Algiers] Token extrait: demo_token
[02-Aug-2025 12:26:10 Africa/Algiers] === Début getUserIdFromToken ===
[02-Aug-2025 12:26:10 Africa/Algiers] Token reçu : demo_token
[02-Aug-2025 12:26:10 Africa/Algiers] Headers reçus : {"Host":"localhost:8000","Connection":"keep-alive","sec-ch-ua-platform":"\"Windows\"","Authorization":"Bearer demo_token","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Brave\";v=\"138\"","sec-ch-ua-mobile":"?0","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.6","Sec-Fetch-Site":"same-origin","Sec-Fetch-Mode":"cors","Sec-Fetch-Dest":"empty","Referer":"http:\/\/localhost:8000\/dashboard.html","Accept-Encoding":"gzip, deflate, br, zstd"}
[02-Aug-2025 12:26:10 Africa/Algiers] Utilisation du token de démo
[02-Aug-2025 12:26:10 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:26:10 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(131): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(38): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:26:10 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:26:10 Africa/Algiers] userId reçu: 1
[02-Aug-2025 12:26:10 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:26:10 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:26:10 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = 4 AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:26:10 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:26:10 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = 4 AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:26:10 Africa/Algiers] UserId: 1
[02-Aug-2025 12:26:10 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:26:28 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:26:28 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:26:28 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:26:28 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:26:28 Africa/Algiers] === Vérification de l'authentification ===
[02-Aug-2025 12:26:28 Africa/Algiers] Headers reçus: {"Host":"localhost:8000","Connection":"keep-alive","sec-ch-ua-platform":"\"Windows\"","Authorization":"Bearer demo_token","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Brave\";v=\"138\"","sec-ch-ua-mobile":"?0","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.6","Sec-Fetch-Site":"same-origin","Sec-Fetch-Mode":"cors","Sec-Fetch-Dest":"empty","Referer":"http:\/\/localhost:8000\/dashboard.html","Accept-Encoding":"gzip, deflate, br, zstd"}
[02-Aug-2025 12:26:28 Africa/Algiers] Token extrait: demo_token
[02-Aug-2025 12:26:28 Africa/Algiers] === Début getUserIdFromToken ===
[02-Aug-2025 12:26:28 Africa/Algiers] Token reçu : demo_token
[02-Aug-2025 12:26:28 Africa/Algiers] Headers reçus : {"Host":"localhost:8000","Connection":"keep-alive","sec-ch-ua-platform":"\"Windows\"","Authorization":"Bearer demo_token","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Brave\";v=\"138\"","sec-ch-ua-mobile":"?0","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.6","Sec-Fetch-Site":"same-origin","Sec-Fetch-Mode":"cors","Sec-Fetch-Dest":"empty","Referer":"http:\/\/localhost:8000\/dashboard.html","Accept-Encoding":"gzip, deflate, br, zstd"}
[02-Aug-2025 12:26:28 Africa/Algiers] Utilisation du token de démo
[02-Aug-2025 12:26:28 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:26:28 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(131): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(38): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:26:28 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:26:28 Africa/Algiers] userId reçu: 1
[02-Aug-2025 12:26:28 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:26:28 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:26:28 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = 4 AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:26:28 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:26:28 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = 4 AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:26:28 Africa/Algiers] UserId: 1
[02-Aug-2025 12:26:28 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:26:28 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:26:28 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:26:28 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:26:28 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:26:28 Africa/Algiers] === Vérification de l'authentification ===
[02-Aug-2025 12:26:28 Africa/Algiers] Headers reçus: {"Host":"localhost:8000","Connection":"keep-alive","sec-ch-ua-platform":"\"Windows\"","Authorization":"Bearer demo_token","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Brave\";v=\"138\"","sec-ch-ua-mobile":"?0","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.6","Sec-Fetch-Site":"same-origin","Sec-Fetch-Mode":"cors","Sec-Fetch-Dest":"empty","Referer":"http:\/\/localhost:8000\/dashboard.html","Accept-Encoding":"gzip, deflate, br, zstd"}
[02-Aug-2025 12:26:28 Africa/Algiers] Token extrait: demo_token
[02-Aug-2025 12:26:28 Africa/Algiers] === Début getUserIdFromToken ===
[02-Aug-2025 12:26:28 Africa/Algiers] Token reçu : demo_token
[02-Aug-2025 12:26:28 Africa/Algiers] Headers reçus : {"Host":"localhost:8000","Connection":"keep-alive","sec-ch-ua-platform":"\"Windows\"","Authorization":"Bearer demo_token","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Brave\";v=\"138\"","sec-ch-ua-mobile":"?0","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.6","Sec-Fetch-Site":"same-origin","Sec-Fetch-Mode":"cors","Sec-Fetch-Dest":"empty","Referer":"http:\/\/localhost:8000\/dashboard.html","Accept-Encoding":"gzip, deflate, br, zstd"}
[02-Aug-2025 12:26:28 Africa/Algiers] Utilisation du token de démo
[02-Aug-2025 12:26:28 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:26:28 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(131): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(38): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:26:28 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:26:28 Africa/Algiers] userId reçu: 1
[02-Aug-2025 12:26:28 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:26:28 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:26:28 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = 4 AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:26:28 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:26:28 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = 4 AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:26:28 Africa/Algiers] UserId: 1
[02-Aug-2025 12:26:28 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:28:04 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:28:04 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:28:04 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:28:04 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:28:04 Africa/Algiers] === Vérification de l'authentification ===
[02-Aug-2025 12:28:04 Africa/Algiers] Headers reçus: {"Host":"localhost:8000","Connection":"keep-alive","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Trae\/1.100.3 Chrome\/132.0.6834.210 Electron\/34.5.1 Safari\/537.36","Accept":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","Sec-Fetch-Site":"none","Sec-Fetch-Mode":"navigate","Sec-Fetch-User":"?1","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US"}
[02-Aug-2025 12:28:04 Africa/Algiers] Token extrait: null
[02-Aug-2025 12:28:04 Africa/Algiers] === Début getUserIdFromToken ===
[02-Aug-2025 12:28:04 Africa/Algiers] Token reçu : null
[02-Aug-2025 12:28:04 Africa/Algiers] Headers reçus : {"Host":"localhost:8000","Connection":"keep-alive","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Trae\/1.100.3 Chrome\/132.0.6834.210 Electron\/34.5.1 Safari\/537.36","Accept":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","Sec-Fetch-Site":"none","Sec-Fetch-Mode":"navigate","Sec-Fetch-User":"?1","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US"}
[02-Aug-2025 12:28:04 Africa/Algiers] Aucun token fourni, utilisation de l'ID par défaut
[02-Aug-2025 12:28:04 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:28:04 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(131): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(38): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:28:04 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:28:04 Africa/Algiers] userId reçu: 1
[02-Aug-2025 12:28:04 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:28:04 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:28:04 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = 4 AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:28:04 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:28:04 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id
            INNER JOIN users u ON m.email = u.email
            WHERE u.id = 4 AND u.status = 'active' AND m.status = 'active' AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:28:04 Africa/Algiers] UserId: 1
[02-Aug-2025 12:28:04 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:29:24 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:29:24 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:29:24 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:29:24 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:29:24 Africa/Algiers] === Vérification de l'authentification ===
[02-Aug-2025 12:29:24 Africa/Algiers] Headers reçus: {"Host":"localhost:8000","Connection":"keep-alive","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Trae\/1.100.3 Chrome\/132.0.6834.210 Electron\/34.5.1 Safari\/537.36","Accept":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","Sec-Fetch-Site":"none","Sec-Fetch-Mode":"navigate","Sec-Fetch-User":"?1","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US"}
[02-Aug-2025 12:29:24 Africa/Algiers] Token extrait: null
[02-Aug-2025 12:29:24 Africa/Algiers] === Début getUserIdFromToken ===
[02-Aug-2025 12:29:24 Africa/Algiers] Token reçu : null
[02-Aug-2025 12:29:24 Africa/Algiers] Headers reçus : {"Host":"localhost:8000","Connection":"keep-alive","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Trae\/1.100.3 Chrome\/132.0.6834.210 Electron\/34.5.1 Safari\/537.36","Accept":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","Sec-Fetch-Site":"none","Sec-Fetch-Mode":"navigate","Sec-Fetch-User":"?1","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US"}
[02-Aug-2025 12:29:24 Africa/Algiers] Aucun token fourni, utilisation de l'ID par défaut
[02-Aug-2025 12:29:24 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:29:24 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(131): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(38): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:29:24 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:29:24 Africa/Algiers] userId reçu: 1
[02-Aug-2025 12:29:24 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:29:24 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:29:24 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:29:24 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:29:24 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:29:24 Africa/Algiers] UserId: 1
[02-Aug-2025 12:29:24 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:29:55 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:29:55 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:29:55 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:29:55 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:29:55 Africa/Algiers] === Vérification de l'authentification ===
[02-Aug-2025 12:29:55 Africa/Algiers] Headers reçus: {"Host":"localhost:8000","Connection":"keep-alive","Pragma":"no-cache","Cache-Control":"no-cache","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Trae\/1.100.3 Chrome\/132.0.6834.210 Electron\/34.5.1 Safari\/537.36","Accept":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","Sec-Fetch-Site":"none","Sec-Fetch-Mode":"navigate","Sec-Fetch-User":"?1","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US"}
[02-Aug-2025 12:29:55 Africa/Algiers] Token extrait: null
[02-Aug-2025 12:29:55 Africa/Algiers] === Début getUserIdFromToken ===
[02-Aug-2025 12:29:55 Africa/Algiers] Token reçu : null
[02-Aug-2025 12:29:55 Africa/Algiers] Headers reçus : {"Host":"localhost:8000","Connection":"keep-alive","Pragma":"no-cache","Cache-Control":"no-cache","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Trae\/1.100.3 Chrome\/132.0.6834.210 Electron\/34.5.1 Safari\/537.36","Accept":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","Sec-Fetch-Site":"none","Sec-Fetch-Mode":"navigate","Sec-Fetch-User":"?1","Sec-Fetch-Dest":"document","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US"}
[02-Aug-2025 12:29:55 Africa/Algiers] Aucun token fourni, utilisation de l'ID par défaut
[02-Aug-2025 12:29:55 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:29:55 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(131): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(38): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:29:55 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:29:55 Africa/Algiers] userId reçu: 1
[02-Aug-2025 12:29:55 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:29:55 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:29:55 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:29:55 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:29:55 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:29:55 Africa/Algiers] UserId: 1
[02-Aug-2025 12:29:55 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:30:01 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:30:01 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:30:01 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:30:01 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:30:01 Africa/Algiers] === Utilisation du merchant_id de test ===
[02-Aug-2025 12:30:01 Africa/Algiers] MerchantId: 1
[02-Aug-2025 12:30:01 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:30:01 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(128): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:30:01 Africa/Algiers] PHP Warning:  Undefined variable $userId in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 41
[02-Aug-2025 12:30:01 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:30:01 Africa/Algiers] userId reçu: 
[02-Aug-2025 12:30:01 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:30:01 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:30:01 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:30:01 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:30:01 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:30:01 Africa/Algiers] UserId: 
[02-Aug-2025 12:30:01 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:30:04 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:30:04 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:30:04 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:30:04 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:30:04 Africa/Algiers] === Utilisation du merchant_id de test ===
[02-Aug-2025 12:30:04 Africa/Algiers] MerchantId: 1
[02-Aug-2025 12:30:04 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:30:04 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(128): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:30:04 Africa/Algiers] PHP Warning:  Undefined variable $userId in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 41
[02-Aug-2025 12:30:04 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:30:04 Africa/Algiers] userId reçu: 
[02-Aug-2025 12:30:04 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:30:04 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:30:04 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:30:04 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:30:04 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:30:04 Africa/Algiers] UserId: 
[02-Aug-2025 12:30:04 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:30:08 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:30:08 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:30:08 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:30:08 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:30:08 Africa/Algiers] === Utilisation du merchant_id de test ===
[02-Aug-2025 12:30:08 Africa/Algiers] MerchantId: 1
[02-Aug-2025 12:30:08 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:30:08 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(128): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:30:08 Africa/Algiers] PHP Warning:  Undefined variable $userId in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 41
[02-Aug-2025 12:30:08 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:30:08 Africa/Algiers] userId reçu: 
[02-Aug-2025 12:30:08 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:30:08 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:30:08 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:30:08 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:30:08 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:30:08 Africa/Algiers] UserId: 
[02-Aug-2025 12:30:08 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:30:09 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:30:09 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:30:09 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:30:09 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:30:09 Africa/Algiers] === Utilisation du merchant_id de test ===
[02-Aug-2025 12:30:09 Africa/Algiers] MerchantId: 1
[02-Aug-2025 12:30:09 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:30:09 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(128): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:30:09 Africa/Algiers] PHP Warning:  Undefined variable $userId in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 41
[02-Aug-2025 12:30:09 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:30:09 Africa/Algiers] userId reçu: 
[02-Aug-2025 12:30:09 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:30:09 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:30:09 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:30:09 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:30:09 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:30:09 Africa/Algiers] UserId: 
[02-Aug-2025 12:30:09 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:30:44 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:30:44 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:30:44 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:30:44 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:30:44 Africa/Algiers] === Utilisation du merchant_id de test ===
[02-Aug-2025 12:30:44 Africa/Algiers] MerchantId: 1
[02-Aug-2025 12:30:44 Africa/Algiers] Erreur dans getSubscriptionPlanId: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'us.subscription_plan_id' in 'field list'
[02-Aug-2025 12:30:44 Africa/Algiers] Stack trace: #0 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(129): PDO->prepare()
#1 K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php(35): getSubscriptionPlanId()
#2 {main}
[02-Aug-2025 12:30:44 Africa/Algiers] PHP Warning:  Undefined variable $userId in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 41
[02-Aug-2025 12:30:44 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:30:44 Africa/Algiers] userId reçu: 
[02-Aug-2025 12:30:44 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:30:44 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:30:44 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:30:44 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:30:44 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:30:44 Africa/Algiers] UserId: 
[02-Aug-2025 12:30:44 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:31:50 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:31:50 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:31:50 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:31:50 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:31:50 Africa/Algiers] === Utilisation du merchant_id de test ===
[02-Aug-2025 12:31:50 Africa/Algiers] MerchantId: 1
[02-Aug-2025 12:31:50 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 12:31:50 Africa/Algiers] PHP Warning:  Undefined variable $userId in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 41
[02-Aug-2025 12:31:50 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:31:50 Africa/Algiers] userId reçu: 
[02-Aug-2025 12:31:50 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:31:50 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:31:50 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:31:50 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:31:50 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:31:50 Africa/Algiers] UserId: 
[02-Aug-2025 12:31:50 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:32:48 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:32:48 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:32:48 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:32:48 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:32:48 Africa/Algiers] === Utilisation du merchant_id de test ===
[02-Aug-2025 12:32:48 Africa/Algiers] MerchantId: 1
[02-Aug-2025 12:32:48 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 12:32:48 Africa/Algiers] PHP Warning:  Undefined variable $userId in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 41
[02-Aug-2025 12:32:48 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:32:48 Africa/Algiers] userId reçu: 
[02-Aug-2025 12:32:48 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:32:48 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:32:48 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:32:48 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:32:48 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:32:48 Africa/Algiers] UserId: 
[02-Aug-2025 12:32:48 Africa/Algiers] Paramètres de la requête: [null]
[02-Aug-2025 12:32:48 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:34:05 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:34:07 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:34:07 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:34:07 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:34:07 Africa/Algiers] === Utilisation du merchant_id de test ===
[02-Aug-2025 12:34:07 Africa/Algiers] MerchantId: 1
[02-Aug-2025 12:34:07 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 12:34:07 Africa/Algiers] PHP Warning:  Undefined variable $userId in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 41
[02-Aug-2025 12:34:07 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:34:07 Africa/Algiers] userId reçu: 
[02-Aug-2025 12:34:07 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:34:07 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:34:07 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:34:07 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:34:07 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:34:07 Africa/Algiers] UserId: 
[02-Aug-2025 12:34:07 Africa/Algiers] Paramètres de la requête: [null]
[02-Aug-2025 12:34:07 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:35:04 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:35:04 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:35:04 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:35:04 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:35:04 Africa/Algiers] === Utilisation du merchant_id de test ===
[02-Aug-2025 12:35:04 Africa/Algiers] MerchantId: 1
[02-Aug-2025 12:35:04 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 12:35:04 Africa/Algiers] PHP Warning:  Undefined variable $userId in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 46
[02-Aug-2025 12:35:04 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:35:04 Africa/Algiers] userId reçu: 
[02-Aug-2025 12:35:04 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:35:04 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:35:04 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:35:04 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:35:04 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:35:04 Africa/Algiers] UserId: 
[02-Aug-2025 12:35:04 Africa/Algiers] Paramètres de la requête: [null]
[02-Aug-2025 12:35:04 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:58:25 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:58:25 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:58:25 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:58:25 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:58:25 Africa/Algiers] === Utilisation du merchant_id de test ===
[02-Aug-2025 12:58:25 Africa/Algiers] MerchantId: 1
[02-Aug-2025 12:58:25 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 12:58:25 Africa/Algiers] PHP Warning:  Undefined variable $userId in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 46
[02-Aug-2025 12:58:25 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:58:25 Africa/Algiers] userId reçu: 
[02-Aug-2025 12:58:25 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:58:25 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:58:25 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:58:25 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:58:25 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:58:25 Africa/Algiers] UserId: 
[02-Aug-2025 12:58:25 Africa/Algiers] Paramètres de la requête: [null]
[02-Aug-2025 12:58:25 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 12:58:25 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 12:58:25 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 12:58:25 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:58:25 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 12:58:25 Africa/Algiers] === Utilisation du merchant_id de test ===
[02-Aug-2025 12:58:25 Africa/Algiers] MerchantId: 1
[02-Aug-2025 12:58:25 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 12:58:25 Africa/Algiers] PHP Warning:  Undefined variable $userId in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 46
[02-Aug-2025 12:58:25 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 12:58:25 Africa/Algiers] userId reçu: 
[02-Aug-2025 12:58:25 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 12:58:25 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 12:58:25 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:58:25 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 12:58:25 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 12:58:25 Africa/Algiers] UserId: 
[02-Aug-2025 12:58:25 Africa/Algiers] Paramètres de la requête: [null]
[02-Aug-2025 12:58:25 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 13:01:20 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 13:01:20 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 13:01:20 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 13:01:20 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 13:01:20 Africa/Algiers] === Utilisation du merchant_id de test ===
[02-Aug-2025 13:01:20 Africa/Algiers] MerchantId: 1
[02-Aug-2025 13:01:20 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 13:01:20 Africa/Algiers] PHP Warning:  Undefined variable $userId in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 46
[02-Aug-2025 13:01:20 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 13:01:20 Africa/Algiers] userId reçu: 
[02-Aug-2025 13:01:20 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 13:01:20 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 13:01:20 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 13:01:20 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 13:01:20 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 13:01:20 Africa/Algiers] UserId: 
[02-Aug-2025 13:01:20 Africa/Algiers] Paramètres de la requête: [null]
[02-Aug-2025 13:01:20 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 13:01:20 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 13:01:20 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 13:01:20 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 13:01:20 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 13:01:20 Africa/Algiers] === Utilisation du merchant_id de test ===
[02-Aug-2025 13:01:20 Africa/Algiers] MerchantId: 1
[02-Aug-2025 13:01:20 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 13:01:20 Africa/Algiers] PHP Warning:  Undefined variable $userId in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 46
[02-Aug-2025 13:01:20 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 13:01:20 Africa/Algiers] userId reçu: 
[02-Aug-2025 13:01:20 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 13:01:20 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 13:01:20 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 13:01:20 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 13:01:20 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 13:01:20 Africa/Algiers] UserId: 
[02-Aug-2025 13:01:20 Africa/Algiers] Paramètres de la requête: [null]
[02-Aug-2025 13:01:20 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 13:14:03 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 13:14:05 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 13:14:05 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 13:14:05 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 13:14:05 Africa/Algiers] === Utilisation du merchant_id de test ===
[02-Aug-2025 13:14:05 Africa/Algiers] MerchantId: 1
[02-Aug-2025 13:14:05 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 13:14:05 Africa/Algiers] PHP Warning:  Undefined variable $userId in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 46
[02-Aug-2025 13:14:05 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 13:14:05 Africa/Algiers] userId reçu: 
[02-Aug-2025 13:14:05 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 13:14:05 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 13:14:05 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 13:14:05 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 13:14:05 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = 1 AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 13:14:05 Africa/Algiers] UserId: 
[02-Aug-2025 13:14:05 Africa/Algiers] Paramètres de la requête: [null]
[02-Aug-2025 13:14:05 Africa/Algiers] SQLSTATE[HY093]: Invalid parameter number
[02-Aug-2025 13:18:48 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 13:18:50 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 13:18:50 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 13:18:50 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 13:18:50 Africa/Algiers] PHP Fatal error:  Uncaught Error: Call to undefined function getMerchantId() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:35
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 35
[02-Aug-2025 12:37:08 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 12:37:11 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 13:37:13 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 13:37:13 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 13:37:13 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 13:37:13 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 13:37:13 Africa/Algiers] PHP Fatal error:  Uncaught Error: Call to undefined function getMerchantId() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:35
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 35
[02-Aug-2025 13:37:13 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 13:37:13 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 13:37:13 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 13:37:13 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 13:37:13 Africa/Algiers] PHP Fatal error:  Uncaught Error: Call to undefined function getMerchantId() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:35
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 35
[02-Aug-2025 13:37:29 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 13:37:29 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 13:37:29 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 13:37:29 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 13:37:29 Africa/Algiers] PHP Fatal error:  Uncaught Error: Call to undefined function getMerchantId() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:35
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 35
[02-Aug-2025 13:37:29 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 13:37:29 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 13:37:29 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 13:37:29 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 13:37:29 Africa/Algiers] PHP Fatal error:  Uncaught Error: Call to undefined function getMerchantId() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:35
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 35
[02-Aug-2025 13:43:52 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 13:43:54 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 13:43:54 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 13:43:54 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 13:43:54 Africa/Algiers] PHP Fatal error:  Uncaught Error: Call to undefined function getMerchantId() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:35
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 35
[02-Aug-2025 13:43:54 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 13:43:54 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 13:43:54 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 13:43:54 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 13:43:54 Africa/Algiers] PHP Fatal error:  Uncaught Error: Call to undefined function getMerchantId() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:35
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 35
[02-Aug-2025 13:49:16 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 13:49:18 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 13:49:19 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 13:49:20 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 13:49:20 Africa/Algiers] PHP Fatal error:  Uncaught Error: Call to undefined function getMerchantId() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:35
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 35
[02-Aug-2025 13:53:52 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 13:53:54 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 13:53:54 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 13:53:54 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 13:53:54 Africa/Algiers] PHP Fatal error:  Uncaught Error: Call to undefined function getMerchantId() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:35
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 35
[02-Aug-2025 13:59:27 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 13:59:29 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 13:59:29 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 13:59:29 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 13:59:29 Africa/Algiers] PHP Fatal error:  Uncaught Error: Call to undefined function getMerchantId() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:35
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 35
[02-Aug-2025 14:03:21 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 14:03:23 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 14:03:23 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 14:03:23 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 14:03:23 Africa/Algiers] PHP Fatal error:  Uncaught Error: Call to undefined function getMerchantId() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:35
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 35
[02-Aug-2025 14:03:23 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 14:03:23 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 14:03:23 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 14:03:23 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 14:03:23 Africa/Algiers] PHP Fatal error:  Uncaught Error: Call to undefined function getMerchantId() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:35
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 35
[02-Aug-2025 14:03:52 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 14:03:52 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 14:03:52 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 14:03:52 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 14:03:52 Africa/Algiers] PHP Fatal error:  Uncaught Error: Call to undefined function getMerchantId() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:35
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 35
[02-Aug-2025 14:03:52 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 14:03:53 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 14:03:53 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 14:03:53 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 14:03:53 Africa/Algiers] PHP Fatal error:  Uncaught Error: Call to undefined function getMerchantId() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:35
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 35
[02-Aug-2025 14:05:09 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 14:05:09 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 14:05:09 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 14:05:09 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 14:05:09 Africa/Algiers] PHP Fatal error:  Uncaught Error: Call to undefined function getMerchantId() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:35
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 35
[02-Aug-2025 14:05:11 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 14:05:11 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 14:05:11 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 14:05:11 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 14:05:11 Africa/Algiers] PHP Fatal error:  Uncaught Error: Call to undefined function getMerchantId() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:35
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 35
[02-Aug-2025 14:07:43 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 14:07:45 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 14:07:45 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 14:07:45 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 14:07:45 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 14:07:45 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 14:07:45 Africa/Algiers] MerchantId: 1
[02-Aug-2025 14:07:45 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 14:07:45 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 14:07:45 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 14:07:45 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 14:07:45 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 14:07:45 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 14:07:45 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 14:07:45 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 14:07:45 Africa/Algiers] MerchantId: 1
[02-Aug-2025 14:07:45 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 14:07:45 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 14:07:45 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 14:07:45 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 14:10:31 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 14:10:33 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 14:10:33 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 14:10:33 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 14:10:33 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 14:10:33 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 14:10:33 Africa/Algiers] MerchantId: 1
[02-Aug-2025 14:10:33 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 14:10:33 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 14:10:33 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 14:10:33 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 14:10:33 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 14:10:33 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 14:10:33 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 14:10:33 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 14:10:33 Africa/Algiers] MerchantId: 1
[02-Aug-2025 14:10:33 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 14:10:33 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 14:10:33 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 14:10:33 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 14:10:33 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 14:10:33 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 14:10:33 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 14:10:33 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 14:10:33 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 14:10:33 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 14:10:33 Africa/Algiers] MerchantId: 1
[02-Aug-2025 14:10:33 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 14:10:33 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 14:10:33 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 14:10:33 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 14:10:33 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 14:10:33 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 14:10:33 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 14:10:33 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 14:10:33 Africa/Algiers] MerchantId: 1
[02-Aug-2025 14:10:33 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 14:10:33 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 14:10:33 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 14:10:33 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 14:18:39 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 14:18:41 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 14:18:41 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 14:18:41 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 14:18:41 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 14:18:41 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 14:18:41 Africa/Algiers] MerchantId: 1
[02-Aug-2025 14:18:41 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 14:18:41 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 14:18:41 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 14:18:41 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 14:18:41 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 14:18:41 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 14:18:41 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 14:18:41 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 14:18:41 Africa/Algiers] MerchantId: 1
[02-Aug-2025 14:18:41 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 14:18:41 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 14:18:41 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 14:18:41 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 15:59:04 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 15:59:04 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 16:59:06 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 16:59:06 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 16:59:06 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 16:59:06 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 16:59:06 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 16:59:06 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 16:59:06 Africa/Algiers] MerchantId: 1
[02-Aug-2025 16:59:06 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 16:59:06 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 16:59:06 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 16:59:06 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 16:59:06 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 16:59:06 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 16:59:06 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 16:59:06 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 16:59:06 Africa/Algiers] MerchantId: 1
[02-Aug-2025 16:59:06 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 16:59:06 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 16:59:06 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 16:59:06 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 16:59:06 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 16:59:06 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 16:59:06 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 16:59:06 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 16:59:06 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 16:59:06 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 16:59:06 Africa/Algiers] MerchantId: 1
[02-Aug-2025 16:59:06 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 16:59:06 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 16:59:06 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 16:59:06 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 16:59:06 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 16:59:06 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 16:59:06 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 16:59:06 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 16:59:06 Africa/Algiers] MerchantId: 1
[02-Aug-2025 16:59:06 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 16:59:06 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 16:59:06 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 16:59:06 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 16:28:25 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 16:28:25 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 17:28:27 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 17:28:27 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 17:28:27 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 17:28:27 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 17:28:27 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 17:28:27 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 17:28:27 Africa/Algiers] MerchantId: 1
[02-Aug-2025 17:28:27 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 17:28:27 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 17:28:27 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 17:28:27 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 17:28:27 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 17:28:27 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 17:28:27 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 17:28:27 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 17:28:27 Africa/Algiers] MerchantId: 1
[02-Aug-2025 17:28:27 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 17:28:27 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 17:28:27 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 17:28:27 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 17:28:27 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 17:28:27 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 17:28:27 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 17:28:27 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 17:28:27 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 17:28:27 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 17:28:27 Africa/Algiers] MerchantId: 1
[02-Aug-2025 17:28:27 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 17:28:27 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 17:28:27 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 17:28:27 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 17:28:27 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 17:28:27 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 17:28:27 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 17:28:27 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 17:28:27 Africa/Algiers] MerchantId: 1
[02-Aug-2025 17:28:27 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 17:28:27 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 17:28:27 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 17:28:27 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 17:18:55 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 17:18:57 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 18:18:59 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 18:18:59 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 18:18:59 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 18:18:59 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 18:18:59 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 18:18:59 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 18:18:59 Africa/Algiers] MerchantId: 1
[02-Aug-2025 18:18:59 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 18:18:59 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 18:18:59 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 18:18:59 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 18:18:59 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 18:18:59 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 18:18:59 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 18:18:59 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 18:18:59 Africa/Algiers] MerchantId: 1
[02-Aug-2025 18:18:59 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 18:18:59 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 18:18:59 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 18:18:59 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 18:18:59 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 18:18:59 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 18:18:59 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 18:18:59 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 18:18:59 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 18:18:59 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 18:18:59 Africa/Algiers] MerchantId: 1
[02-Aug-2025 18:18:59 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 18:18:59 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 18:18:59 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 18:18:59 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 18:18:59 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 18:18:59 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 18:18:59 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 18:18:59 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 18:18:59 Africa/Algiers] MerchantId: 1
[02-Aug-2025 18:18:59 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 18:18:59 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 18:18:59 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 18:18:59 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 18:42:49 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 18:42:49 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 18:42:50 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 18:42:50 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 18:42:50 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 18:42:50 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 18:42:50 Africa/Algiers] MerchantId: 1
[02-Aug-2025 18:42:50 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 18:42:50 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 18:42:50 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 18:42:50 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 18:42:50 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 18:42:50 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 18:42:50 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 18:42:50 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 18:42:50 Africa/Algiers] MerchantId: 1
[02-Aug-2025 18:42:50 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 18:42:50 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 18:42:50 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 18:42:50 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 18:42:51 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 18:42:51 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 18:42:51 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 18:42:51 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 18:42:51 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 18:42:51 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 18:42:51 Africa/Algiers] MerchantId: 1
[02-Aug-2025 18:42:51 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 18:42:51 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 18:42:51 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 18:42:51 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 18:42:51 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 18:42:51 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 18:42:51 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 18:42:51 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 18:42:51 Africa/Algiers] MerchantId: 1
[02-Aug-2025 18:42:51 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 18:42:51 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 18:42:51 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 18:42:51 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 18:56:01 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 18:56:01 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 18:56:01 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 18:56:01 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 18:56:01 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 18:56:01 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 18:56:01 Africa/Algiers] MerchantId: 1
[02-Aug-2025 18:56:01 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 18:56:01 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 18:56:01 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 18:56:01 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 18:56:01 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 18:56:01 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 18:56:01 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 18:56:01 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 18:56:01 Africa/Algiers] MerchantId: 1
[02-Aug-2025 18:56:01 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 18:56:01 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 18:56:01 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 18:56:01 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 18:56:01 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 18:56:01 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 18:56:01 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 18:56:01 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 18:56:01 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 18:56:01 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 18:56:01 Africa/Algiers] MerchantId: 1
[02-Aug-2025 18:56:01 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 18:56:01 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 18:56:01 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 18:56:01 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 18:56:01 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 18:56:01 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 18:56:01 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 18:56:01 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 18:56:01 Africa/Algiers] MerchantId: 1
[02-Aug-2025 18:56:01 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 18:56:01 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 18:56:01 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 18:56:01 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 17:56:06 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 17:56:06 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 18:56:50 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 18:56:50 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 18:56:50 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 18:56:50 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 18:56:50 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 18:56:50 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 18:56:50 Africa/Algiers] MerchantId: 1
[02-Aug-2025 18:56:50 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 19:21:13 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 19:21:13 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 19:21:13 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 19:21:13 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 19:21:13 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 19:21:13 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 19:21:13 Africa/Algiers] MerchantId: 1
[02-Aug-2025 19:21:13 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 19:21:13 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 19:21:13 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 19:21:13 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 19:21:13 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 19:21:13 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 19:21:13 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 19:21:13 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 19:21:13 Africa/Algiers] MerchantId: 1
[02-Aug-2025 19:21:13 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 19:21:13 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 19:21:13 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 19:21:13 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 19:21:14 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 19:21:14 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 19:21:14 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 19:21:14 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 19:21:14 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 19:21:14 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 19:21:14 Africa/Algiers] MerchantId: 1
[02-Aug-2025 19:21:14 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 19:21:14 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 19:21:14 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 19:21:14 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 19:21:14 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 19:21:14 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 19:21:14 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 19:21:14 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 19:21:14 Africa/Algiers] MerchantId: 1
[02-Aug-2025 19:21:14 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 19:21:14 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 19:21:14 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 19:21:14 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 19:21:22 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 19:21:22 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 19:21:22 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 19:21:22 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 19:21:22 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 19:21:22 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 19:21:22 Africa/Algiers] MerchantId: 1
[02-Aug-2025 19:21:22 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 19:21:22 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 19:21:22 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 19:21:22 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 19:21:22 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 19:21:22 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 19:21:22 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 19:21:22 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 19:21:22 Africa/Algiers] MerchantId: 1
[02-Aug-2025 19:21:22 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 19:21:22 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 19:21:22 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 19:21:22 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 19:21:22 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 19:21:22 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 19:21:22 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 19:21:22 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 19:21:22 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 19:21:22 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 19:21:22 Africa/Algiers] MerchantId: 1
[02-Aug-2025 19:21:22 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 19:21:22 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 19:21:22 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 19:21:22 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 19:21:22 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 19:21:22 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 19:21:22 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 19:21:22 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 19:21:22 Africa/Algiers] MerchantId: 1
[02-Aug-2025 19:21:22 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 19:21:22 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 19:21:22 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 19:21:22 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 19:21:29 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 19:21:29 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 19:21:29 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 19:21:29 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 19:21:29 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 19:21:29 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 19:21:29 Africa/Algiers] MerchantId: 1
[02-Aug-2025 19:21:29 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 19:37:07 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 19:37:07 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 19:37:07 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 19:37:07 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 19:37:07 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 19:37:07 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 19:37:07 Africa/Algiers] MerchantId: 1
[02-Aug-2025 19:37:07 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 19:37:07 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 19:37:07 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 19:37:07 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 19:37:07 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 19:37:07 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 19:37:07 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 19:37:07 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 19:37:07 Africa/Algiers] MerchantId: 1
[02-Aug-2025 19:37:07 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 19:37:07 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 19:37:07 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 19:37:07 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 19:37:07 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 19:37:08 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 19:37:08 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 19:37:08 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 19:37:08 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 19:37:08 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 19:37:08 Africa/Algiers] MerchantId: 1
[02-Aug-2025 19:37:08 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 19:37:08 Africa/Algiers] === Début getLandingPages ===
[02-Aug-2025 19:37:08 Africa/Algiers] merchantId reçu: 1
[02-Aug-2025 19:37:08 Africa/Algiers] Type de connexion DB: PDO
[02-Aug-2025 19:37:08 Africa/Algiers] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 19:37:08 Africa/Algiers] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 19:37:08 Africa/Algiers] === Exécution de la requête SQL ===
[02-Aug-2025 19:37:08 Africa/Algiers] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE 
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 19:37:08 Africa/Algiers] MerchantId: 1
[02-Aug-2025 19:37:08 Africa/Algiers] Paramètres de la requête: [1]
[02-Aug-2025 19:37:08 Africa/Algiers] Nombre de pages trouvées: 1
[02-Aug-2025 19:37:08 Africa/Algiers] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 19:37:08 Africa/Algiers] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 19:37:14 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 19:37:14 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 19:37:14 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 19:37:14 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 19:37:14 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 19:37:14 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 19:37:14 Africa/Algiers] MerchantId: 1
[02-Aug-2025 19:37:14 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 19:37:14 Africa/Algiers] PHP Warning:  Undefined variable $userId in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 70
[02-Aug-2025 19:37:17 Africa/Algiers] === Tentative de connexion à la base de données ===
[02-Aug-2025 19:37:17 Africa/Algiers] Host: localhost, Port: 3307, Database: landingpage_new
[02-Aug-2025 19:37:17 Africa/Algiers] DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 19:37:17 Africa/Algiers] Connexion à la base de données établie avec succès
[02-Aug-2025 19:37:17 Africa/Algiers] Début de getMerchantId
[02-Aug-2025 19:37:17 Africa/Algiers] === MerchantId récupéré ===
[02-Aug-2025 19:37:17 Africa/Algiers] MerchantId: 1
[02-Aug-2025 19:37:17 Africa/Algiers] Plan d'abonnement trouvé: 2
[02-Aug-2025 19:37:17 Africa/Algiers] PHP Warning:  Undefined variable $userId in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 70
[02-Aug-2025 18:37:59 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 18:37:59 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 19:01:12 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[02-Aug-2025 19:01:12 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[02-Aug-2025 19:01:15 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[02-Aug-2025 19:01:15 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[02-Aug-2025 19:01:31 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[02-Aug-2025 19:01:38 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[02-Aug-2025 19:02:13 UTC] Début de getMerchantId
[02-Aug-2025 19:02:13 UTC] === MerchantId récupéré ===
[02-Aug-2025 19:02:13 UTC] MerchantId: 1
[02-Aug-2025 19:02:13 UTC] Plan d'abonnement trouvé: 2
[02-Aug-2025 19:02:13 UTC] === Début getLandingPages ===
[02-Aug-2025 19:02:13 UTC] merchantId reçu: 1
[02-Aug-2025 19:02:13 UTC] Type de connexion DB: PDO
[02-Aug-2025 19:02:13 UTC] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 19:02:13 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 19:02:13 UTC] === Exécution de la requête SQL ===
[02-Aug-2025 19:02:13 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 19:02:13 UTC] MerchantId: 1
[02-Aug-2025 19:02:13 UTC] Paramètres de la requête: [1]
[02-Aug-2025 19:02:13 UTC] Nombre de pages trouvées: 1
[02-Aug-2025 19:02:13 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 19:02:13 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 19:02:13 UTC] Début de getMerchantId
[02-Aug-2025 19:02:13 UTC] === MerchantId récupéré ===
[02-Aug-2025 19:02:13 UTC] MerchantId: 1
[02-Aug-2025 19:02:13 UTC] Plan d'abonnement trouvé: 2
[02-Aug-2025 19:02:13 UTC] === Début getLandingPages ===
[02-Aug-2025 19:02:13 UTC] merchantId reçu: 1
[02-Aug-2025 19:02:13 UTC] Type de connexion DB: PDO
[02-Aug-2025 19:02:13 UTC] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 19:02:13 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 19:02:13 UTC] === Exécution de la requête SQL ===
[02-Aug-2025 19:02:13 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 19:02:13 UTC] MerchantId: 1
[02-Aug-2025 19:02:13 UTC] Paramètres de la requête: [1]
[02-Aug-2025 19:02:13 UTC] Nombre de pages trouvées: 1
[02-Aug-2025 19:02:13 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 19:02:13 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 19:02:16 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[02-Aug-2025 19:02:16 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[02-Aug-2025 19:02:30 UTC] Début de getMerchantId
[02-Aug-2025 19:02:30 UTC] === MerchantId récupéré ===
[02-Aug-2025 19:02:30 UTC] MerchantId: 1
[02-Aug-2025 19:02:30 UTC] Plan d'abonnement trouvé: 2
[02-Aug-2025 20:39:27 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 20:39:27 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[02-Aug-2025 20:39:29 UTC] Début de getMerchantId
[02-Aug-2025 20:39:29 UTC] === MerchantId récupéré ===
[02-Aug-2025 20:39:29 UTC] MerchantId: 1
[02-Aug-2025 20:39:29 UTC] Plan d'abonnement trouvé: 2
[02-Aug-2025 20:39:29 UTC] === Début getLandingPages ===
[02-Aug-2025 20:39:29 UTC] merchantId reçu: 1
[02-Aug-2025 20:39:29 UTC] Type de connexion DB: PDO
[02-Aug-2025 20:39:29 UTC] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 20:39:29 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 20:39:29 UTC] === Exécution de la requête SQL ===
[02-Aug-2025 20:39:29 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 20:39:29 UTC] MerchantId: 1
[02-Aug-2025 20:39:29 UTC] Paramètres de la requête: [1]
[02-Aug-2025 20:39:29 UTC] Nombre de pages trouvées: 1
[02-Aug-2025 20:39:29 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 20:39:29 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 20:39:29 UTC] Début de getMerchantId
[02-Aug-2025 20:39:29 UTC] === MerchantId récupéré ===
[02-Aug-2025 20:39:29 UTC] MerchantId: 1
[02-Aug-2025 20:39:29 UTC] Plan d'abonnement trouvé: 2
[02-Aug-2025 20:39:29 UTC] === Début getLandingPages ===
[02-Aug-2025 20:39:29 UTC] merchantId reçu: 1
[02-Aug-2025 20:39:29 UTC] Type de connexion DB: PDO
[02-Aug-2025 20:39:29 UTC] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 20:39:29 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 20:39:29 UTC] === Exécution de la requête SQL ===
[02-Aug-2025 20:39:29 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 20:39:29 UTC] MerchantId: 1
[02-Aug-2025 20:39:29 UTC] Paramètres de la requête: [1]
[02-Aug-2025 20:39:29 UTC] Nombre de pages trouvées: 1
[02-Aug-2025 20:39:29 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 20:39:29 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 20:40:00 UTC] Début de getMerchantId
[02-Aug-2025 20:40:00 UTC] === MerchantId récupéré ===
[02-Aug-2025 20:40:00 UTC] MerchantId: 1
[02-Aug-2025 20:40:00 UTC] Plan d'abonnement trouvé: 2
[02-Aug-2025 20:40:00 UTC] === Début getLandingPages ===
[02-Aug-2025 20:40:00 UTC] merchantId reçu: 1
[02-Aug-2025 20:40:00 UTC] Type de connexion DB: PDO
[02-Aug-2025 20:40:00 UTC] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 20:40:00 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 20:40:00 UTC] === Exécution de la requête SQL ===
[02-Aug-2025 20:40:00 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 20:40:00 UTC] MerchantId: 1
[02-Aug-2025 20:40:00 UTC] Paramètres de la requête: [1]
[02-Aug-2025 20:40:00 UTC] Nombre de pages trouvées: 1
[02-Aug-2025 20:40:00 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 20:40:00 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 20:40:00 UTC] Début de getMerchantId
[02-Aug-2025 20:40:00 UTC] === MerchantId récupéré ===
[02-Aug-2025 20:40:00 UTC] MerchantId: 1
[02-Aug-2025 20:40:00 UTC] Plan d'abonnement trouvé: 2
[02-Aug-2025 20:40:00 UTC] === Début getLandingPages ===
[02-Aug-2025 20:40:00 UTC] merchantId reçu: 1
[02-Aug-2025 20:40:00 UTC] Type de connexion DB: PDO
[02-Aug-2025 20:40:00 UTC] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 20:40:00 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 20:40:00 UTC] === Exécution de la requête SQL ===
[02-Aug-2025 20:40:00 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 20:40:00 UTC] MerchantId: 1
[02-Aug-2025 20:40:00 UTC] Paramètres de la requête: [1]
[02-Aug-2025 20:40:00 UTC] Nombre de pages trouvées: 1
[02-Aug-2025 20:40:00 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 20:40:00 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 20:40:03 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[02-Aug-2025 20:40:03 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[02-Aug-2025 21:17:31 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[02-Aug-2025 21:17:32 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[02-Aug-2025 21:20:15 UTC] Début de getMerchantId
[02-Aug-2025 21:20:15 UTC] === MerchantId récupéré ===
[02-Aug-2025 21:20:15 UTC] MerchantId: 1
[02-Aug-2025 21:20:15 UTC] Plan d'abonnement trouvé: 2
[02-Aug-2025 21:20:15 UTC] === Début getLandingPages ===
[02-Aug-2025 21:20:15 UTC] merchantId reçu: 1
[02-Aug-2025 21:20:15 UTC] Type de connexion DB: PDO
[02-Aug-2025 21:20:15 UTC] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 21:20:15 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 21:20:15 UTC] === Exécution de la requête SQL ===
[02-Aug-2025 21:20:15 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 21:20:15 UTC] MerchantId: 1
[02-Aug-2025 21:20:15 UTC] Paramètres de la requête: [1]
[02-Aug-2025 21:20:15 UTC] Nombre de pages trouvées: 1
[02-Aug-2025 21:20:15 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 21:20:15 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 21:20:15 UTC] Début de getMerchantId
[02-Aug-2025 21:20:15 UTC] === MerchantId récupéré ===
[02-Aug-2025 21:20:15 UTC] MerchantId: 1
[02-Aug-2025 21:20:15 UTC] Plan d'abonnement trouvé: 2
[02-Aug-2025 21:20:15 UTC] === Début getLandingPages ===
[02-Aug-2025 21:20:15 UTC] merchantId reçu: 1
[02-Aug-2025 21:20:15 UTC] Type de connexion DB: PDO
[02-Aug-2025 21:20:15 UTC] Préparation de la requête SQL pour getLandingPages
[02-Aug-2025 21:20:15 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 21:20:15 UTC] === Exécution de la requête SQL ===
[02-Aug-2025 21:20:15 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[02-Aug-2025 21:20:15 UTC] MerchantId: 1
[02-Aug-2025 21:20:15 UTC] Paramètres de la requête: [1]
[02-Aug-2025 21:20:15 UTC] Nombre de pages trouvées: 1
[02-Aug-2025 21:20:15 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 21:20:15 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[02-Aug-2025 21:30:44 UTC] Début de getMerchantId
[02-Aug-2025 21:30:46 UTC] === MerchantId récupéré ===
[02-Aug-2025 21:30:46 UTC] MerchantId: 1
[02-Aug-2025 21:30:46 UTC] Plan d'abonnement trouvé: 2
[02-Aug-2025 21:47:38 UTC] Début de getMerchantId
[02-Aug-2025 21:47:38 UTC] === MerchantId récupéré ===
[02-Aug-2025 21:47:38 UTC] MerchantId: 1
[02-Aug-2025 21:47:38 UTC] Plan d'abonnement trouvé: 2
[02-Aug-2025 21:48:15 UTC] Début de getMerchantId
[02-Aug-2025 21:48:15 UTC] === MerchantId récupéré ===
[02-Aug-2025 21:48:15 UTC] MerchantId: 1
[02-Aug-2025 21:48:15 UTC] Plan d'abonnement trouvé: 2
[02-Aug-2025 21:50:44 UTC] Début de getMerchantId
[02-Aug-2025 21:50:45 UTC] === MerchantId récupéré ===
[02-Aug-2025 21:50:45 UTC] MerchantId: 1
[02-Aug-2025 21:50:45 UTC] Plan d'abonnement trouvé: 2
[02-Aug-2025 22:24:55 UTC] Début de getMerchantId
[02-Aug-2025 22:24:55 UTC] === MerchantId récupéré ===
[02-Aug-2025 22:24:55 UTC] MerchantId: 1
[02-Aug-2025 22:24:55 UTC] Plan d'abonnement trouvé: 2
[03-Aug-2025 08:28:51 UTC] Début de getMerchantId
[03-Aug-2025 08:28:51 UTC] === MerchantId récupéré ===
[03-Aug-2025 08:28:51 UTC] MerchantId: 1
[03-Aug-2025 08:28:51 UTC] Plan d'abonnement trouvé: 2
[03-Aug-2025 08:28:51 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 08:28:51 UTC] Exception générale: Store non trouvé
[03-Aug-2025 08:30:15 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 08:30:15 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 08:37:41 UTC] Début de getMerchantId
[03-Aug-2025 08:37:41 UTC] === MerchantId récupéré ===
[03-Aug-2025 08:37:41 UTC] MerchantId: 1
[03-Aug-2025 08:37:41 UTC] Plan d'abonnement trouvé: 2
[03-Aug-2025 08:37:41 UTC] === Début getLandingPages ===
[03-Aug-2025 08:37:41 UTC] merchantId reçu: 1
[03-Aug-2025 08:37:41 UTC] Type de connexion DB: PDO
[03-Aug-2025 08:37:41 UTC] Préparation de la requête SQL pour getLandingPages
[03-Aug-2025 08:37:41 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 08:37:41 UTC] === Exécution de la requête SQL ===
[03-Aug-2025 08:37:41 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 08:37:41 UTC] MerchantId: 1
[03-Aug-2025 08:37:41 UTC] Paramètres de la requête: [1]
[03-Aug-2025 08:37:41 UTC] Nombre de pages trouvées: 1
[03-Aug-2025 08:37:41 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 08:37:41 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 08:37:41 UTC] Début de getMerchantId
[03-Aug-2025 08:37:41 UTC] === MerchantId récupéré ===
[03-Aug-2025 08:37:41 UTC] MerchantId: 1
[03-Aug-2025 08:37:41 UTC] Plan d'abonnement trouvé: 2
[03-Aug-2025 08:37:42 UTC] === Début getLandingPages ===
[03-Aug-2025 08:37:42 UTC] merchantId reçu: 1
[03-Aug-2025 08:37:42 UTC] Type de connexion DB: PDO
[03-Aug-2025 08:37:42 UTC] Préparation de la requête SQL pour getLandingPages
[03-Aug-2025 08:37:42 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 08:37:42 UTC] === Exécution de la requête SQL ===
[03-Aug-2025 08:37:42 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 08:37:42 UTC] MerchantId: 1
[03-Aug-2025 08:37:42 UTC] Paramètres de la requête: [1]
[03-Aug-2025 08:37:42 UTC] Nombre de pages trouvées: 1
[03-Aug-2025 08:37:42 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 08:37:42 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 08:37:55 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[03-Aug-2025 08:37:56 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[03-Aug-2025 10:07:59 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 10:08:00 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 10:23:41 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 10:23:41 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 10:23:44 UTC] Début de getMerchantId
[03-Aug-2025 10:23:44 UTC] === MerchantId récupéré ===
[03-Aug-2025 10:23:44 UTC] MerchantId: 1
[03-Aug-2025 10:23:44 UTC] Plan d'abonnement trouvé: 2
[03-Aug-2025 10:23:44 UTC] === Début getLandingPages ===
[03-Aug-2025 10:23:44 UTC] merchantId reçu: 1
[03-Aug-2025 10:23:44 UTC] Type de connexion DB: PDO
[03-Aug-2025 10:23:44 UTC] Préparation de la requête SQL pour getLandingPages
[03-Aug-2025 10:23:44 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 10:23:44 UTC] === Exécution de la requête SQL ===
[03-Aug-2025 10:23:44 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 10:23:44 UTC] MerchantId: 1
[03-Aug-2025 10:23:44 UTC] Paramètres de la requête: [1]
[03-Aug-2025 10:23:44 UTC] Nombre de pages trouvées: 1
[03-Aug-2025 10:23:44 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 10:23:44 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 10:23:44 UTC] Début de getMerchantId
[03-Aug-2025 10:23:44 UTC] === MerchantId récupéré ===
[03-Aug-2025 10:23:44 UTC] MerchantId: 1
[03-Aug-2025 10:23:44 UTC] Plan d'abonnement trouvé: 2
[03-Aug-2025 10:23:44 UTC] === Début getLandingPages ===
[03-Aug-2025 10:23:44 UTC] merchantId reçu: 1
[03-Aug-2025 10:23:44 UTC] Type de connexion DB: PDO
[03-Aug-2025 10:23:44 UTC] Préparation de la requête SQL pour getLandingPages
[03-Aug-2025 10:23:44 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 10:23:44 UTC] === Exécution de la requête SQL ===
[03-Aug-2025 10:23:44 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 10:23:44 UTC] MerchantId: 1
[03-Aug-2025 10:23:44 UTC] Paramètres de la requête: [1]
[03-Aug-2025 10:23:44 UTC] Nombre de pages trouvées: 1
[03-Aug-2025 10:23:44 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 10:23:44 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 10:48:12 UTC] Début de getMerchantId
[03-Aug-2025 10:48:12 UTC] === MerchantId récupéré ===
[03-Aug-2025 10:48:12 UTC] MerchantId: 1
[03-Aug-2025 10:48:12 UTC] Plan d'abonnement trouvé: 2
[03-Aug-2025 10:48:13 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 10:48:13 UTC] Exception générale: Store non trouvé
[03-Aug-2025 10:48:31 UTC] Début de getMerchantId
[03-Aug-2025 10:48:31 UTC] === MerchantId récupéré ===
[03-Aug-2025 10:48:31 UTC] MerchantId: 1
[03-Aug-2025 10:48:31 UTC] Plan d'abonnement trouvé: 2
[03-Aug-2025 10:48:32 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 10:48:32 UTC] Exception générale: Store non trouvé
[03-Aug-2025 10:48:50 UTC] Début de getMerchantId
[03-Aug-2025 10:48:50 UTC] === MerchantId récupéré ===
[03-Aug-2025 10:48:50 UTC] MerchantId: 1
[03-Aug-2025 10:48:50 UTC] Plan d'abonnement trouvé: 2
[03-Aug-2025 10:48:50 UTC] === Début getLandingPages ===
[03-Aug-2025 10:48:50 UTC] merchantId reçu: 1
[03-Aug-2025 10:48:50 UTC] Type de connexion DB: PDO
[03-Aug-2025 10:48:50 UTC] Préparation de la requête SQL pour getLandingPages
[03-Aug-2025 10:48:50 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 10:48:50 UTC] === Exécution de la requête SQL ===
[03-Aug-2025 10:48:50 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 10:48:50 UTC] MerchantId: 1
[03-Aug-2025 10:48:50 UTC] Paramètres de la requête: [1]
[03-Aug-2025 10:48:50 UTC] Nombre de pages trouvées: 1
[03-Aug-2025 10:48:50 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 10:48:50 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 10:48:50 UTC] Début de getMerchantId
[03-Aug-2025 10:48:50 UTC] === MerchantId récupéré ===
[03-Aug-2025 10:48:50 UTC] MerchantId: 1
[03-Aug-2025 10:48:50 UTC] Plan d'abonnement trouvé: 2
[03-Aug-2025 10:48:50 UTC] === Début getLandingPages ===
[03-Aug-2025 10:48:50 UTC] merchantId reçu: 1
[03-Aug-2025 10:48:50 UTC] Type de connexion DB: PDO
[03-Aug-2025 10:48:50 UTC] Préparation de la requête SQL pour getLandingPages
[03-Aug-2025 10:48:50 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 10:48:50 UTC] === Exécution de la requête SQL ===
[03-Aug-2025 10:48:50 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 10:48:51 UTC] MerchantId: 1
[03-Aug-2025 10:48:51 UTC] Paramètres de la requête: [1]
[03-Aug-2025 10:48:51 UTC] Nombre de pages trouvées: 1
[03-Aug-2025 10:48:51 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 10:48:51 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 10:48:55 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[03-Aug-2025 10:48:55 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[03-Aug-2025 11:00:27 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 11:00:28 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 11:00:42 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 11:00:42 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 11:08:45 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 11:08:46 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 11:08:49 UTC] Début de getMerchantId
[03-Aug-2025 11:08:49 UTC] === MerchantId récupéré ===
[03-Aug-2025 11:08:49 UTC] MerchantId: 1
[03-Aug-2025 11:08:49 UTC] Plan d'abonnement trouvé: 2
[03-Aug-2025 11:08:49 UTC] === Début getLandingPages ===
[03-Aug-2025 11:08:49 UTC] merchantId reçu: 1
[03-Aug-2025 11:08:49 UTC] Type de connexion DB: PDO
[03-Aug-2025 11:08:49 UTC] Préparation de la requête SQL pour getLandingPages
[03-Aug-2025 11:08:49 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 11:08:49 UTC] === Exécution de la requête SQL ===
[03-Aug-2025 11:08:49 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 11:08:49 UTC] MerchantId: 1
[03-Aug-2025 11:08:49 UTC] Paramètres de la requête: [1]
[03-Aug-2025 11:08:49 UTC] Nombre de pages trouvées: 1
[03-Aug-2025 11:08:49 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 11:08:49 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 11:08:49 UTC] Début de getMerchantId
[03-Aug-2025 11:08:49 UTC] === MerchantId récupéré ===
[03-Aug-2025 11:08:49 UTC] MerchantId: 1
[03-Aug-2025 11:08:49 UTC] Plan d'abonnement trouvé: 2
[03-Aug-2025 11:08:49 UTC] === Début getLandingPages ===
[03-Aug-2025 11:08:49 UTC] merchantId reçu: 1
[03-Aug-2025 11:08:49 UTC] Type de connexion DB: PDO
[03-Aug-2025 11:08:49 UTC] Préparation de la requête SQL pour getLandingPages
[03-Aug-2025 11:08:49 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 11:08:49 UTC] === Exécution de la requête SQL ===
[03-Aug-2025 11:08:49 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 11:08:49 UTC] MerchantId: 1
[03-Aug-2025 11:08:49 UTC] Paramètres de la requête: [1]
[03-Aug-2025 11:08:49 UTC] Nombre de pages trouvées: 1
[03-Aug-2025 11:08:49 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 11:08:49 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 11:09:01 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[03-Aug-2025 11:09:02 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[03-Aug-2025 11:23:22 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 11:23:22 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 11:23:28 UTC] Début de getMerchantId
[03-Aug-2025 11:23:28 UTC] === MerchantId récupéré ===
[03-Aug-2025 11:23:28 UTC] MerchantId: 1
[03-Aug-2025 11:23:28 UTC] Plan d'abonnement trouvé: 2
[03-Aug-2025 11:23:28 UTC] === Début getLandingPages ===
[03-Aug-2025 11:23:28 UTC] merchantId reçu: 1
[03-Aug-2025 11:23:28 UTC] Type de connexion DB: PDO
[03-Aug-2025 11:23:28 UTC] Préparation de la requête SQL pour getLandingPages
[03-Aug-2025 11:23:28 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 11:23:28 UTC] === Exécution de la requête SQL ===
[03-Aug-2025 11:23:28 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 11:23:28 UTC] MerchantId: 1
[03-Aug-2025 11:23:28 UTC] Paramètres de la requête: [1]
[03-Aug-2025 11:23:28 UTC] Nombre de pages trouvées: 1
[03-Aug-2025 11:23:28 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 11:23:28 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 11:23:28 UTC] Début de getMerchantId
[03-Aug-2025 11:23:28 UTC] === MerchantId récupéré ===
[03-Aug-2025 11:23:28 UTC] MerchantId: 1
[03-Aug-2025 11:23:28 UTC] Plan d'abonnement trouvé: 2
[03-Aug-2025 11:23:28 UTC] === Début getLandingPages ===
[03-Aug-2025 11:23:28 UTC] merchantId reçu: 1
[03-Aug-2025 11:23:28 UTC] Type de connexion DB: PDO
[03-Aug-2025 11:23:28 UTC] Préparation de la requête SQL pour getLandingPages
[03-Aug-2025 11:23:28 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 11:23:28 UTC] === Exécution de la requête SQL ===
[03-Aug-2025 11:23:28 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 11:23:28 UTC] MerchantId: 1
[03-Aug-2025 11:23:28 UTC] Paramètres de la requête: [1]
[03-Aug-2025 11:23:28 UTC] Nombre de pages trouvées: 1
[03-Aug-2025 11:23:28 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 11:23:28 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 12:59:05 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 12:59:10 UTC] Erreur de connexion PDO: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it
[03-Aug-2025 12:59:10 UTC] Exception générale: Erreur de connexion à la base de données: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it
[03-Aug-2025 12:59:15 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 12:59:19 UTC] Erreur de connexion PDO: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it
[03-Aug-2025 12:59:19 UTC] Exception générale: Erreur de connexion à la base de données: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it
[03-Aug-2025 12:59:50 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 12:59:54 UTC] Erreur de connexion PDO: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it
[03-Aug-2025 12:59:54 UTC] Exception générale: Erreur de connexion à la base de données: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it
[03-Aug-2025 13:00:02 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 13:00:03 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 13:00:03 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 13:00:07 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 13:00:08 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 13:01:16 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[03-Aug-2025 13:01:16 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[03-Aug-2025 13:01:49 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[03-Aug-2025 13:01:49 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[03-Aug-2025 15:19:39 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 15:19:41 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 15:21:27 UTC] Début de getMerchantId
[03-Aug-2025 15:21:27 UTC] === MerchantId récupéré ===
[03-Aug-2025 15:21:27 UTC] MerchantId: 1
[03-Aug-2025 15:21:27 UTC] Plan d'abonnement trouvé: 2
[03-Aug-2025 15:21:27 UTC] === Début getLandingPages ===
[03-Aug-2025 15:21:27 UTC] merchantId reçu: 1
[03-Aug-2025 15:21:27 UTC] Type de connexion DB: PDO
[03-Aug-2025 15:21:27 UTC] Préparation de la requête SQL pour getLandingPages
[03-Aug-2025 15:21:27 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 15:21:28 UTC] === Exécution de la requête SQL ===
[03-Aug-2025 15:21:28 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 15:21:28 UTC] MerchantId: 1
[03-Aug-2025 15:21:28 UTC] Paramètres de la requête: [1]
[03-Aug-2025 15:21:28 UTC] Nombre de pages trouvées: 1
[03-Aug-2025 15:21:28 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 15:21:28 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 15:21:28 UTC] Début de getMerchantId
[03-Aug-2025 15:21:28 UTC] === MerchantId récupéré ===
[03-Aug-2025 15:21:28 UTC] MerchantId: 1
[03-Aug-2025 15:21:28 UTC] Plan d'abonnement trouvé: 2
[03-Aug-2025 15:21:28 UTC] === Début getLandingPages ===
[03-Aug-2025 15:21:28 UTC] merchantId reçu: 1
[03-Aug-2025 15:21:28 UTC] Type de connexion DB: PDO
[03-Aug-2025 15:21:28 UTC] Préparation de la requête SQL pour getLandingPages
[03-Aug-2025 15:21:28 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 15:21:28 UTC] === Exécution de la requête SQL ===
[03-Aug-2025 15:21:28 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 15:21:28 UTC] MerchantId: 1
[03-Aug-2025 15:21:28 UTC] Paramètres de la requête: [1]
[03-Aug-2025 15:21:28 UTC] Nombre de pages trouvées: 1
[03-Aug-2025 15:21:28 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 15:21:28 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 16:20:48 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 16:20:50 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 16:21:18 UTC] Début de getMerchantId
[03-Aug-2025 16:21:18 UTC] === MerchantId récupéré ===
[03-Aug-2025 16:21:18 UTC] MerchantId: 1
[03-Aug-2025 16:21:18 UTC] Plan d'abonnement trouvé: 2
[03-Aug-2025 16:21:18 UTC] === Début getLandingPages ===
[03-Aug-2025 16:21:18 UTC] merchantId reçu: 1
[03-Aug-2025 16:21:18 UTC] Type de connexion DB: PDO
[03-Aug-2025 16:21:18 UTC] Préparation de la requête SQL pour getLandingPages
[03-Aug-2025 16:21:18 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 16:21:18 UTC] === Exécution de la requête SQL ===
[03-Aug-2025 16:21:18 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 16:21:18 UTC] MerchantId: 1
[03-Aug-2025 16:21:18 UTC] Paramètres de la requête: [1]
[03-Aug-2025 16:21:18 UTC] Nombre de pages trouvées: 1
[03-Aug-2025 16:21:18 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 16:21:18 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 16:27:48 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 16:27:50 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 16:28:02 UTC] Début de getMerchantId
[03-Aug-2025 16:28:02 UTC] === MerchantId récupéré ===
[03-Aug-2025 16:28:02 UTC] MerchantId: 1
[03-Aug-2025 16:28:02 UTC] Plan d'abonnement trouvé: 2
[03-Aug-2025 16:28:02 UTC] === Début getLandingPages ===
[03-Aug-2025 16:28:02 UTC] merchantId reçu: 1
[03-Aug-2025 16:28:02 UTC] Type de connexion DB: PDO
[03-Aug-2025 16:28:02 UTC] Préparation de la requête SQL pour getLandingPages
[03-Aug-2025 16:28:02 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 16:28:02 UTC] === Exécution de la requête SQL ===
[03-Aug-2025 16:28:02 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 16:28:02 UTC] MerchantId: 1
[03-Aug-2025 16:28:02 UTC] Paramètres de la requête: [1]
[03-Aug-2025 16:28:02 UTC] Nombre de pages trouvées: 1
[03-Aug-2025 16:28:02 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 16:28:02 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 16:28:02 UTC] Début de getMerchantId
[03-Aug-2025 16:28:02 UTC] === MerchantId récupéré ===
[03-Aug-2025 16:28:02 UTC] MerchantId: 1
[03-Aug-2025 16:28:02 UTC] Plan d'abonnement trouvé: 2
[03-Aug-2025 16:28:02 UTC] === Début getLandingPages ===
[03-Aug-2025 16:28:02 UTC] merchantId reçu: 1
[03-Aug-2025 16:28:02 UTC] Type de connexion DB: PDO
[03-Aug-2025 16:28:02 UTC] Préparation de la requête SQL pour getLandingPages
[03-Aug-2025 16:28:02 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 16:28:02 UTC] === Exécution de la requête SQL ===
[03-Aug-2025 16:28:02 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 16:28:02 UTC] MerchantId: 1
[03-Aug-2025 16:28:02 UTC] Paramètres de la requête: [1]
[03-Aug-2025 16:28:02 UTC] Nombre de pages trouvées: 1
[03-Aug-2025 16:28:02 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 16:28:02 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 16:45:03 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 16:45:03 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 16:45:04 UTC] Début de getMerchantId
[03-Aug-2025 16:45:04 UTC] === MerchantId récupéré ===
[03-Aug-2025 16:45:04 UTC] MerchantId: 1
[03-Aug-2025 16:45:04 UTC] Plan d'abonnement trouvé: 2
[03-Aug-2025 16:45:04 UTC] === Début getLandingPages ===
[03-Aug-2025 16:45:04 UTC] merchantId reçu: 1
[03-Aug-2025 16:45:04 UTC] Type de connexion DB: PDO
[03-Aug-2025 16:45:04 UTC] Préparation de la requête SQL pour getLandingPages
[03-Aug-2025 16:45:04 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 16:45:04 UTC] === Exécution de la requête SQL ===
[03-Aug-2025 16:45:04 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 16:45:04 UTC] MerchantId: 1
[03-Aug-2025 16:45:04 UTC] Paramètres de la requête: [1]
[03-Aug-2025 16:45:04 UTC] Nombre de pages trouvées: 1
[03-Aug-2025 16:45:04 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 16:45:04 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 16:45:04 UTC] Début de getMerchantId
[03-Aug-2025 16:45:04 UTC] === MerchantId récupéré ===
[03-Aug-2025 16:45:04 UTC] MerchantId: 1
[03-Aug-2025 16:45:04 UTC] Plan d'abonnement trouvé: 2
[03-Aug-2025 16:45:04 UTC] === Début getLandingPages ===
[03-Aug-2025 16:45:04 UTC] merchantId reçu: 1
[03-Aug-2025 16:45:04 UTC] Type de connexion DB: PDO
[03-Aug-2025 16:45:04 UTC] Préparation de la requête SQL pour getLandingPages
[03-Aug-2025 16:45:04 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 16:45:04 UTC] === Exécution de la requête SQL ===
[03-Aug-2025 16:45:04 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 16:45:04 UTC] MerchantId: 1
[03-Aug-2025 16:45:04 UTC] Paramètres de la requête: [1]
[03-Aug-2025 16:45:04 UTC] Nombre de pages trouvées: 1
[03-Aug-2025 16:45:04 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 16:45:04 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 16:48:13 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[03-Aug-2025 16:48:13 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[03-Aug-2025 17:25:56 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[03-Aug-2025 17:25:58 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[03-Aug-2025 17:26:02 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[03-Aug-2025 17:26:03 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[03-Aug-2025 17:26:10 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[03-Aug-2025 17:26:11 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[03-Aug-2025 18:33:19 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[03-Aug-2025 18:33:19 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[03-Aug-2025 18:33:22 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[03-Aug-2025 18:33:22 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[03-Aug-2025 19:35:52 UTC] Début de getMerchantId
[03-Aug-2025 19:35:52 UTC] === MerchantId récupéré ===
[03-Aug-2025 19:35:52 UTC] MerchantId: 1
[03-Aug-2025 19:35:53 UTC] Plan d'abonnement trouvé: 2
[03-Aug-2025 19:35:53 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 19:35:53 UTC] Exception générale: Store non trouvé
[03-Aug-2025 19:36:08 UTC] Début de getMerchantId
[03-Aug-2025 19:36:08 UTC] === MerchantId récupéré ===
[03-Aug-2025 19:36:08 UTC] MerchantId: 1
[03-Aug-2025 19:36:08 UTC] Plan d'abonnement trouvé: 2
[03-Aug-2025 19:36:08 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 19:36:08 UTC] Exception générale: Store non trouvé
[03-Aug-2025 20:27:39 UTC] Début de getMerchantId
[03-Aug-2025 20:27:39 UTC] === MerchantId récupéré ===
[03-Aug-2025 20:27:40 UTC] MerchantId: 1
[03-Aug-2025 20:27:40 UTC] Plan d'abonnement trouvé: 2
[03-Aug-2025 20:27:40 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 20:27:40 UTC] Exception générale: Store non trouvé
[03-Aug-2025 20:28:20 UTC] Début de getMerchantId
[03-Aug-2025 20:28:20 UTC] === MerchantId récupéré ===
[03-Aug-2025 20:28:20 UTC] MerchantId: 1
[03-Aug-2025 20:28:20 UTC] Plan d'abonnement trouvé: 2
[03-Aug-2025 20:28:20 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 20:28:20 UTC] Exception générale: Store non trouvé
[03-Aug-2025 20:40:26 UTC] Début de getMerchantId
[03-Aug-2025 20:40:26 UTC] === MerchantId récupéré ===
[03-Aug-2025 20:40:26 UTC] MerchantId: 1
[03-Aug-2025 20:40:26 UTC] Plan d'abonnement trouvé: 2
[03-Aug-2025 20:40:27 UTC] Tentative de connexion avec DSN: mysql:host=localhost;port=3307;dbname=landingpage_new;charset=utf8mb4
[03-Aug-2025 20:40:27 UTC] Erreur lors de l'exécution de la requête: SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.user_id' in 'on clause'
[03-Aug-2025 20:40:27 UTC] Exception générale: Erreur lors de la récupération du store: SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.user_id' in 'on clause'
[03-Aug-2025 21:09:28 UTC] Erreur lors de l'exécution de la requête: SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.user_id' in 'on clause'
[03-Aug-2025 21:09:28 UTC] Exception générale: Erreur lors de la récupération du store: SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.user_id' in 'on clause'
[03-Aug-2025 21:09:47 UTC] Début de getMerchantId
[03-Aug-2025 21:09:47 UTC] === MerchantId récupéré ===
[03-Aug-2025 21:09:47 UTC] MerchantId: 1
[03-Aug-2025 21:09:47 UTC] Plan d'abonnement trouvé: 2
[03-Aug-2025 21:09:47 UTC] === Début getLandingPages ===
[03-Aug-2025 21:09:47 UTC] merchantId reçu: 1
[03-Aug-2025 21:09:47 UTC] Type de connexion DB: PDO
[03-Aug-2025 21:09:47 UTC] Préparation de la requête SQL pour getLandingPages
[03-Aug-2025 21:09:47 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 21:09:47 UTC] === Exécution de la requête SQL ===
[03-Aug-2025 21:09:47 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 21:09:47 UTC] MerchantId: 1
[03-Aug-2025 21:09:47 UTC] Paramètres de la requête: [1]
[03-Aug-2025 21:09:47 UTC] Nombre de pages trouvées: 1
[03-Aug-2025 21:09:47 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 21:09:47 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 21:09:48 UTC] Début de getMerchantId
[03-Aug-2025 21:09:48 UTC] === MerchantId récupéré ===
[03-Aug-2025 21:09:48 UTC] MerchantId: 1
[03-Aug-2025 21:09:48 UTC] Plan d'abonnement trouvé: 2
[03-Aug-2025 21:09:48 UTC] === Début getLandingPages ===
[03-Aug-2025 21:09:48 UTC] merchantId reçu: 1
[03-Aug-2025 21:09:48 UTC] Type de connexion DB: PDO
[03-Aug-2025 21:09:48 UTC] Préparation de la requête SQL pour getLandingPages
[03-Aug-2025 21:09:48 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 21:09:48 UTC] === Exécution de la requête SQL ===
[03-Aug-2025 21:09:48 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 21:09:48 UTC] MerchantId: 1
[03-Aug-2025 21:09:48 UTC] Paramètres de la requête: [1]
[03-Aug-2025 21:09:48 UTC] Nombre de pages trouvées: 1
[03-Aug-2025 21:09:48 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 21:09:48 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 21:09:57 UTC] Erreur lors de l'exécution de la requête: SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.user_id' in 'on clause'
[03-Aug-2025 21:09:57 UTC] Exception générale: Erreur lors de la récupération des stores: SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.user_id' in 'on clause'
[03-Aug-2025 21:09:57 UTC] Erreur lors de l'exécution de la requête: SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.user_id' in 'on clause'
[03-Aug-2025 21:09:57 UTC] Exception générale: Erreur lors de la récupération des stores: SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.user_id' in 'on clause'
[03-Aug-2025 21:12:45 UTC] Début de getMerchantId
[03-Aug-2025 21:12:45 UTC] === MerchantId récupéré ===
[03-Aug-2025 21:12:45 UTC] MerchantId: 1
[03-Aug-2025 21:12:45 UTC] Plan d'abonnement trouvé: 2
[03-Aug-2025 21:12:45 UTC] Erreur lors de l'exécution de la requête: SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.user_id' in 'on clause'
[03-Aug-2025 21:12:45 UTC] Exception générale: Erreur lors de la récupération du store: SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.user_id' in 'on clause'
[03-Aug-2025 21:25:11 UTC] Début de getMerchantId
[03-Aug-2025 21:25:11 UTC] === MerchantId récupéré ===
[03-Aug-2025 21:25:11 UTC] MerchantId: 1
[03-Aug-2025 21:25:11 UTC] Plan d'abonnement trouvé: 2
[03-Aug-2025 21:25:11 UTC] === Début getLandingPages ===
[03-Aug-2025 21:25:11 UTC] merchantId reçu: 1
[03-Aug-2025 21:25:11 UTC] Type de connexion DB: PDO
[03-Aug-2025 21:25:11 UTC] Préparation de la requête SQL pour getLandingPages
[03-Aug-2025 21:25:11 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 21:25:11 UTC] === Exécution de la requête SQL ===
[03-Aug-2025 21:25:11 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 21:25:11 UTC] MerchantId: 1
[03-Aug-2025 21:25:11 UTC] Paramètres de la requête: [1]
[03-Aug-2025 21:25:11 UTC] Nombre de pages trouvées: 1
[03-Aug-2025 21:25:11 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 21:25:11 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 21:25:11 UTC] Début de getMerchantId
[03-Aug-2025 21:25:11 UTC] === MerchantId récupéré ===
[03-Aug-2025 21:25:11 UTC] MerchantId: 1
[03-Aug-2025 21:25:11 UTC] Plan d'abonnement trouvé: 2
[03-Aug-2025 21:25:11 UTC] === Début getLandingPages ===
[03-Aug-2025 21:25:11 UTC] merchantId reçu: 1
[03-Aug-2025 21:25:11 UTC] Type de connexion DB: PDO
[03-Aug-2025 21:25:11 UTC] Préparation de la requête SQL pour getLandingPages
[03-Aug-2025 21:25:11 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 21:25:11 UTC] === Exécution de la requête SQL ===
[03-Aug-2025 21:25:11 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 21:25:11 UTC] MerchantId: 1
[03-Aug-2025 21:25:11 UTC] Paramètres de la requête: [1]
[03-Aug-2025 21:25:11 UTC] Nombre de pages trouvées: 1
[03-Aug-2025 21:25:11 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 21:25:11 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 21:47:05 UTC] Erreur lors de l'exécution de la requête: SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.user_id' in 'on clause'
[03-Aug-2025 21:47:05 UTC] Exception générale: Erreur lors de la récupération des stores: SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.user_id' in 'on clause'
[03-Aug-2025 21:47:05 UTC] Erreur lors de l'exécution de la requête: SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.user_id' in 'on clause'
[03-Aug-2025 21:47:05 UTC] Exception générale: Erreur lors de la récupération des stores: SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.user_id' in 'on clause'
[03-Aug-2025 21:47:12 UTC] Début de getMerchantId
[03-Aug-2025 21:47:12 UTC] === MerchantId récupéré ===
[03-Aug-2025 21:47:12 UTC] MerchantId: 1
[03-Aug-2025 21:47:12 UTC] Plan d'abonnement trouvé: 2
[03-Aug-2025 21:47:12 UTC] === Début getLandingPages ===
[03-Aug-2025 21:47:12 UTC] merchantId reçu: 1
[03-Aug-2025 21:47:12 UTC] Type de connexion DB: PDO
[03-Aug-2025 21:47:12 UTC] Préparation de la requête SQL pour getLandingPages
[03-Aug-2025 21:47:12 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 21:47:12 UTC] === Exécution de la requête SQL ===
[03-Aug-2025 21:47:12 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 21:47:12 UTC] MerchantId: 1
[03-Aug-2025 21:47:12 UTC] Paramètres de la requête: [1]
[03-Aug-2025 21:47:12 UTC] Nombre de pages trouvées: 1
[03-Aug-2025 21:47:12 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 21:47:12 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 21:47:13 UTC] Début de getMerchantId
[03-Aug-2025 21:47:13 UTC] === MerchantId récupéré ===
[03-Aug-2025 21:47:13 UTC] MerchantId: 1
[03-Aug-2025 21:47:13 UTC] Plan d'abonnement trouvé: 2
[03-Aug-2025 21:47:13 UTC] === Début getLandingPages ===
[03-Aug-2025 21:47:13 UTC] merchantId reçu: 1
[03-Aug-2025 21:47:13 UTC] Type de connexion DB: PDO
[03-Aug-2025 21:47:13 UTC] Préparation de la requête SQL pour getLandingPages
[03-Aug-2025 21:47:13 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 21:47:13 UTC] === Exécution de la requête SQL ===
[03-Aug-2025 21:47:13 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 21:47:13 UTC] MerchantId: 1
[03-Aug-2025 21:47:13 UTC] Paramètres de la requête: [1]
[03-Aug-2025 21:47:13 UTC] Nombre de pages trouvées: 1
[03-Aug-2025 21:47:13 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 21:47:13 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 22:15:06 UTC] Erreur lors de l'exécution de la requête: SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.user_id' in 'on clause'
[03-Aug-2025 22:15:06 UTC] Exception générale: Erreur lors de la récupération des stores: SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.user_id' in 'on clause'
[03-Aug-2025 22:15:07 UTC] Erreur lors de l'exécution de la requête: SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.user_id' in 'on clause'
[03-Aug-2025 22:15:07 UTC] Exception générale: Erreur lors de la récupération des stores: SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.user_id' in 'on clause'
[03-Aug-2025 22:41:48 UTC] Exception générale: SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.user_id' in 'on clause'
[03-Aug-2025 22:41:48 UTC] Exception générale: SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.user_id' in 'on clause'
[03-Aug-2025 22:42:00 UTC] Exception générale: SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.user_id' in 'on clause'
[03-Aug-2025 22:42:01 UTC] Exception générale: SQLSTATE[42S22]: Column not found: 1054 Unknown column 's.user_id' in 'on clause'
[03-Aug-2025 22:42:04 UTC] Début de getMerchantId
[03-Aug-2025 22:42:04 UTC] === MerchantId récupéré ===
[03-Aug-2025 22:42:04 UTC] MerchantId: 1
[03-Aug-2025 22:42:05 UTC] Plan d'abonnement trouvé: 2
[03-Aug-2025 22:42:05 UTC] === Début getLandingPages ===
[03-Aug-2025 22:42:05 UTC] merchantId reçu: 1
[03-Aug-2025 22:42:05 UTC] Type de connexion DB: PDO
[03-Aug-2025 22:42:05 UTC] Préparation de la requête SQL pour getLandingPages
[03-Aug-2025 22:42:05 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 22:42:05 UTC] === Exécution de la requête SQL ===
[03-Aug-2025 22:42:05 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 22:42:05 UTC] MerchantId: 1
[03-Aug-2025 22:42:05 UTC] Paramètres de la requête: [1]
[03-Aug-2025 22:42:05 UTC] Nombre de pages trouvées: 1
[03-Aug-2025 22:42:05 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 22:42:05 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 22:42:05 UTC] Début de getMerchantId
[03-Aug-2025 22:42:05 UTC] === MerchantId récupéré ===
[03-Aug-2025 22:42:05 UTC] MerchantId: 1
[03-Aug-2025 22:42:05 UTC] Plan d'abonnement trouvé: 2
[03-Aug-2025 22:42:05 UTC] === Début getLandingPages ===
[03-Aug-2025 22:42:05 UTC] merchantId reçu: 1
[03-Aug-2025 22:42:05 UTC] Type de connexion DB: PDO
[03-Aug-2025 22:42:05 UTC] Préparation de la requête SQL pour getLandingPages
[03-Aug-2025 22:42:05 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 22:42:05 UTC] === Exécution de la requête SQL ===
[03-Aug-2025 22:42:05 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[03-Aug-2025 22:42:05 UTC] MerchantId: 1
[03-Aug-2025 22:42:05 UTC] Paramètres de la requête: [1]
[03-Aug-2025 22:42:06 UTC] Nombre de pages trouvées: 1
[03-Aug-2025 22:42:06 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 22:42:06 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[03-Aug-2025 22:42:08 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[03-Aug-2025 22:42:08 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[03-Aug-2025 22:50:54 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[03-Aug-2025 22:50:54 UTC] Error getting subscription stats: SQLSTATE[23000]: Integrity constraint violation: 1052 Column 'status' in field list is ambiguous
[04-Aug-2025 23:35:06 UTC] Début de getMerchantId
[04-Aug-2025 23:35:06 UTC] === MerchantId récupéré ===
[04-Aug-2025 23:35:06 UTC] MerchantId: 1
[04-Aug-2025 23:35:06 UTC] Plan d'abonnement trouvé: 2
[04-Aug-2025 23:36:42 UTC] Début de getMerchantId
[04-Aug-2025 23:36:43 UTC] === MerchantId récupéré ===
[04-Aug-2025 23:36:43 UTC] MerchantId: 1
[04-Aug-2025 23:36:43 UTC] Plan d'abonnement trouvé: 2
[05-Aug-2025 10:39:16 UTC] Début de getMerchantId
[05-Aug-2025 10:39:17 UTC] === MerchantId récupéré ===
[05-Aug-2025 10:39:17 UTC] MerchantId: 1
[05-Aug-2025 10:39:17 UTC] Plan d'abonnement trouvé: 2
[05-Aug-2025 10:53:07 UTC] Début de getMerchantId
[05-Aug-2025 10:53:07 UTC] === MerchantId récupéré ===
[05-Aug-2025 10:53:07 UTC] MerchantId: 1
[05-Aug-2025 10:53:07 UTC] Plan d'abonnement trouvé: 2
[05-Aug-2025 11:34:24 UTC] Début de getMerchantId
[05-Aug-2025 11:34:24 UTC] === MerchantId récupéré ===
[05-Aug-2025 11:34:24 UTC] MerchantId: 1
[05-Aug-2025 11:34:24 UTC] Plan d'abonnement trouvé: 2
[05-Aug-2025 11:36:37 UTC] Début de getMerchantId
[05-Aug-2025 11:36:37 UTC] === MerchantId récupéré ===
[05-Aug-2025 11:36:37 UTC] MerchantId: 1
[05-Aug-2025 11:36:37 UTC] Plan d'abonnement trouvé: 2
[05-Aug-2025 11:36:49 UTC] Début de getMerchantId
[05-Aug-2025 11:36:49 UTC] === MerchantId récupéré ===
[05-Aug-2025 11:36:50 UTC] MerchantId: 1
[05-Aug-2025 11:36:50 UTC] Plan d'abonnement trouvé: 2
[05-Aug-2025 12:06:43 UTC] Début de getMerchantId
[05-Aug-2025 12:06:44 UTC] === MerchantId récupéré ===
[05-Aug-2025 12:06:44 UTC] MerchantId: 1
[05-Aug-2025 12:06:44 UTC] Plan d'abonnement trouvé: 2
[05-Aug-2025 12:06:44 UTC] === Début getLandingPages ===
[05-Aug-2025 12:06:44 UTC] merchantId reçu: 1
[05-Aug-2025 12:06:44 UTC] Type de connexion DB: PDO
[05-Aug-2025 12:06:44 UTC] Préparation de la requête SQL pour getLandingPages
[05-Aug-2025 12:06:44 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[05-Aug-2025 12:06:44 UTC] === Exécution de la requête SQL ===
[05-Aug-2025 12:06:44 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[05-Aug-2025 12:06:44 UTC] MerchantId: 1
[05-Aug-2025 12:06:44 UTC] Paramètres de la requête: [1]
[05-Aug-2025 12:06:44 UTC] Nombre de pages trouvées: 1
[05-Aug-2025 12:06:44 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[05-Aug-2025 12:06:44 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[05-Aug-2025 12:41:30 UTC] Début de getMerchantId
[05-Aug-2025 12:41:30 UTC] === MerchantId récupéré ===
[05-Aug-2025 12:41:30 UTC] MerchantId: 1
[05-Aug-2025 12:41:30 UTC] Plan d'abonnement trouvé: 2
[05-Aug-2025 12:41:30 UTC] === Début getLandingPages ===
[05-Aug-2025 12:41:30 UTC] merchantId reçu: 1
[05-Aug-2025 12:41:30 UTC] Type de connexion DB: PDO
[05-Aug-2025 12:41:30 UTC] Préparation de la requête SQL pour getLandingPages
[05-Aug-2025 12:41:30 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[05-Aug-2025 12:41:30 UTC] === Exécution de la requête SQL ===
[05-Aug-2025 12:41:30 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[05-Aug-2025 12:41:30 UTC] MerchantId: 1
[05-Aug-2025 12:41:30 UTC] Paramètres de la requête: [1]
[05-Aug-2025 12:41:30 UTC] Nombre de pages trouvées: 1
[05-Aug-2025 12:41:30 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[05-Aug-2025 12:41:30 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[05-Aug-2025 12:46:18 UTC] Début de getMerchantId
[05-Aug-2025 12:46:18 UTC] === MerchantId récupéré ===
[05-Aug-2025 12:46:18 UTC] MerchantId: 1
[05-Aug-2025 12:46:18 UTC] Plan d'abonnement trouvé: 2
[05-Aug-2025 12:46:18 UTC] === Début getLandingPages ===
[05-Aug-2025 12:46:18 UTC] merchantId reçu: 1
[05-Aug-2025 12:46:18 UTC] Type de connexion DB: PDO
[05-Aug-2025 12:46:18 UTC] Préparation de la requête SQL pour getLandingPages
[05-Aug-2025 12:46:18 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[05-Aug-2025 12:46:18 UTC] === Exécution de la requête SQL ===
[05-Aug-2025 12:46:18 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[05-Aug-2025 12:46:18 UTC] MerchantId: 1
[05-Aug-2025 12:46:18 UTC] Paramètres de la requête: [1]
[05-Aug-2025 12:46:18 UTC] Nombre de pages trouvées: 1
[05-Aug-2025 12:46:18 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[05-Aug-2025 12:46:18 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[05-Aug-2025 13:24:31 UTC] Début de getMerchantId
[05-Aug-2025 13:24:31 UTC] === MerchantId récupéré ===
[05-Aug-2025 13:24:31 UTC] MerchantId: 1
[05-Aug-2025 13:24:31 UTC] Plan d'abonnement trouvé: 2
[05-Aug-2025 13:24:31 UTC] === Début getLandingPages ===
[05-Aug-2025 13:24:31 UTC] merchantId reçu: 1
[05-Aug-2025 13:24:31 UTC] Type de connexion DB: PDO
[05-Aug-2025 13:24:31 UTC] Préparation de la requête SQL pour getLandingPages
[05-Aug-2025 13:24:31 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[05-Aug-2025 13:24:31 UTC] === Exécution de la requête SQL ===
[05-Aug-2025 13:24:31 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[05-Aug-2025 13:24:31 UTC] MerchantId: 1
[05-Aug-2025 13:24:31 UTC] Paramètres de la requête: [1]
[05-Aug-2025 13:24:31 UTC] Nombre de pages trouvées: 1
[05-Aug-2025 13:24:31 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[05-Aug-2025 13:24:31 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[05-Aug-2025 15:14:40 UTC] Début de getMerchantId
[05-Aug-2025 15:14:40 UTC] === MerchantId récupéré ===
[05-Aug-2025 15:14:40 UTC] MerchantId: 1
[05-Aug-2025 15:14:40 UTC] Plan d'abonnement trouvé: 2
[05-Aug-2025 15:14:40 UTC] === Début getLandingPages ===
[05-Aug-2025 15:14:40 UTC] merchantId reçu: 1
[05-Aug-2025 15:14:40 UTC] Type de connexion DB: PDO
[05-Aug-2025 15:14:40 UTC] Préparation de la requête SQL pour getLandingPages
[05-Aug-2025 15:14:40 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[05-Aug-2025 15:14:40 UTC] === Exécution de la requête SQL ===
[05-Aug-2025 15:14:40 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[05-Aug-2025 15:14:40 UTC] MerchantId: 1
[05-Aug-2025 15:14:40 UTC] Paramètres de la requête: [1]
[05-Aug-2025 15:14:40 UTC] Nombre de pages trouvées: 1
[05-Aug-2025 15:14:40 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[05-Aug-2025 15:14:40 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[05-Aug-2025 15:14:56 UTC] Début de getMerchantId
[05-Aug-2025 15:14:56 UTC] === MerchantId récupéré ===
[05-Aug-2025 15:14:56 UTC] MerchantId: 1
[05-Aug-2025 15:14:56 UTC] Plan d'abonnement trouvé: 2
[05-Aug-2025 15:14:56 UTC] === Début getLandingPages ===
[05-Aug-2025 15:14:56 UTC] merchantId reçu: 1
[05-Aug-2025 15:14:56 UTC] Type de connexion DB: PDO
[05-Aug-2025 15:14:56 UTC] Préparation de la requête SQL pour getLandingPages
[05-Aug-2025 15:14:56 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[05-Aug-2025 15:14:56 UTC] === Exécution de la requête SQL ===
[05-Aug-2025 15:14:56 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[05-Aug-2025 15:14:56 UTC] MerchantId: 1
[05-Aug-2025 15:14:56 UTC] Paramètres de la requête: [1]
[05-Aug-2025 15:14:56 UTC] Nombre de pages trouvées: 1
[05-Aug-2025 15:14:56 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[05-Aug-2025 15:14:56 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[05-Aug-2025 16:22:47 UTC] Début de getMerchantId
[05-Aug-2025 16:22:47 UTC] === MerchantId récupéré ===
[05-Aug-2025 16:22:47 UTC] MerchantId: 1
[05-Aug-2025 16:22:47 UTC] Plan d'abonnement trouvé: 2
[05-Aug-2025 18:12:43 UTC] Début de getMerchantId
[05-Aug-2025 18:12:43 UTC] === MerchantId récupéré ===
[05-Aug-2025 18:12:43 UTC] MerchantId: 1
[05-Aug-2025 18:12:43 UTC] Plan d'abonnement trouvé: 2
[05-Aug-2025 18:12:43 UTC] === Début getLandingPages ===
[05-Aug-2025 18:12:43 UTC] merchantId reçu: 1
[05-Aug-2025 18:12:43 UTC] Type de connexion DB: PDO
[05-Aug-2025 18:12:43 UTC] Préparation de la requête SQL pour getLandingPages
[05-Aug-2025 18:12:43 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[05-Aug-2025 18:12:43 UTC] === Exécution de la requête SQL ===
[05-Aug-2025 18:12:43 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[05-Aug-2025 18:12:43 UTC] MerchantId: 1
[05-Aug-2025 18:12:43 UTC] Paramètres de la requête: [1]
[05-Aug-2025 18:12:43 UTC] Nombre de pages trouvées: 1
[05-Aug-2025 18:12:44 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[05-Aug-2025 18:12:44 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[05-Aug-2025 18:50:14 UTC] PHP Fatal error:  Uncaught Error: Class "Database" not found in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:26
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 26
[05-Aug-2025 20:25:18 UTC] PHP Fatal error:  Uncaught Error: Class "Database" not found in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:26
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 26
[05-Aug-2025 20:27:33 UTC] PHP Fatal error:  Uncaught Error: Class "Database" not found in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:26
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 26
[05-Aug-2025 21:21:20 UTC] PHP Fatal error:  Uncaught Error: Class "Database" not found in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:26
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 26
[05-Aug-2025 21:22:53 UTC] PHP Fatal error:  Uncaught Error: Class "Database" not found in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:26
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 26
[05-Aug-2025 21:25:08 UTC] PHP Fatal error:  Uncaught Error: Class "Database" not found in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:26
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 26
[05-Aug-2025 21:48:26 UTC] PHP Fatal error:  Uncaught Error: Class "Database" not found in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:26
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 26
[05-Aug-2025 21:53:24 UTC] PHP Fatal error:  Uncaught Error: Call to undefined function getCurrentUser() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:63
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 63
[05-Aug-2025 21:53:27 UTC] PHP Fatal error:  Uncaught Error: Call to undefined function getCurrentUser() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:63
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 63
[05-Aug-2025 21:55:08 UTC] PHP Fatal error:  Uncaught Error: Call to undefined function getCurrentUser() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:63
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 63
[05-Aug-2025 21:55:23 UTC] PHP Fatal error:  Uncaught Error: Call to undefined function getCurrentUser() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:63
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 63
[05-Aug-2025 21:55:26 UTC] PHP Fatal error:  Uncaught Error: Call to undefined function getCurrentUser() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:63
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 63
[05-Aug-2025 21:56:36 UTC] PHP Fatal error:  Uncaught Error: Call to undefined function getCurrentUser() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:63
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 63
[05-Aug-2025 21:57:06 UTC] PHP Fatal error:  Uncaught Error: Call to undefined function getCurrentUser() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:63
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 63
[05-Aug-2025 21:57:51 UTC] PHP Fatal error:  Uncaught Error: Call to undefined function getCurrentUser() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:63
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 63
[05-Aug-2025 21:58:02 UTC] PHP Fatal error:  Uncaught Error: Call to undefined function getCurrentUser() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:63
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 63
[05-Aug-2025 21:58:11 UTC] PHP Fatal error:  Uncaught Error: Call to undefined function getCurrentUser() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:63
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 63
[05-Aug-2025 21:58:26 UTC] PHP Fatal error:  Uncaught Error: Call to undefined function getCurrentUser() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:63
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 63
[05-Aug-2025 21:58:27 UTC] PHP Fatal error:  Uncaught Error: Call to undefined function getCurrentUser() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:63
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 63
[05-Aug-2025 21:58:58 UTC] PHP Fatal error:  Uncaught Error: Call to undefined function getCurrentUser() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:63
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 63
[05-Aug-2025 22:00:39 UTC] PHP Fatal error:  Uncaught Error: Call to undefined function getCurrentUser() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:63
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 63
[05-Aug-2025 22:09:05 UTC] PHP Fatal error:  Uncaught Error: Call to undefined function getCurrentUser() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:63
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 63
[05-Aug-2025 22:09:15 UTC] PHP Fatal error:  Uncaught Error: Call to undefined function getCurrentUser() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:63
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 63
[05-Aug-2025 22:09:24 UTC] PHP Fatal error:  Uncaught Error: Call to undefined function getCurrentUser() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:63
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 63
[05-Aug-2025 22:10:18 UTC] PHP Fatal error:  Uncaught Error: Call to undefined function getCurrentUser() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:63
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 63
[05-Aug-2025 22:15:43 UTC] PHP Fatal error:  Uncaught Error: Call to undefined function getCurrentUser() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:63
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 63
[05-Aug-2025 22:16:17 UTC] PHP Fatal error:  Uncaught Error: Call to undefined function getCurrentUser() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:63
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 63
[05-Aug-2025 22:16:18 UTC] PHP Fatal error:  Uncaught Error: Call to undefined function getCurrentUser() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:63
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 63
[05-Aug-2025 22:18:01 UTC] PHP Fatal error:  Uncaught Error: Call to undefined function getCurrentUser() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:63
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 63
[05-Aug-2025 22:18:34 UTC] PHP Fatal error:  Uncaught Error: Call to undefined function getCurrentUser() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:63
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 63
[05-Aug-2025 23:32:25 UTC] PHP Fatal error:  Uncaught Error: Call to undefined function getCurrentUser() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:63
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 63
[05-Aug-2025 23:35:00 UTC] PHP Fatal error:  Uncaught Error: Call to undefined function getCurrentUser() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:63
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 63
[05-Aug-2025 23:35:02 UTC] PHP Fatal error:  Uncaught Error: Call to undefined function getCurrentUser() in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php:63
Stack trace:
#0 {main}
  thrown in K:\Projets_Sites_Web\LandingPage-New\api\landing-pages.php on line 63
[06-Aug-2025 08:58:42 UTC] === MerchantId récupéré ===
[06-Aug-2025 08:58:42 UTC] MerchantId: 1
[06-Aug-2025 08:58:42 UTC] Plan d'abonnement trouvé: 2
[06-Aug-2025 08:58:42 UTC] === Début getLandingPages ===
[06-Aug-2025 08:58:42 UTC] merchantId reçu: 1
[06-Aug-2025 08:58:42 UTC] Type de connexion DB: PDO
[06-Aug-2025 08:58:42 UTC] Préparation de la requête SQL pour getLandingPages
[06-Aug-2025 08:58:42 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN landing_page_templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[06-Aug-2025 08:58:42 UTC] === Exécution de la requête SQL ===
[06-Aug-2025 08:58:42 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN landing_page_templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[06-Aug-2025 08:58:42 UTC] MerchantId: 1
[06-Aug-2025 08:58:42 UTC] Paramètres de la requête: [1]
[06-Aug-2025 08:58:42 UTC] Nombre de pages trouvées: 1
[06-Aug-2025 08:58:42 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[06-Aug-2025 08:58:42 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[06-Aug-2025 16:36:05 UTC] === MerchantId récupéré ===
[06-Aug-2025 16:36:05 UTC] MerchantId: 1
[06-Aug-2025 16:36:05 UTC] Plan d'abonnement trouvé: 2
[06-Aug-2025 16:36:05 UTC] === Début getLandingPages ===
[06-Aug-2025 16:36:05 UTC] merchantId reçu: 1
[06-Aug-2025 16:36:05 UTC] Type de connexion DB: PDO
[06-Aug-2025 16:36:05 UTC] Préparation de la requête SQL pour getLandingPages
[06-Aug-2025 16:36:05 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN landing_page_templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[06-Aug-2025 16:36:05 UTC] === Exécution de la requête SQL ===
[06-Aug-2025 16:36:05 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN landing_page_templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[06-Aug-2025 16:36:06 UTC] MerchantId: 1
[06-Aug-2025 16:36:06 UTC] Paramètres de la requête: [1]
[06-Aug-2025 16:36:06 UTC] Nombre de pages trouvées: 1
[06-Aug-2025 16:36:06 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[06-Aug-2025 16:36:06 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":"Modern Shop","conversion_rate":"10.00"}
[06-Aug-2025 17:16:54 UTC] === MerchantId récupéré ===
[06-Aug-2025 17:16:54 UTC] MerchantId: 1
[06-Aug-2025 17:16:54 UTC] Plan d'abonnement trouvé: 2
[06-Aug-2025 17:16:55 UTC] === Début getLandingPages ===
[06-Aug-2025 17:16:55 UTC] merchantId reçu: 1
[06-Aug-2025 17:16:55 UTC] Type de connexion DB: PDO
[06-Aug-2025 17:16:55 UTC] Préparation de la requête SQL pour getLandingPages
[06-Aug-2025 17:16:55 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN landing_page_templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[06-Aug-2025 17:16:55 UTC] === Exécution de la requête SQL ===
[06-Aug-2025 17:16:55 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN landing_page_templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[06-Aug-2025 17:16:55 UTC] MerchantId: 1
[06-Aug-2025 17:16:55 UTC] Paramètres de la requête: [1]
[06-Aug-2025 17:16:55 UTC] Nombre de pages trouvées: 1
[06-Aug-2025 17:16:55 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":null,"conversion_rate":"10.00"}
[06-Aug-2025 17:16:55 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":null,"conversion_rate":"10.00"}
[06-Aug-2025 17:20:23 UTC] === MerchantId récupéré ===
[06-Aug-2025 17:20:23 UTC] MerchantId: 1
[06-Aug-2025 17:20:23 UTC] Plan d'abonnement trouvé: 2
[06-Aug-2025 17:20:23 UTC] === Début getLandingPages ===
[06-Aug-2025 17:20:23 UTC] merchantId reçu: 1
[06-Aug-2025 17:20:23 UTC] Type de connexion DB: PDO
[06-Aug-2025 17:20:23 UTC] Préparation de la requête SQL pour getLandingPages
[06-Aug-2025 17:20:23 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN landing_page_templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[06-Aug-2025 17:20:23 UTC] === Exécution de la requête SQL ===
[06-Aug-2025 17:20:23 UTC] Query: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN landing_page_templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[06-Aug-2025 17:20:23 UTC] MerchantId: 1
[06-Aug-2025 17:20:23 UTC] Paramètres de la requête: [1]
[06-Aug-2025 17:20:23 UTC] Nombre de pages trouvées: 1
[06-Aug-2025 17:20:23 UTC] Première page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":null,"conversion_rate":"10.00"}
[06-Aug-2025 17:20:23 UTC] Dernière page: {"id":1,"title":"Page de test","slug":"test-page","status":"published","views":100,"conversions":10,"created_at":"2025-08-02 12:19:06","template_name":null,"conversion_rate":"10.00"}
[08-Aug-2025 16:30:13 UTC] === MerchantId récupéré ===
[08-Aug-2025 16:30:13 UTC] MerchantId: 1
[08-Aug-2025 16:30:14 UTC] Plan d'abonnement trouvé: 2
[08-Aug-2025 16:30:14 UTC] === Début getLandingPages ===
[08-Aug-2025 16:30:14 UTC] merchantId reçu: 1
[08-Aug-2025 16:30:14 UTC] Type de connexion DB: PDO
[08-Aug-2025 16:30:14 UTC] Préparation de la requête SQL pour getLandingPages
[08-Aug-2025 16:30:14 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN landing_page_templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[08-Aug-2025 16:30:14 UTC] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.title' in 'field list'
[09-Aug-2025 15:47:50 UTC] === MerchantId récupéré ===
[09-Aug-2025 15:47:50 UTC] MerchantId: 2
[09-Aug-2025 15:47:50 UTC] Plan d'abonnement trouvé: 1
[09-Aug-2025 15:47:50 UTC] === Début getLandingPages ===
[09-Aug-2025 15:47:50 UTC] merchantId reçu: 2
[09-Aug-2025 15:47:50 UTC] Type de connexion DB: PDO
[09-Aug-2025 15:47:50 UTC] Préparation de la requête SQL pour getLandingPages
[09-Aug-2025 15:47:50 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN landing_page_templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[09-Aug-2025 15:47:50 UTC] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.title' in 'field list'
[09-Aug-2025 22:39:18 UTC] === MerchantId récupéré ===
[09-Aug-2025 22:39:18 UTC] MerchantId: 2
[09-Aug-2025 22:39:18 UTC] Plan d'abonnement trouvé: 1
[09-Aug-2025 22:39:18 UTC] === Début getLandingPages ===
[09-Aug-2025 22:39:18 UTC] merchantId reçu: 2
[09-Aug-2025 22:39:18 UTC] Type de connexion DB: PDO
[09-Aug-2025 22:39:18 UTC] Préparation de la requête SQL pour getLandingPages
[09-Aug-2025 22:39:18 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN landing_page_templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[09-Aug-2025 22:39:18 UTC] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.title' in 'field list'
[10-Aug-2025 09:15:03 UTC] === MerchantId récupéré ===
[10-Aug-2025 09:15:03 UTC] MerchantId: 1
[10-Aug-2025 09:15:03 UTC] Plan d'abonnement trouvé: 2
[10-Aug-2025 09:15:03 UTC] === Début getLandingPages ===
[10-Aug-2025 09:15:03 UTC] merchantId reçu: 1
[10-Aug-2025 09:15:03 UTC] Type de connexion DB: PDO
[10-Aug-2025 09:15:03 UTC] Préparation de la requête SQL pour getLandingPages
[10-Aug-2025 09:15:03 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN landing_page_templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[10-Aug-2025 09:15:03 UTC] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.title' in 'field list'
[10-Aug-2025 10:14:11 UTC] === MerchantId récupéré ===
[10-Aug-2025 10:14:11 UTC] MerchantId: 2
[10-Aug-2025 10:14:11 UTC] Plan d'abonnement trouvé: 1
[10-Aug-2025 10:14:11 UTC] === Début getLandingPages ===
[10-Aug-2025 10:14:11 UTC] merchantId reçu: 2
[10-Aug-2025 10:14:11 UTC] Type de connexion DB: PDO
[10-Aug-2025 10:14:11 UTC] Préparation de la requête SQL pour getLandingPages
[10-Aug-2025 10:14:11 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN landing_page_templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[10-Aug-2025 10:14:11 UTC] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.title' in 'field list'
[10-Aug-2025 14:03:48 UTC] === MerchantId récupéré ===
[10-Aug-2025 14:03:48 UTC] MerchantId: 2
[10-Aug-2025 14:03:48 UTC] Plan d'abonnement trouvé: 1
[10-Aug-2025 14:03:48 UTC] === Début getLandingPages ===
[10-Aug-2025 14:03:48 UTC] merchantId reçu: 2
[10-Aug-2025 14:03:48 UTC] Type de connexion DB: PDO
[10-Aug-2025 14:03:48 UTC] Préparation de la requête SQL pour getLandingPages
[10-Aug-2025 14:03:48 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN landing_page_templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[10-Aug-2025 14:03:48 UTC] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.title' in 'field list'
[10-Aug-2025 14:04:29 UTC] === MerchantId récupéré ===
[10-Aug-2025 14:04:29 UTC] MerchantId: 2
[10-Aug-2025 14:04:29 UTC] Plan d'abonnement trouvé: 1
[10-Aug-2025 14:04:29 UTC] === Début getLandingPages ===
[10-Aug-2025 14:04:29 UTC] merchantId reçu: 2
[10-Aug-2025 14:04:29 UTC] Type de connexion DB: PDO
[10-Aug-2025 14:04:29 UTC] Préparation de la requête SQL pour getLandingPages
[10-Aug-2025 14:04:29 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN landing_page_templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[10-Aug-2025 14:04:29 UTC] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.title' in 'field list'
[10-Aug-2025 15:48:39 UTC] === MerchantId récupéré ===
[10-Aug-2025 15:48:39 UTC] MerchantId: 2
[10-Aug-2025 15:48:39 UTC] Plan d'abonnement trouvé: 1
[10-Aug-2025 15:48:39 UTC] === Début getLandingPages ===
[10-Aug-2025 15:48:39 UTC] merchantId reçu: 2
[10-Aug-2025 15:48:39 UTC] Type de connexion DB: PDO
[10-Aug-2025 15:48:39 UTC] Préparation de la requête SQL pour getLandingPages
[10-Aug-2025 15:48:39 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN landing_page_templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[10-Aug-2025 15:48:39 UTC] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.title' in 'field list'
[11-Aug-2025 18:53:10 UTC] === MerchantId récupéré ===
[11-Aug-2025 18:53:10 UTC] MerchantId: 3
[11-Aug-2025 18:53:10 UTC] Plan d'abonnement trouvé: 1
[11-Aug-2025 18:53:10 UTC] === Début getLandingPages ===
[11-Aug-2025 18:53:10 UTC] merchantId reçu: 3
[11-Aug-2025 18:53:10 UTC] Type de connexion DB: PDO
[11-Aug-2025 18:53:10 UTC] Préparation de la requête SQL pour getLandingPages
[11-Aug-2025 18:53:10 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN landing_page_templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[11-Aug-2025 18:53:10 UTC] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.title' in 'field list'
[11-Aug-2025 18:53:23 UTC] === MerchantId récupéré ===
[11-Aug-2025 18:53:23 UTC] MerchantId: 3
[11-Aug-2025 18:53:23 UTC] Plan d'abonnement trouvé: 1
[11-Aug-2025 18:53:23 UTC] === Début getLandingPages ===
[11-Aug-2025 18:53:23 UTC] merchantId reçu: 3
[11-Aug-2025 18:53:23 UTC] Type de connexion DB: PDO
[11-Aug-2025 18:53:23 UTC] Préparation de la requête SQL pour getLandingPages
[11-Aug-2025 18:53:23 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN landing_page_templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[11-Aug-2025 18:53:23 UTC] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.title' in 'field list'
[11-Aug-2025 18:53:25 UTC] === MerchantId récupéré ===
[11-Aug-2025 18:53:25 UTC] MerchantId: 3
[11-Aug-2025 18:53:25 UTC] Plan d'abonnement trouvé: 1
[11-Aug-2025 18:53:25 UTC] === Début getLandingPages ===
[11-Aug-2025 18:53:25 UTC] merchantId reçu: 3
[11-Aug-2025 18:53:25 UTC] Type de connexion DB: PDO
[11-Aug-2025 18:53:25 UTC] Préparation de la requête SQL pour getLandingPages
[11-Aug-2025 18:53:25 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN landing_page_templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[11-Aug-2025 18:53:25 UTC] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.title' in 'field list'
[11-Aug-2025 18:53:27 UTC] === MerchantId récupéré ===
[11-Aug-2025 18:53:27 UTC] MerchantId: 3
[11-Aug-2025 18:53:27 UTC] Plan d'abonnement trouvé: 1
[11-Aug-2025 18:53:27 UTC] === Début getLandingPages ===
[11-Aug-2025 18:53:27 UTC] merchantId reçu: 3
[11-Aug-2025 18:53:27 UTC] Type de connexion DB: PDO
[11-Aug-2025 18:53:27 UTC] Préparation de la requête SQL pour getLandingPages
[11-Aug-2025 18:53:27 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN landing_page_templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[11-Aug-2025 18:53:27 UTC] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.title' in 'field list'
[11-Aug-2025 19:28:09 UTC] === MerchantId récupéré ===
[11-Aug-2025 19:28:09 UTC] MerchantId: 3
[11-Aug-2025 19:28:09 UTC] Plan d'abonnement trouvé: 1
[11-Aug-2025 19:28:09 UTC] === Début getLandingPages ===
[11-Aug-2025 19:28:09 UTC] merchantId reçu: 3
[11-Aug-2025 19:28:09 UTC] Type de connexion DB: PDO
[11-Aug-2025 19:28:09 UTC] Préparation de la requête SQL pour getLandingPages
[11-Aug-2025 19:28:09 UTC] Requête SQL construite: SELECT DISTINCT
                lp.id, lp.title, lp.slug, lp.status,
                COALESCE(lp.views, 0) as views,
                COALESCE(lp.conversions, 0) as conversions,
                lp.created_at,
                t.name as template_name,
                CASE
                    WHEN lp.views > 0 THEN ROUND((lp.conversions / lp.views) * 100, 2)
                    ELSE 0
                END as conversion_rate
            FROM landing_pages lp
            LEFT JOIN landing_page_templates t ON lp.template_id = t.id
            INNER JOIN merchants m ON lp.merchant_id = m.id AND m.status = 'active'
            INNER JOIN users u ON m.email = u.email AND u.status = 'active'
            WHERE m.id = ? AND lp.status != 'archived'
            ORDER BY lp.created_at DESC
        
[11-Aug-2025 19:28:09 UTC] SQLSTATE[42S22]: Column not found: 1054 Unknown column 'lp.title' in 'field list'
