<?php
// Test script to verify categories statistics are working
require_once 'dashboard/includes/database.php';

try {
    $db = connectDB();
    $storeId = 7;
    
    echo "<h2>Categories Statistics Test for Store ID: $storeId</h2>";
    
    // Get merchant_id for this store
    $merchantQuery = "SELECT merchant_id FROM stores WHERE id = ?";
    $merchantStmt = $db->prepare($merchantQuery);
    $merchantStmt->execute([$storeId]);
    $storeData = $merchantStmt->fetch(PDO::FETCH_ASSOC);
    $merchantId = $storeData['merchant_id'];
    
    echo "<p><strong>Merchant ID:</strong> $merchantId</p>";
    
    // Test the exact query from getUserCategories
    $categoriesQuery = "
        SELECT
            c.id,
            COALESCE(c.name_fr, c.name) as name,
            c.slug,
            c.description,
            NULL as parent_id,
            c.status,
            c.created_at,
            COUNT(DISTINCT pc.product_id) as products_count
        FROM categories c
        LEFT JOIN product_categories pc ON c.id = pc.category_id
        WHERE c.merchant_id = ? AND c.status = 'active'
        GROUP BY c.id, c.name, c.name_fr, c.slug, c.description, c.status, c.created_at
        ORDER BY c.name ASC
    ";
    
    $stmt = $db->prepare($categoriesQuery);
    $stmt->execute([$merchantId]);
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>All Categories:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Products Count</th><th>Status</th></tr>";
    
    $totalProducts = 0;
    $categoriesWithProducts = 0;
    $maxProducts = 0;
    $mostPopular = '-';
    
    foreach ($categories as $category) {
        $productsCount = intval($category['products_count']);
        $totalProducts += $productsCount;
        
        if ($productsCount > 0) {
            $categoriesWithProducts++;
            if ($productsCount > $maxProducts) {
                $maxProducts = $productsCount;
                $mostPopular = $category['name'];
            }
        }
        
        echo "<tr>";
        echo "<td>{$category['id']}</td>";
        echo "<td>{$category['name']}</td>";
        echo "<td>{$productsCount}</td>";
        echo "<td>{$category['status']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>Statistics Summary:</h3>";
    echo "<ul>";
    echo "<li><strong>Total Categories:</strong> " . count($categories) . "</li>";
    echo "<li><strong>Categories with Products:</strong> $categoriesWithProducts</li>";
    echo "<li><strong>Total Products:</strong> $totalProducts</li>";
    echo "<li><strong>Most Popular Category:</strong> $mostPopular ($maxProducts products)</li>";
    echo "</ul>";
    
    // Test API call
    echo "<h3>API Response Test:</h3>";
    $apiUrl = "http://localhost:8000/api/categories.php?action=user-categories&store_id=$storeId";
    echo "<p><strong>API URL:</strong> <a href='$apiUrl' target='_blank'>$apiUrl</a></p>";
    
    $apiResponse = file_get_contents($apiUrl);
    $apiData = json_decode($apiResponse, true);
    
    if ($apiData && $apiData['success']) {
        echo "<p><strong>API Success:</strong> Yes</p>";
        echo "<p><strong>API Categories Count:</strong> " . count($apiData['categories']) . "</p>";
        echo "<p><strong>API Stats:</strong></p>";
        echo "<ul>";
        if (isset($apiData['stats'])) {
            foreach ($apiData['stats'] as $key => $value) {
                echo "<li><strong>$key:</strong> $value</li>";
            }
        }
        echo "</ul>";
    } else {
        echo "<p><strong>API Error:</strong> " . ($apiData['error'] ?? 'Unknown error') . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
}
?>
