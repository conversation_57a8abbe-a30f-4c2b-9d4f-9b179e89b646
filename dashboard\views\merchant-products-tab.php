<?php

/**
 * Vue simplifiée des produits pour le chargement AJAX dans les onglets
 * Sans dépendances d'authentification complexes
 */

// Load products data server-side for reliable display
require_once __DIR__ . '/../includes/database.php';

$productsData = [];
$productsStats = [
    'total_products' => 0,
    'active_products' => 0,
    'draft_products' => 0,
    'total_value' => 0
];

try {
    $db = connectDB();
    $storeId = $_GET['store_id'] ?? 7;

    // Get merchant_id for this store
    $merchantQuery = "SELECT merchant_id FROM stores WHERE id = ?";
    $merchantStmt = $db->prepare($merchantQuery);
    $merchantStmt->execute([$storeId]);
    $storeData = $merchantStmt->fetch(PDO::FETCH_ASSOC);
    $merchantId = $storeData['merchant_id'] ?? 3;

    // Get products with category information
    $productsQuery = "
        SELECT
            p.*,
            GROUP_CONCAT(c.name SEPARATOR ', ') as categories
        FROM products p
        LEFT JOIN product_categories pc ON p.id = pc.product_id
        LEFT JOIN categories c ON pc.category_id = c.id
        WHERE p.store_id = ?
        GROUP BY p.id
        ORDER BY p.created_at DESC
    ";

    $stmt = $db->prepare($productsQuery);
    $stmt->execute([$storeId]);
    $productsData = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Calculate statistics
    foreach ($productsData as $product) {
        $productsStats['total_products']++;
        if ($product['status'] === 'active') {
            $productsStats['active_products']++;
        } elseif ($product['status'] === 'draft') {
            $productsStats['draft_products']++;
        }
        $productsStats['total_value'] += floatval($product['price']);
    }
} catch (Exception $e) {
    error_log("Products tab error: " . $e->getMessage());
}

// Récupérer l'email du marchand depuis les paramètres
$merchantEmail = $_GET['email'] ?? '';
?>

<div class="products-section">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3>
            <i class="fas fa-box me-2"></i>
            Mes Produits
        </h3>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createProductModal">
            <i class="fas fa-plus me-2"></i>Ajouter un produit
        </button>
    </div>

    <!-- Filtres et recherche -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="input-group">
                <span class="input-group-text"><i class="fas fa-search"></i></span>
                <input type="text" class="form-control" placeholder="Rechercher un produit..." id="searchProducts">
            </div>
        </div>
        <div class="col-md-2">
            <select class="form-select" id="filterCategory">
                <option value="">Toutes catégories</option>
            </select>
        </div>
        <div class="col-md-2">
            <select class="form-select" id="filterStatus">
                <option value="">Tous statuts</option>
                <option value="active">Actif</option>
                <option value="inactive">Inactif</option>
                <option value="draft">Brouillon</option>
            </select>
        </div>
        <div class="col-md-3">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-secondary" id="viewGrid" title="Vue grille">
                    <i class="fas fa-th"></i>
                </button>
                <button type="button" class="btn btn-outline-secondary active" id="viewList" title="Vue liste">
                    <i class="fas fa-list"></i>
                </button>
            </div>
        </div>
        <div class="col-md-2">
            <button class="btn btn-outline-primary" onclick="exportProducts()">
                <i class="fas fa-download me-2"></i>Exporter
            </button>
        </div>
    </div>

    <!-- Statistiques rapides -->
    <div class="row mb-4" id="productsStats">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h6 class="card-title mb-0">Total Produits</h6>
                            <h4 class="mb-0" id="totalProducts"><?php echo $productsStats['total_products']; ?></h4>
                        </div>
                        <i class="fas fa-box fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h6 class="card-title mb-0">Produits Actifs</h6>
                            <h4 class="mb-0" id="activeProducts"><?php echo $productsStats['active_products']; ?></h4>
                        </div>
                        <i class="fas fa-check-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h6 class="card-title mb-0">Catégories</h6>
                            <h4 class="mb-0" id="categoriesCount">0</h4>
                        </div>
                        <i class="fas fa-tags fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h6 class="card-title mb-0">Valeur Stock</h6>
                            <h4 class="mb-0" id="stockValue">0 DA</h4>
                        </div>
                        <i class="fas fa-money-bill-wave fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Zone d'affichage des produits -->
    <div class="card">
        <div class="card-body">
            <div id="productsContainer">
                <?php if (!empty($productsData)): ?>
                    <!-- Products Table -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Image</th>
                                    <th>Nom du produit</th>
                                    <th>Prix</th>
                                    <th>Catégorie</th>
                                    <th>Statut</th>
                                    <th>Date création</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($productsData as $product): ?>
                                    <tr>
                                        <td>
                                            <?php
                                            // Handle image display
                                            $imageUrl = '';
                                            if (!empty($product['image_url'])) {
                                                $imageUrl = $product['image_url'];
                                            } elseif (!empty($product['images'])) {
                                                $images = json_decode($product['images'], true);
                                                if (is_array($images) && !empty($images)) {
                                                    $imageUrl = $images[0];
                                                }
                                            }
                                            ?>
                                            <?php if (!empty($imageUrl)): ?>
                                                <img src="<?php echo htmlspecialchars($imageUrl); ?>"
                                                    alt="<?php echo htmlspecialchars($product['name']); ?>"
                                                    class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;">
                                            <?php else: ?>
                                                <div class="bg-light d-flex align-items-center justify-content-center"
                                                    style="width: 50px; height: 50px; border-radius: 4px;">
                                                    <i class="fas fa-image text-muted"></i>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($product['name']); ?></strong>
                                            <?php if (!empty($product['sku'])): ?>
                                                <br><small class="text-muted">SKU: <?php echo htmlspecialchars($product['sku']); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="fw-bold text-primary"><?php echo number_format($product['price'], 2); ?> DZD</span>
                                        </td>
                                        <td>
                                            <?php if (!empty($product['categories'])): ?>
                                                <span class="badge bg-light text-dark"><?php echo htmlspecialchars($product['categories']); ?></span>
                                            <?php else: ?>
                                                <span class="text-muted">Aucune</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php
                                            $statusClass = $product['status'] === 'active' ? 'success' : ($product['status'] === 'draft' ? 'warning' : 'secondary');
                                            $statusText = $product['status'] === 'active' ? 'Actif' : ($product['status'] === 'draft' ? 'Brouillon' : 'Inactif');
                                            ?>
                                            <span class="badge bg-<?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                                        </td>
                                        <td>
                                            <small class="text-muted"><?php echo date('d/m/Y', strtotime($product['created_at'])); ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="editProduct(<?php echo $product['id']; ?>)" title="Modifier">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <a href="/store-frontend.php?store=medical-software-solutions&page=products"
                                                    class="btn btn-outline-success" target="_blank" title="Voir dans la boutique">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <button class="btn btn-outline-danger" onclick="deleteProduct(<?php echo $product['id']; ?>)" title="Supprimer">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination info -->
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <small class="text-muted">
                            Affichage de <?php echo count($productsData); ?> produits
                        </small>
                        <a href="?page=products&store_id=<?php echo $storeId; ?>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-external-link-alt me-1"></i>Voir la page complète
                        </a>
                    </div>

                <?php else: ?>
                    <!-- Empty state -->
                    <div class="text-center py-5">
                        <i class="fas fa-box fa-3x text-muted mb-3"></i>
                        <h5>Aucun produit pour le moment</h5>
                        <p class="text-muted">Commencez par ajouter vos premiers produits à votre catalogue</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createProductModal">
                            <i class="fas fa-plus me-2"></i>Ajouter mon premier produit
                        </button>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour créer/éditer un produit -->
<div class="modal fade" id="createProductModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>Ajouter un produit
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="productForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="productName" class="form-label">Nom du produit *</label>
                                <input type="text" class="form-control" id="productName" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="productSku" class="form-label">SKU</label>
                                <input type="text" class="form-control" id="productSku" name="sku">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="productDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="productDescription" name="description" rows="3"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="productPrice" class="form-label">Prix (DA) *</label>
                                <input type="number" class="form-control" id="productPrice" name="price" step="0.01" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="productCategory" class="form-label">Catégorie</label>
                                <select class="form-select" id="productCategory" name="category_id">
                                    <option value="">Sélectionner une catégorie</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="productStock" class="form-label">Stock</label>
                                <input type="number" class="form-control" id="productStock" name="stock" min="0">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" onclick="saveProduct()">
                    <i class="fas fa-save me-2"></i>Sauvegarder
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    console.log('📦 Products tab loaded for merchant:', '<?php echo htmlspecialchars($merchantEmail); ?>');

    // Initialize products when tab content is loaded
    document.addEventListener('DOMContentLoaded', function() {
        loadUserProducts();
        initializeProductsFilters();
    });

    // Load products for the current merchant
    async function loadUserProducts() {
        try {
            const merchantEmail = '<?php echo htmlspecialchars($merchantEmail); ?>';
            const apiUrl = `/api/merchant-products.php${merchantEmail ? '?email=' + encodeURIComponent(merchantEmail) : ''}`;

            console.log('🔍 Loading products for merchant:', merchantEmail);
            console.log('🌐 API URL:', apiUrl);

            const response = await fetch(apiUrl);
            const data = await response.json();

            console.log('📦 API Response:', data);

            if (data.success) {
                console.log('✅ Products loaded successfully:', data.data?.length || 0, 'products');
                displayProducts(data.data || []);
                updateProductsStats(data.stats || {});

                // Update categories filter
                if (data.stats && data.stats.categories) {
                    updateCategoriesFilter(data.stats.categories);
                }
            } else {
                console.error('❌ API Error:', data.error || data.message);
                showError(data.error || data.message || 'Erreur lors du chargement des produits');
                showEmptyState();
            }
        } catch (error) {
            console.error('💥 Erreur loadUserProducts:', error);
            showError('Erreur de connexion: ' + error.message);
            showEmptyState();
        }
    }

    function displayProducts(products) {
        const loadingState = document.getElementById('loadingState');
        const emptyState = document.getElementById('emptyState');
        const productsList = document.getElementById('productsList');

        // Hide loading
        loadingState.style.display = 'none';

        if (!products || products.length === 0) {
            showEmptyState();
            return;
        }

        // Hide empty state and show products
        emptyState.style.display = 'none';
        productsList.style.display = 'block';

        // Generate products HTML
        let html = '<div class="table-responsive"><table class="table table-hover">';
        html += '<thead><tr>';
        html += '<th>Produit</th>';
        html += '<th>Catégorie</th>';
        html += '<th>Prix</th>';
        html += '<th>Stock</th>';
        html += '<th>Statut</th>';
        html += '<th>Actions</th>';
        html += '</tr></thead><tbody>';

        products.forEach(product => {
            const statusBadge = getStatusBadge(product.status || 'active');
            html += `
                <tr>
                    <td>
                        <div>
                            <strong>${escapeHtml(product.name)}</strong>
                            ${product.sku ? `<br><small class="text-muted">SKU: ${escapeHtml(product.sku)}</small>` : ''}
                        </div>
                    </td>
                    <td>${escapeHtml(product.category_name || 'Non catégorisé')}</td>
                    <td><strong>${formatPrice(product.price)} DA</strong></td>
                    <td>${product.stock_quantity || 0}</td>
                    <td>${statusBadge}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="editProduct(${product.id})" title="Modifier">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-info" onclick="viewProduct(${product.id})" title="Voir">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteProduct(${product.id})" title="Supprimer">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
        productsList.innerHTML = html;
    }

    function showEmptyState() {
        document.getElementById('loadingState').style.display = 'none';
        document.getElementById('emptyState').style.display = 'block';
        document.getElementById('productsList').style.display = 'none';
    }

    function updateProductsStats(stats) {
        if (!stats) return;

        document.getElementById('totalProducts').textContent = stats.total_products || 0;
        document.getElementById('activeProducts').textContent = stats.active_products || 0;
        document.getElementById('categoriesCount').textContent = stats.categories_count || 0;
        document.getElementById('stockValue').textContent = formatPrice(stats.total_value || 0) + ' DA';

        // Show stats if we have products
        if (stats.total_products > 0) {
            document.getElementById('productsStats').style.display = 'block';
        }
    }

    function updateCategoriesFilter(categories) {
        const filterCategory = document.getElementById('filterCategory');
        if (!filterCategory) return;

        // Clear existing options except "Toutes catégories"
        filterCategory.innerHTML = '<option value="">Toutes catégories</option>';

        // Add categories
        categories.forEach(category => {
            if (category && category.trim()) {
                const option = document.createElement('option');
                option.value = category;
                option.textContent = category;
                filterCategory.appendChild(option);
            }
        });

        console.log('✅ Categories filter updated with', categories.length, 'categories');
    }

    function initializeProductsFilters() {
        // Search functionality
        const searchInput = document.getElementById('searchProducts');
        if (searchInput) {
            searchInput.addEventListener('input', debounce(filterProducts, 300));
        }

        // Filter functionality
        const filterCategory = document.getElementById('filterCategory');
        const filterStatus = document.getElementById('filterStatus');

        if (filterCategory) filterCategory.addEventListener('change', filterProducts);
        if (filterStatus) filterStatus.addEventListener('change', filterProducts);
    }

    function filterProducts() {
        // Implementation for filtering products
        console.log('🔍 Filtering products...');
    }

    function getStatusBadge(status) {
        const badges = {
            'active': '<span class="badge bg-success">Actif</span>',
            'inactive': '<span class="badge bg-secondary">Inactif</span>',
            'draft': '<span class="badge bg-warning">Brouillon</span>',
            'blocked': '<span class="badge bg-danger">Bloqué</span>'
        };
        return badges[status] || badges['active'];
    }

    function formatPrice(price) {
        return new Intl.NumberFormat('fr-DZ').format(price || 0);
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Product actions
    function editProduct(productId) {
        console.log('✏️ Edit product:', productId);
        alert('Fonctionnalité d\'édition en développement');
    }

    function viewProduct(productId) {
        console.log('👁️ View product:', productId);
        alert('Fonctionnalité de visualisation en développement');
    }

    function deleteProduct(productId) {
        if (confirm('Êtes-vous sûr de vouloir supprimer ce produit ?')) {
            console.log('🗑️ Delete product:', productId);
            alert('Fonctionnalité de suppression en développement');
        }
    }

    async function saveProduct() {
        console.log('💾 Save product');

        try {
            // Get form data
            const form = document.getElementById('productForm');
            const formData = new FormData(form);

            // Get merchant email from the page
            const merchantEmail = '<?php echo htmlspecialchars($merchantEmail); ?>';
            formData.append('merchant_email', merchantEmail);

            // Validate required fields
            const productName = formData.get('name');
            const productPrice = formData.get('price');

            if (!productName || !productPrice) {
                alert('Veuillez remplir tous les champs obligatoires');
                return;
            }

            // Show loading state
            const saveButton = document.querySelector('#createProductModal .btn-primary');
            const originalText = saveButton.innerHTML;
            saveButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sauvegarde...';
            saveButton.disabled = true;

            // Send to products API
            const response = await fetch('/api/products.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                console.log('✅ Product created successfully:', result);

                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('createProductModal'));
                modal.hide();

                // Reset form
                form.reset();

                // Reload products list
                await loadProducts();

                // Show success message
                showNotification('Produit créé avec succès!', 'success');

            } else {
                console.error('❌ Product creation failed:', result.error);
                alert('Erreur lors de la création du produit: ' + (result.error || 'Erreur inconnue'));
            }

        } catch (error) {
            console.error('💥 Error saving product:', error);
            alert('Erreur lors de la sauvegarde: ' + error.message);
        } finally {
            // Restore button state
            const saveButton = document.querySelector('#createProductModal .btn-primary');
            saveButton.innerHTML = '<i class="fas fa-save me-2"></i>Sauvegarder';
            saveButton.disabled = false;
        }
    }

    function exportProducts() {
        console.log('📤 Export products');
        alert('Fonctionnalité d\'export en développement');
    }

    function showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Add to page
        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    function showError(message) {
        console.error('❌ Error:', message);
        // You could show a toast notification here
    }
</script>
