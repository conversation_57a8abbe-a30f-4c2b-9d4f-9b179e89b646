<?php
// Landing Page Builder
global $db;
if (!isset($db)) {
    $db = connectDB();
}

// Get user role for permissions
$user = getCurrentUser();
$userRole = $user['role'] ?? 'customer';

// Get templates
$templatesQuery = "
    SELECT
        id, name, description, preview_image, category,
        created_at
    FROM landing_page_templates
    WHERE status = 'active'
    ORDER BY category, name
";

try {
    $stmt = $db->prepare($templatesQuery);
    $stmt->execute();
    $templates = $stmt->fetchAll();
} catch (Exception $e) {
    $templates = [];
    error_log("Error fetching templates: " . $e->getMessage());
}

// Get user's landing pages
$pagesQuery = "
    SELECT
        id, title, slug, status, template_id,
        created_at, updated_at, views, conversions
    FROM landing_pages
    WHERE merchant_id = ? OR ? = 'admin'
    ORDER BY updated_at DESC
    LIMIT 20
";

try {
    $stmt = $db->prepare($pagesQuery);
    $stmt->execute([$user['id'] ?? 0, $userRole]);
    $userPages = $stmt->fetchAll();
} catch (Exception $e) {
    $userPages = [];
    error_log("Error fetching user pages: " . $e->getMessage());
}
?>

<div class="landing-page-builder">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3><i class="fas fa-magic me-2"></i>Constructeur de Landing Pages</h3>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-info" onclick="showProductSelector()">
                <i class="fas fa-box me-2"></i>Sélectionner Produit
            </button>
            <button class="btn btn-outline-primary" onclick="showTemplates()">
                <i class="fas fa-layer-group me-2"></i>Templates
            </button>
            <button class="btn btn-success" onclick="createFromScratch()">
                <i class="fas fa-plus me-2"></i>Créer de zéro
            </button>
            <button class="btn btn-primary" onclick="createFromTemplate()">
                <i class="fas fa-clone me-2"></i>Utiliser un template
            </button>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-primary">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="stat-content">
                    <h4><?php echo count($userPages); ?></h4>
                    <p>Mes Pages</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-success">
                    <i class="fas fa-eye"></i>
                </div>
                <div class="stat-content">
                    <h4><?php echo array_sum(array_column($userPages, 'views')); ?></h4>
                    <p>Vues totales</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-warning">
                    <i class="fas fa-mouse-pointer"></i>
                </div>
                <div class="stat-content">
                    <h4><?php echo array_sum(array_column($userPages, 'conversions')); ?></h4>
                    <p>Conversions</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-info">
                    <i class="fas fa-layer-group"></i>
                </div>
                <div class="stat-content">
                    <h4><?php echo count($templates); ?></h4>
                    <p>Templates disponibles</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs Navigation -->
    <ul class="nav nav-tabs mb-4" id="builderTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="my-pages-tab" data-bs-toggle="tab" data-bs-target="#my-pages" type="button" role="tab">
                <i class="fas fa-file-alt me-2"></i>Mes Pages
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="templates-tab" data-bs-toggle="tab" data-bs-target="#templates" type="button" role="tab">
                <i class="fas fa-layer-group me-2"></i>Templates
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="ai-assistant-tab" data-bs-toggle="tab" data-bs-target="#ai-assistant" type="button" role="tab">
                <i class="fas fa-robot me-2"></i>Assistant IA
            </button>
        </li>
    </ul>

    <!-- Tab Content -->
    <div class="tab-content" id="builderTabsContent">
        <!-- My Pages Tab -->
        <div class="tab-pane fade show active" id="my-pages" role="tabpanel">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-file-alt me-2"></i>Mes Landing Pages</h5>
                    <div class="d-flex gap-2">
                        <input type="text" class="form-control form-control-sm" placeholder="Rechercher..." id="searchPages" style="width: 200px;">
                        <select class="form-select form-select-sm" id="filterStatus" style="width: 150px;">
                            <option value="">Tous les statuts</option>
                            <option value="draft">Brouillon</option>
                            <option value="published">Publié</option>
                            <option value="archived">Archivé</option>
                        </select>
                    </div>
                </div>
                <div class="card-body p-0">
                    <?php if (empty($userPages)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Aucune landing page trouvée</h5>
                            <p class="text-muted">Créez votre première landing page pour commencer</p>
                            <button class="btn btn-primary" onclick="createFromScratch()">
                                <i class="fas fa-plus me-2"></i>Créer ma première page
                            </button>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0" id="pagesTable">
                                <thead class="table-light">
                                    <tr>
                                        <th>Page</th>
                                        <th>Statut</th>
                                        <th>Statistiques</th>
                                        <th>Dernière modification</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($userPages as $page): ?>
                                        <tr data-page-id="<?php echo $page['id']; ?>" data-status="<?php echo $page['status'] ?? 'draft'; ?>">
                                            <td>
                                                <div>
                                                    <div class="fw-bold"><?php echo htmlspecialchars($page['title'] ?? 'Page sans titre'); ?></div>
                                                    <small class="text-muted">
                                                        <i class="fas fa-link me-1"></i>
                                                        <?php echo htmlspecialchars($page['slug'] ?? ''); ?>
                                                    </small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo getStatusColor($page['status'] ?? 'draft'); ?>">
                                                    <?php echo getStatusText($page['status'] ?? 'draft'); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="text-center">
                                                    <div class="fw-bold"><?php echo number_format($page['views'] ?? 0); ?></div>
                                                    <small class="text-muted">vues</small>
                                                    <div class="fw-bold text-success"><?php echo number_format($page['conversions'] ?? 0); ?></div>
                                                    <small class="text-muted">conversions</small>
                                                </div>
                                            </td>
                                            <td>
                                                <small><?php echo date('d/m/Y H:i', strtotime($page['updated_at'] ?? 'now')); ?></small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" onclick="editPage(<?php echo $page['id']; ?>)" title="Modifier">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-outline-success" onclick="previewPage(<?php echo $page['id']; ?>)" title="Aperçu">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-info" onclick="duplicatePage(<?php echo $page['id']; ?>)" title="Dupliquer">
                                                        <i class="fas fa-copy"></i>
                                                    </button>
                                                    <div class="btn-group btn-group-sm">
                                                        <button class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                                            <i class="fas fa-ellipsis-v"></i>
                                                        </button>
                                                        <ul class="dropdown-menu">
                                                            <li><a class="dropdown-item" href="#" onclick="publishPage(<?php echo $page['id']; ?>)">
                                                                    <i class="fas fa-globe me-2"></i>Publier
                                                                </a></li>
                                                            <li><a class="dropdown-item" href="#" onclick="archivePage(<?php echo $page['id']; ?>)">
                                                                    <i class="fas fa-archive me-2"></i>Archiver
                                                                </a></li>
                                                            <li>
                                                                <hr class="dropdown-divider">
                                                            </li>
                                                            <li><a class="dropdown-item text-danger" href="#" onclick="deletePage(<?php echo $page['id']; ?>)">
                                                                    <i class="fas fa-trash me-2"></i>Supprimer
                                                                </a></li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Templates Tab -->
        <div class="tab-pane fade" id="templates" role="tabpanel">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-layer-group me-2"></i>Templates Disponibles</h5>
                    <div class="d-flex gap-2">
                        <select class="form-select form-select-sm" id="filterCategory" style="width: 200px;">
                            <option value="">Toutes les catégories</option>
                            <option value="ecommerce">E-commerce</option>
                            <option value="services">Services</option>
                            <option value="lead-generation">Génération de leads</option>
                            <option value="portfolio">Portfolio</option>
                            <option value="event">Événement</option>
                        </select>
                        <?php if ($userRole === 'admin'): ?>
                            <button class="btn btn-primary btn-sm" onclick="createTemplate()">
                                <i class="fas fa-plus me-2"></i>Nouveau Template
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (empty($templates)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-layer-group fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Aucun template disponible</h5>
                            <p class="text-muted">Les templates seront bientôt disponibles</p>
                        </div>
                    <?php else: ?>
                        <div class="row" id="templatesGrid">
                            <?php foreach ($templates as $template): ?>
                                <div class="col-md-4 mb-4" data-category="<?php echo $template['category'] ?? ''; ?>">
                                    <div class="template-card">
                                        <div class="template-preview">
                                            <?php if (!empty($template['preview_image'])): ?>
                                                <img src="<?php echo htmlspecialchars($template['preview_image']); ?>" alt="Preview">
                                            <?php else: ?>
                                                <div class="template-placeholder">
                                                    <i class="fas fa-image"></i>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="template-info">
                                            <h6><?php echo htmlspecialchars($template['name'] ?? 'Template sans nom'); ?></h6>
                                            <p class="text-muted small"><?php echo htmlspecialchars($template['description'] ?? ''); ?></p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <span class="badge bg-secondary"><?php echo ucfirst($template['category'] ?? 'Général'); ?></span>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-info" onclick="previewTemplate(<?php echo $template['id']; ?>)" title="Aperçu">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-primary" onclick="useTemplate(<?php echo $template['id']; ?>)">
                                                        Utiliser
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- AI Assistant Tab -->
        <div class="tab-pane fade" id="ai-assistant" role="tabpanel">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-robot me-2"></i>Assistant IA pour Landing Pages</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="ai-feature-card">
                                <div class="ai-icon">
                                    <i class="fas fa-heading"></i>
                                </div>
                                <h6>Génération de Titres</h6>
                                <p>Créez des titres accrocheurs avec l'IA</p>
                                <button class="btn btn-outline-primary btn-sm" onclick="generateTitle()">
                                    Générer un titre
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="ai-feature-card">
                                <div class="ai-icon">
                                    <i class="fas fa-align-left"></i>
                                </div>
                                <h6>Génération de Contenu</h6>
                                <p>Créez du contenu marketing optimisé</p>
                                <button class="btn btn-outline-primary btn-sm" onclick="generateContent()">
                                    Générer du contenu
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <div class="ai-prompt-section">
                            <h6>Prompt Personnalisé</h6>
                            <div class="input-group">
                                <textarea class="form-control" id="aiPrompt" rows="3" placeholder="Décrivez ce que vous voulez créer..."></textarea>
                                <button class="btn btn-primary" onclick="generateFromPrompt()">
                                    <i class="fas fa-magic me-2"></i>Générer
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Template Preview Modal -->
<div class="modal fade" id="templatePreviewModal" tabindex="-1" aria-labelledby="templatePreviewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-fullscreen-lg-down" style="max-width: 95vw; width: 95vw;">
        <div class="modal-content" style="height: 95vh;">
            <div class="modal-header d-flex justify-content-between align-items-center">
                <h5 class="modal-title" id="templatePreviewModalLabel">
                    <i class="fas fa-eye me-2"></i>Aperçu du Template
                </h5>
                <div class="d-flex align-items-center gap-2">
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="toggleFullscreen()" title="Plein écran">
                        <i class="fas fa-expand" id="fullscreenIcon"></i>
                    </button>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
            </div>
            <div class="modal-body p-0" style="flex: 1; overflow: hidden;">
                <div id="templatePreviewContainer" style="height: 100%; overflow: auto; resize: both;">
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                        <p class="mt-2">Chargement de l'aperçu...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                <button type="button" class="btn btn-primary" id="useTemplateFromPreview" onclick="useTemplateFromPreview()">
                    <i class="fas fa-check me-2"></i>Utiliser ce Template
                </button>
            </div>
        </div>
    </div>
</div>

<?php
// Helper functions
function getStatusColor($status)
{
    switch ($status) {
        case 'published':
            return 'success';
        case 'draft':
            return 'warning';
        case 'archived':
            return 'secondary';
        default:
            return 'secondary';
    }
}

function getStatusText($status)
{
    switch ($status) {
        case 'published':
            return 'Publié';
        case 'draft':
            return 'Brouillon';
        case 'archived':
            return 'Archivé';
        default:
            return 'Inconnu';
    }
}
?>

<script>
    // Landing Page Builder JavaScript Functions

    function createFromScratch() {
        // Redirect to the page builder with blank template
        window.location.href = '?page=page-builder&mode=create';
    }

    function createFromTemplate() {
        // Show template selection modal or redirect to templates tab
        document.getElementById('templates-tab').click();
    }

    function showTemplates() {
        // Switch to templates tab
        document.getElementById('templates-tab').click();
    }

    function editPage(pageId) {
        // Redirect to page builder with existing page
        window.location.href = `?page=page-builder&mode=edit&id=${pageId}`;
    }

    function previewPage(pageId) {
        // Open page preview in new tab
        window.open(`/preview/${pageId}`, '_blank');
    }

    function duplicatePage(pageId) {
        if (confirm('Voulez-vous dupliquer cette page ?')) {
            fetch(`/api/landing-pages.php?action=duplicate&id=${pageId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        alert('Page dupliquée avec succès');
                        location.reload();
                    } else {
                        alert('Erreur: ' + (data.error || 'Erreur inconnue'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Erreur de connexion: ' + error.message);
                });
        }
    }

    function publishPage(pageId) {
        if (confirm('Voulez-vous publier cette page ?')) {
            fetch(`/api/landing-pages.php?action=publish&id=${pageId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        alert('Page publiée avec succès');
                        location.reload();
                    } else {
                        alert('Erreur: ' + (data.error || 'Erreur inconnue'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Erreur de connexion: ' + error.message);
                });
        }
    }

    function archivePage(pageId) {
        if (confirm('Voulez-vous archiver cette page ?')) {
            fetch(`/api/landing-pages.php?action=archive&id=${pageId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        alert('Page archivée avec succès');
                        location.reload();
                    } else {
                        alert('Erreur: ' + (data.error || 'Erreur inconnue'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Erreur de connexion: ' + error.message);
                });
        }
    }

    function deletePage(pageId) {
        if (confirm('Êtes-vous sûr de vouloir supprimer cette page ? Cette action est irréversible.')) {
            fetch(`/api/landing-pages.php?action=delete&id=${pageId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        alert('Page supprimée avec succès');
                        location.reload();
                    } else {
                        alert('Erreur: ' + (data.error || 'Erreur inconnue'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Erreur de connexion: ' + error.message);
                });
        }
    }

    function useTemplate(templateId) {
        // Redirect to page builder with selected template
        window.location.href = `?page=page-builder&mode=template&template=${templateId}`;
    }

    function createTemplate() {
        // Redirect to template creation
        window.location.href = '?page=template-builder&mode=create';
    }

    // AI Functions
    function generateTitle() {
        const prompt = prompt('Décrivez votre produit/service pour générer un titre:');
        if (prompt) {
            generateAIContent('title', prompt);
        }
    }

    function generateContent() {
        const prompt = prompt('Décrivez le type de contenu que vous voulez générer:');
        if (prompt) {
            generateAIContent('content', prompt);
        }
    }

    function generateFromPrompt() {
        const prompt = document.getElementById('aiPrompt').value;
        if (prompt.trim()) {
            generateAIContent('custom', prompt);
        } else {
            alert('Veuillez entrer un prompt');
        }
    }

    function generateAIContent(type, prompt) {
        // Show loading state
        alert('Génération IA en cours... (Fonctionnalité en développement)');

        // TODO: Implement actual AI integration
        setTimeout(() => {
            const mockResults = {
                title: 'Titre Généré par IA: ' + prompt,
                content: 'Contenu généré par IA basé sur: ' + prompt,
                custom: 'Résultat personnalisé: ' + prompt
            };

            showAIResult(mockResults[type] || mockResults.custom);
        }, 1000);
    }

    function showAIResult(result) {
        // Show AI result in alert for now
        alert('Résultat IA:\n\n' + result);
    }

    // Enhanced Landing Page Builder Functions
    function showProductSelector() {
        // Create and show product selector modal
        const modalHtml = `
            <div class="modal fade" id="productSelectorModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-box me-2"></i>Sélectionner le produit principal
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <input type="text" class="form-control" id="productSearch"
                                               placeholder="Rechercher un produit...">
                                    </div>

                                    <div id="productsList" class="row">
                                        <!-- Products will be loaded here -->
                                        <div class="text-center py-4">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Chargement...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">Configuration d'affichage</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label class="form-label">Produits à afficher</label>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="displayMode"
                                                           id="displayPrimary" value="primary" checked>
                                                    <label class="form-check-label" for="displayPrimary">
                                                        Produit principal uniquement
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="displayMode"
                                                           id="displayCategory" value="category">
                                                    <label class="form-check-label" for="displayCategory">
                                                        Tous les produits de la même catégorie
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="displayMode"
                                                           id="displayAll" value="all">
                                                    <label class="form-check-label" for="displayAll">
                                                        Tous mes produits
                                                    </label>
                                                </div>
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label">Position du bouton "Acheter"</label>
                                                <select class="form-select" id="buyButtonPosition">
                                                    <option value="top">En haut de page</option>
                                                    <option value="middle" selected>Au milieu</option>
                                                    <option value="bottom">En bas de page</option>
                                                    <option value="floating">Bouton flottant</option>
                                                    <option value="custom">Position personnalisée</option>
                                                </select>
                                            </div>

                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input" type="checkbox" id="enableCart" checked>
                                                <label class="form-check-label" for="enableCart">
                                                    Activer le panier
                                                </label>
                                            </div>

                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input" type="checkbox" id="showPricing" checked>
                                                <label class="form-check-label" for="showPricing">
                                                    Afficher les prix
                                                </label>
                                            </div>

                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input" type="checkbox" id="showQuantity" checked>
                                                <label class="form-check-label" for="showQuantity">
                                                    Sélecteur de quantité
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                            <button type="button" class="btn btn-primary" onclick="applyProductSelection()">
                                <i class="fas fa-check me-1"></i>Appliquer la sélection
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('productSelectorModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to DOM and show
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        const modal = new bootstrap.Modal(document.getElementById('productSelectorModal'));
        modal.show();

        // Load products
        loadMerchantProducts();
    }

    async function loadMerchantProducts() {
        try {
            const storeId = getStoreId();
            const response = await fetch(`/api/products.php?action=list&store_id=${storeId}`);
            const data = await response.json();

            if (data.success) {
                displayProductsList(data.products);
            } else {
                document.getElementById('productsList').innerHTML =
                    '<div class="text-center py-4"><p class="text-muted">Aucun produit trouvé</p></div>';
            }
        } catch (error) {
            console.error('Error loading products:', error);
            document.getElementById('productsList').innerHTML =
                '<div class="text-center py-4"><p class="text-danger">Erreur lors du chargement</p></div>';
        }
    }

    function displayProductsList(products) {
        const container = document.getElementById('productsList');

        if (products.length === 0) {
            container.innerHTML = '<div class="text-center py-4"><p class="text-muted">Aucun produit trouvé</p></div>';
            return;
        }

        const productsHtml = products.map(product => `
            <div class="col-md-6 mb-3">
                <div class="card product-selector-card" onclick="selectProduct(${product.id})" data-product-id="${product.id}">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                ${product.image_url ?
                                    `<img src="${product.image_url}" alt="${product.name}" class="rounded" style="width: 60px; height: 60px; object-fit: cover;">` :
                                    '<div class="bg-light rounded d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;"><i class="fas fa-image text-muted"></i></div>'
                                }
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="card-title mb-1">${product.name}</h6>
                                <p class="card-text text-muted small mb-1">${product.description ? product.description.substring(0, 80) + '...' : 'Aucune description'}</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="fw-bold text-primary">${formatPrice(product.price)} DZD</span>
                                    <span class="badge bg-light text-dark">${product.category_name || 'Sans catégorie'}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');

        container.innerHTML = productsHtml;
    }

    let selectedProductId = null;

    function selectProduct(productId) {
        // Remove previous selection
        document.querySelectorAll('.product-selector-card').forEach(card => {
            card.classList.remove('border-primary', 'bg-light');
        });

        // Add selection to clicked card
        const selectedCard = document.querySelector(`[data-product-id="${productId}"]`);
        if (selectedCard) {
            selectedCard.classList.add('border-primary', 'bg-light');
            selectedProductId = productId;
        }
    }

    function applyProductSelection() {
        if (!selectedProductId) {
            alert('Veuillez sélectionner un produit');
            return;
        }

        const displayMode = document.querySelector('input[name="displayMode"]:checked').value;
        const buyButtonPosition = document.getElementById('buyButtonPosition').value;
        const enableCart = document.getElementById('enableCart').checked;
        const showPricing = document.getElementById('showPricing').checked;
        const showQuantity = document.getElementById('showQuantity').checked;

        const config = {
            primaryProductId: selectedProductId,
            displayMode: displayMode,
            buyButtonPosition: buyButtonPosition,
            enableCart: enableCart,
            showPricing: showPricing,
            showQuantity: showQuantity
        };

        // Store configuration for the landing page
        sessionStorage.setItem('landingPageConfig', JSON.stringify(config));

        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('productSelectorModal'));
        modal.hide();

        // Redirect to page builder with product configuration
        window.location.href = `?page=page-builder&mode=create&product=${selectedProductId}&config=${encodeURIComponent(JSON.stringify(config))}`;
    }

    function formatPrice(price) {
        return new Intl.NumberFormat('fr-DZ', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(price);
    }

    function getStoreId() {
        // Get store ID from URL parameters or session
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('store_id') || sessionStorage.getItem('store_id') || '7';
    }

    // Template preview functionality
    let currentPreviewTemplateId = null;

    async function previewTemplate(templateId) {
        currentPreviewTemplateId = templateId;
        const modal = new bootstrap.Modal(document.getElementById('templatePreviewModal'));
        modal.show();

        // Load template content
        try {
            const response = await fetch(`/api/templates.php?action=get-content&template_id=${templateId}`);
            const data = await response.json();

            if (data.success) {
                const container = document.getElementById('templatePreviewContainer');

                // Create iframe for preview
                const iframe = document.createElement('iframe');
                iframe.style.width = '100%';
                iframe.style.height = '100%';
                iframe.style.border = 'none';

                // Write template content to iframe
                container.innerHTML = '';
                container.appendChild(iframe);

                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                iframeDoc.open();
                iframeDoc.write(data.data.content);
                iframeDoc.close();

            } else {
                document.getElementById('templatePreviewContainer').innerHTML = `
                    <div class="text-center py-5">
                        <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                        <h5>Erreur de chargement</h5>
                        <p class="text-muted">${data.error}</p>
                    </div>
                `;
            }
        } catch (error) {
            console.error('Error loading template preview:', error);
            document.getElementById('templatePreviewContainer').innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                    <h5>Erreur de connexion</h5>
                    <p class="text-muted">Impossible de charger l'aperçu du template</p>
                </div>
            `;
        }
    }

    function useTemplateFromPreview() {
        if (currentPreviewTemplateId) {
            bootstrap.Modal.getInstance(document.getElementById('templatePreviewModal')).hide();
            useTemplate(currentPreviewTemplateId);
        }
    }

    // Fullscreen toggle functionality
    let isFullscreen = false;

    function toggleFullscreen() {
        const modal = document.getElementById('templatePreviewModal');
        const modalDialog = modal.querySelector('.modal-dialog');
        const modalContent = modal.querySelector('.modal-content');
        const fullscreenIcon = document.getElementById('fullscreenIcon');

        if (!isFullscreen) {
            // Enter fullscreen
            modalDialog.style.maxWidth = '100vw';
            modalDialog.style.width = '100vw';
            modalDialog.style.margin = '0';
            modalContent.style.height = '100vh';
            modalContent.style.borderRadius = '0';
            fullscreenIcon.className = 'fas fa-compress';
            isFullscreen = true;
        } else {
            // Exit fullscreen
            modalDialog.style.maxWidth = '95vw';
            modalDialog.style.width = '95vw';
            modalDialog.style.margin = '1.75rem auto';
            modalContent.style.height = '95vh';
            modalContent.style.borderRadius = '';
            fullscreenIcon.className = 'fas fa-expand';
            isFullscreen = false;
        }
    }

    // Reset fullscreen state when modal is closed
    document.getElementById('templatePreviewModal').addEventListener('hidden.bs.modal', function() {
        if (isFullscreen) {
            toggleFullscreen();
        }
    });
</script>

<style>
    .landing-page-builder .stat-card {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border: 1px solid #e9ecef;
        display: flex;
        align-items: center;
        margin-bottom: 20px;
    }

    .landing-page-builder .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        color: white;
        font-size: 24px;
    }

    .landing-page-builder .stat-content h4 {
        margin: 0;
        font-size: 24px;
        font-weight: bold;
        color: #2c3e50;
    }

    .landing-page-builder .stat-content p {
        margin: 0;
        color: #7f8c8d;
        font-size: 14px;
    }

    .template-card {
        border: 1px solid #e9ecef;
        border-radius: 10px;
        overflow: hidden;
        transition: transform 0.2s, box-shadow 0.2s;
        background: white;
    }

    .template-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .template-preview {
        height: 200px;
        background: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
    }

    .template-preview img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .template-placeholder {
        color: #6c757d;
        font-size: 48px;
    }

    .template-info {
        padding: 15px;
    }

    .template-info h6 {
        margin: 0 0 8px 0;
        font-weight: 600;
        color: #2c3e50;
    }

    .ai-feature-card {
        border: 1px solid #e9ecef;
        border-radius: 10px;
        padding: 20px;
        text-align: center;
        background: white;
        margin-bottom: 20px;
        transition: transform 0.2s;
    }

    .ai-feature-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .ai-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 15px;
        color: white;
        font-size: 24px;
    }

    .ai-feature-card h6 {
        margin: 0 0 10px 0;
        font-weight: 600;
        color: #2c3e50;
    }

    .ai-feature-card p {
        color: #6c757d;
        margin-bottom: 15px;
        font-size: 14px;
    }

    .ai-prompt-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        border: 1px solid #e9ecef;
    }

    .ai-prompt-section h6 {
        margin: 0 0 15px 0;
        color: #2c3e50;
        font-weight: 600;
    }

    .landing-page-builder .card {
        border: none;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .landing-page-builder .card-header {
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        font-weight: 600;
    }

    .landing-page-builder .nav-tabs {
        border-bottom: 2px solid #e9ecef;
    }

    .landing-page-builder .nav-tabs .nav-link {
        border: none;
        color: #6c757d;
        font-weight: 500;
        padding: 12px 20px;
        border-radius: 0;
    }

    .landing-page-builder .nav-tabs .nav-link.active {
        color: #495057;
        background: none;
        border-bottom: 3px solid #007bff;
        font-weight: 600;
    }

    .landing-page-builder .nav-tabs .nav-link:hover {
        border-color: transparent;
        color: #007bff;
    }

    .landing-page-builder .table th {
        border-top: none;
        font-weight: 600;
        color: #495057;
        background: #f8f9fa;
    }

    .landing-page-builder .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
    }

    .landing-page-builder .badge {
        font-size: 0.75em;
        padding: 0.35em 0.65em;
    }

    @media (max-width: 768px) {
        .landing-page-builder .stat-card {
            flex-direction: column;
            text-align: center;
        }

        .landing-page-builder .stat-icon {
            margin-right: 0;
            margin-bottom: 10px;
        }

        .template-card {
            margin-bottom: 20px;
        }

        .ai-feature-card {
            margin-bottom: 15px;
        }
    }
</style>
