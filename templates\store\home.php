<!DOCTYPE html>
<html lang="fr" dir="ltr">
<?php
// Définir le slug pour ce store (medical-software-solutions)
$store['slug'] = 'medical-software-solutions';
?>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($store['store_name']); ?> - Boutique en ligne</title>
    <meta name="description" content="<?php echo htmlspecialchars($store['description'] ?? 'Découvrez nos produits de qualité'); ?>">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Custom Store Styles -->
    <style>
        :root {
            --primary-color: <?php echo $customization['primary_color'] ?? '#007bff'; ?>;
            --secondary-color: <?php echo $customization['secondary_color'] ?? '#6c757d'; ?>;
            --font-family: <?php
                            $fontMap = [
                                'system' => 'system-ui, -apple-system, sans-serif',
                                'roboto' => 'Roboto, sans-serif',
                                'open-sans' => 'Open Sans, sans-serif',
                                'lato' => 'Lato, sans-serif',
                                'montserrat' => 'Montserrat, sans-serif',
                                'poppins' => 'Poppins, sans-serif'
                            ];
                            echo $fontMap[$customization['font_family']] ?? 'system-ui, -apple-system, sans-serif';
                            ?>;
        }

        body {
            font-family: var(--font-family);
            background-color: #f8f9fa;
        }

        .store-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 2rem 0;
        }

        .store-logo {
            max-height: 80px;
            width: auto;
        }

        .store-stats {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 1rem;
        }

        .category-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: none;
            border-radius: 15px;
            overflow: hidden;
        }

        .category-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .product-card {
            transition: transform 0.3s ease;
            border: none;
            border-radius: 15px;
            overflow: hidden;
            height: 100%;
        }

        .product-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }

        .product-image {
            height: 200px;
            object-fit: cover;
            width: 100%;
        }

        .price-tag {
            background: var(--primary-color);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: bold;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .search-bar {
            border-radius: 25px;
            border: 2px solid var(--primary-color);
        }

        .search-btn {
            border-radius: 0 25px 25px 0;
            background: var(--primary-color);
            border: 2px solid var(--primary-color);
        }

        .category-filter {
            background: white;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .footer-store {
            background: #2c3e50;
            color: white;
            margin-top: 3rem;
        }

        .rating-stars {
            color: #ffc107;
        }

        .breadcrumb {
            background: transparent;
            padding: 0;
        }

        .breadcrumb-item+.breadcrumb-item::before {
            color: var(--primary-color);
        }
    </style>
</head>

<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm sticky-top">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="/store-frontend.php?store=medical-software-solutions">
                <?php if (!empty($store['logo_url'])): ?>
                    <img src="<?php echo htmlspecialchars($store['logo_url']); ?>" alt="Logo" class="store-logo me-2">
                <?php endif; ?>
                <?php if ($customization['show_store_name']): ?>
                    <span class="fw-bold" style="color: var(--primary-color);">
                        <?php echo htmlspecialchars($store['store_name']); ?>
                    </span>
                <?php endif; ?>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/store/<?php echo $store['slug']; ?>">
                            <i class="fas fa-home me-1"></i>Accueil
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/store/<?php echo $store['slug']; ?>/products">
                            <i class="fas fa-box me-1"></i>Produits
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/store/<?php echo $store['slug']; ?>/about">
                            <i class="fas fa-info-circle me-1"></i>À propos
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/store/<?php echo $store['slug']; ?>/contact">
                            <i class="fas fa-envelope me-1"></i>Contact
                        </a>
                    </li>
                </ul>

                <!-- Search Bar -->
                <?php if ($customization['show_search_bar']): ?>
                    <div class="d-flex me-3">
                        <form class="d-flex" action="/store/<?php echo $store['slug']; ?>/search" method="GET">
                            <input class="form-control search-bar" type="search" name="q" placeholder="Rechercher des produits..." style="border-right: none;">
                            <button class="btn btn-primary search-btn" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </form>
                    </div>
                <?php endif; ?>

                <!-- Cart and Account -->
                <div class="d-flex align-items-center">
                    <button class="btn btn-outline-primary me-2" onclick="toggleCart()">
                        <i class="fas fa-shopping-cart me-1"></i>
                        Panier <span class="badge bg-primary" id="cartCount">0</span>
                    </button>
                    <a href="/login.html" class="btn btn-primary">
                        <i class="fas fa-user me-1"></i>Connexion
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Store Header -->
    <div class="store-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <?php if ($customization['show_store_name']): ?>
                        <h1 class="display-4 fw-bold mb-2"><?php echo htmlspecialchars($store['store_name']); ?></h1>
                    <?php endif; ?>

                    <?php if ($customization['show_store_description'] && !empty($store['description'])): ?>
                        <p class="lead mb-3"><?php echo htmlspecialchars($store['description']); ?></p>
                    <?php endif; ?>

                    <!-- Store Rating -->
                    <?php if ($store['average_rating'] > 0): ?>
                        <div class="d-flex align-items-center mb-3">
                            <?php echo generateStarRating($store['average_rating']); ?>
                            <span class="ms-2 fs-5">
                                <?php echo number_format($store['average_rating'], 1); ?>/5
                                (<?php echo $store['total_ratings']; ?> avis)
                            </span>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="col-md-4">
                    <div class="store-stats text-center">
                        <div class="row">
                            <div class="col-6">
                                <div class="fs-3 fw-bold"><?php echo $store['product_count']; ?></div>
                                <div class="small">Produits</div>
                            </div>
                            <div class="col-6">
                                <div class="fs-3 fw-bold"><?php echo $store['category_count']; ?></div>
                                <div class="small">Catégories</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container my-5">
        <div class="row">
            <!-- Sidebar Categories (if enabled) -->
            <?php if ($customization['show_category_filter'] && $customization['category_position'] === 'sidebar'): ?>
                <div class="col-lg-3 mb-4">
                    <div class="category-filter p-4">
                        <h5 class="fw-bold mb-3">
                            <i class="fas fa-list me-2" style="color: var(--primary-color);"></i>
                            Catégories
                        </h5>

                        <?php
                        $categories = getStoreCategories($store['id']);
                        foreach ($categories as $category):
                        ?>
                            <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                                <a href="/store/<?php echo $store['slug']; ?>/category/<?php echo $category['slug']; ?>"
                                    class="text-decoration-none text-dark">
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </a>
                                <span class="badge bg-light text-dark"><?php echo $category['product_count']; ?></span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Main Content Area -->
            <div class="<?php echo ($customization['show_category_filter'] && $customization['category_position'] === 'sidebar') ? 'col-lg-9' : 'col-12'; ?>">

                <!-- Top Categories (if enabled) -->
                <?php if ($customization['show_category_filter'] && $customization['category_position'] === 'top'): ?>
                    <div class="mb-5">
                        <h3 class="fw-bold mb-4">
                            <i class="fas fa-th-large me-2" style="color: var(--primary-color);"></i>
                            Nos Catégories
                        </h3>

                        <div class="row">
                            <?php
                            $categories = getStoreCategories($store['id']);
                            foreach ($categories as $category):
                            ?>
                                <div class="col-md-4 col-lg-3 mb-3">
                                    <div class="card category-card h-100">
                                        <?php if (!empty($category['image_url'])): ?>
                                            <img src="<?php echo htmlspecialchars($category['image_url']); ?>"
                                                class="card-img-top" style="height: 150px; object-fit: cover;"
                                                alt="<?php echo htmlspecialchars($category['name']); ?>">
                                        <?php else: ?>
                                            <div class="card-img-top d-flex align-items-center justify-content-center bg-light"
                                                style="height: 150px;">
                                                <i class="fas fa-folder fa-3x text-muted"></i>
                                            </div>
                                        <?php endif; ?>

                                        <div class="card-body text-center">
                                            <h6 class="card-title"><?php echo htmlspecialchars($category['name']); ?></h6>
                                            <p class="text-muted small"><?php echo $category['product_count']; ?> produits</p>
                                            <a href="/store/<?php echo $store['slug']; ?>/category/<?php echo $category['slug']; ?>"
                                                class="btn btn-primary btn-sm">
                                                Voir les produits
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Featured Products -->
                <div class="mb-5">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3 class="fw-bold">
                            <i class="fas fa-star me-2" style="color: var(--primary-color);"></i>
                            Produits Vedettes
                        </h3>
                        <a href="/store/<?php echo $store['slug']; ?>/products" class="btn btn-outline-primary">
                            Voir tous les produits
                            <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>

                    <div class="row">
                        <?php
                        $featuredProducts = getStoreProducts($store['id'], ['per_page' => 8, 'sort' => 'popular']);
                        foreach ($featuredProducts as $product):
                        ?>
                            <div class="col-md-6 col-lg-<?php echo 12 / ($customization['products_per_row'] ?? 3); ?> mb-4">
                                <div class="card product-card">
                                    <?php if (!empty($product['image_url'])): ?>
                                        <img src="<?php echo htmlspecialchars($product['image_url']); ?>"
                                            class="product-image" alt="<?php echo htmlspecialchars($product['name']); ?>">
                                    <?php else: ?>
                                        <div class="product-image d-flex align-items-center justify-content-center bg-light">
                                            <i class="fas fa-image fa-3x text-muted"></i>
                                        </div>
                                    <?php endif; ?>

                                    <div class="card-body">
                                        <h6 class="card-title"><?php echo htmlspecialchars($product['name']); ?></h6>

                                        <?php if ($customization['show_product_description'] ?? true): ?>
                                            <p class="card-text text-muted small">
                                                <?php echo truncateText($product['description'] ?? '', 80); ?>
                                            </p>
                                        <?php endif; ?>

                                        <?php if (isset($product['average_rating']) && $product['average_rating'] > 0): ?>
                                            <div class="mb-2">
                                                <?php echo generateStarRating($product['average_rating']); ?>
                                                <small class="text-muted ms-1">
                                                    (<?php echo $product['review_count'] ?? 0; ?>)
                                                </small>
                                            </div>
                                        <?php endif; ?>

                                        <div class="d-flex justify-content-between align-items-center">
                                            <?php if ($customization['show_product_price'] ?? true): ?>
                                                <span class="price-tag">
                                                    <?php echo formatPrice($product['price']); ?>
                                                </span>
                                            <?php endif; ?>

                                            <?php if ($customization['show_add_to_cart'] ?? true): ?>
                                                <button class="btn btn-primary btn-sm" onclick="addToCart(<?php echo $product['id']; ?>)">
                                                    <i class="fas fa-cart-plus me-1"></i>Ajouter
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer-store py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5><?php echo htmlspecialchars($store['store_name']); ?></h5>
                    <p><?php echo htmlspecialchars($store['description'] ?? ''); ?></p>
                </div>
                <div class="col-md-4">
                    <h6>Contact</h6>
                    <?php if (!empty($store['phone'])): ?>
                        <p><i class="fas fa-phone me-2"></i><?php echo htmlspecialchars($store['phone']); ?></p>
                    <?php endif; ?>
                    <?php if (!empty($store['email'])): ?>
                        <p><i class="fas fa-envelope me-2"></i><?php echo htmlspecialchars($store['email']); ?></p>
                    <?php endif; ?>
                    <?php if (!empty($store['address'])): ?>
                        <p><i class="fas fa-map-marker-alt me-2"></i><?php echo htmlspecialchars($store['address']); ?></p>
                    <?php endif; ?>
                </div>
                <div class="col-md-4">
                    <h6>Liens rapides</h6>
                    <ul class="list-unstyled">
                        <li><a href="/store/<?php echo $store['slug']; ?>" class="text-light text-decoration-none">Accueil</a></li>
                        <li><a href="/store/<?php echo $store['slug']; ?>/products" class="text-light text-decoration-none">Produits</a></li>
                        <li><a href="/store/<?php echo $store['slug']; ?>/about" class="text-light text-decoration-none">À propos</a></li>
                        <li><a href="/store/<?php echo $store['slug']; ?>/contact" class="text-light text-decoration-none">Contact</a></li>
                    </ul>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p class="mb-0">&copy; <?php echo date('Y'); ?> <?php echo htmlspecialchars($store['store_name']); ?>. Tous droits réservés.</p>
            </div>
        </div>
    </footer>

    <!-- Shopping Cart Sidebar -->
    <div class="offcanvas offcanvas-end" tabindex="-1" id="cartSidebar">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title">Panier</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas"></button>
        </div>
        <div class="offcanvas-body">
            <div id="cartItems">
                <p class="text-muted text-center">Votre panier est vide</p>
            </div>
            <div class="mt-auto">
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" onclick="proceedToCheckout()">
                        <i class="fas fa-credit-card me-1"></i>Commander
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Store JavaScript -->
    <script>
        // Shopping cart functionality
        let cart = JSON.parse(localStorage.getItem('cart_<?php echo $store['id']; ?>') || '[]');

        function updateCartCount() {
            document.getElementById('cartCount').textContent = cart.length;
        }

        function addToCart(productId) {
            // Add product to cart
            cart.push({
                id: productId,
                quantity: 1,
                store_id: <?php echo $store['id']; ?>
            });

            localStorage.setItem('cart_<?php echo $store['id']; ?>', JSON.stringify(cart));
            updateCartCount();

            // Show success message
            showNotification('Produit ajouté au panier!', 'success');
        }

        function toggleCart() {
            const cartSidebar = new bootstrap.Offcanvas(document.getElementById('cartSidebar'));
            cartSidebar.show();
        }

        function proceedToCheckout() {
            if (cart.length === 0) {
                showNotification('Votre panier est vide', 'warning');
                return;
            }

            // Redirect to checkout
            window.location.href = '/templates/checkout/guest-checkout.html';
        }

        function showNotification(message, type = 'info') {
            // Create notification
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // Initialize cart count on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateCartCount();
        });
    </script>
</body>

</html>
