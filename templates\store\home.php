<!DOCTYPE html>
<html lang="fr" dir="ltr">
<?php
// Définir le slug pour ce store (medical-software-solutions)
$store['slug'] = 'medical-software-solutions';
?>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($store['store_name']); ?> - Boutique en ligne</title>
    <meta name="description" content="<?php echo htmlspecialchars($store['description'] ?? 'Découvrez nos produits de qualité'); ?>">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Custom Store Styles -->
    <style>
        :root {
            --primary-color: <?php echo $customization['primary_color'] ?? '#2c5aa0'; ?>;
            --secondary-color: <?php echo $customization['secondary_color'] ?? '#6c757d'; ?>;
            --accent-color: #f8f9fa;
            --text-dark: #2d3748;
            --text-light: #718096;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --font-family: <?php
                            $fontMap = [
                                'system' => 'system-ui, -apple-system, sans-serif',
                                'roboto' => 'Roboto, sans-serif',
                                'open-sans' => 'Open Sans, sans-serif',
                                'lato' => 'Lato, sans-serif',
                                'montserrat' => 'Montserrat, sans-serif',
                                'poppins' => 'Poppins, sans-serif'
                            ];
                            echo $fontMap[$customization['font_family']] ?? 'system-ui, -apple-system, sans-serif';
                            ?>;
        }

        body {
            font-family: var(--font-family);
            color: var(--text-dark);
            line-height: 1.6;
            background-color: #ffffff;
        }

        /* Professional Navigation */
        .navbar {
            box-shadow: var(--shadow-sm);
            border-bottom: 1px solid var(--border-color);
            background: white !important;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }

        .nav-link {
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
            color: var(--text-dark) !important;
        }

        .nav-link:hover {
            color: var(--primary-color) !important;
        }

        .nav-link.active {
            color: var(--primary-color) !important;
        }

        .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 3px;
            background: var(--primary-color);
            border-radius: 2px;
        }

        /* Store Logo */
        .store-logo {
            height: 40px;
            width: auto;
        }

        /* Professional Search Bar */
        .search-bar {
            border-radius: 25px 0 0 25px;
            border-right: none;
            padding: 12px 20px;
            border-color: var(--border-color);
        }

        .search-btn {
            border-radius: 0 25px 25px 0;
            padding: 12px 20px;
            background: var(--primary-color);
            border-color: var(--primary-color);
        }

        /* Cart Button */
        .cart-btn {
            border-radius: 50px;
            padding: 12px 20px;
            font-weight: 600;
        }

        .store-stats {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 1rem;
        }

        .category-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: none;
            border-radius: 15px;
            overflow: hidden;
        }

        .category-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .product-card {
            border: 1px solid var(--border-color);
            border-radius: 12px;
            transition: all 0.3s ease;
            overflow: hidden;
            background: white;
            height: 100%;
        }

        .product-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-color);
        }

        .product-image {
            height: 220px;
            object-fit: cover;
            background: var(--accent-color);
            transition: transform 0.3s ease;
            width: 100%;
        }

        .product-card:hover .product-image {
            transform: scale(1.05);
        }

        .product-price {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .product-old-price {
            text-decoration: line-through;
            color: var(--text-light);
            font-size: 0.9rem;
        }

        .price-tag {
            background: var(--primary-color);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: bold;
        }

        /* Professional Buttons */
        .btn-primary {
            background: var(--primary-color);
            border-color: var(--primary-color);
            font-weight: 600;
            padding: 12px 24px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: color-mix(in srgb, var(--primary-color) 85%, black);
            border-color: color-mix(in srgb, var(--primary-color) 85%, black);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        /* Professional Sections */
        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 3rem;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 4px;
            background: var(--primary-color);
            border-radius: 2px;
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, var(--primary-color), color-mix(in srgb, var(--primary-color) 80%, white));
            color: white;
            padding: 80px 0;
            margin-bottom: 60px;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 1.5rem;
        }

        .hero-subtitle {
            font-size: 1.25rem;
            opacity: 0.9;
            margin-bottom: 2rem;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.1rem;
            }

            .section-title {
                font-size: 2rem;
            }
        }

        .btn-primary:hover {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .search-bar {
            border-radius: 25px;
            border: 2px solid var(--primary-color);
        }

        .search-btn {
            border-radius: 0 25px 25px 0;
            background: var(--primary-color);
            border: 2px solid var(--primary-color);
        }

        .category-filter {
            background: white;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .footer-store {
            background: #2c3e50;
            color: white;
            margin-top: 3rem;
        }

        .rating-stars {
            color: #ffc107;
        }

        .breadcrumb {
            background: transparent;
            padding: 0;
        }

        .breadcrumb-item+.breadcrumb-item::before {
            color: var(--primary-color);
        }
    </style>
</head>

<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm sticky-top">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="/store-frontend.php?store=medical-software-solutions">
                <?php if (!empty($store['logo_url'])): ?>
                    <img src="<?php echo htmlspecialchars($store['logo_url']); ?>" alt="Logo" class="store-logo me-2">
                <?php endif; ?>
                <?php if ($customization['show_store_name']): ?>
                    <span class="fw-bold" style="color: var(--primary-color);">
                        <?php echo htmlspecialchars($store['store_name']); ?>
                    </span>
                <?php endif; ?>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <?php
                    $currentPage = $_GET['page'] ?? 'home';
                    $storeSlug = 'medical-software-solutions';
                    $storeSettings = json_decode($store['settings'] ?? '{}', true);
                    $pageSettings = $storeSettings['pages'] ?? ['home' => true, 'products' => true, 'about' => true, 'contact' => true];
                    ?>

                    <?php if ($pageSettings['home'] !== false): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo $currentPage === 'home' ? 'active' : ''; ?>"
                                href="/store-frontend.php?store=<?php echo $storeSlug; ?>">
                                <i class="fas fa-home me-1"></i>Accueil
                            </a>
                        </li>
                    <?php endif; ?>

                    <?php if ($pageSettings['products'] !== false): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo $currentPage === 'products' ? 'active' : ''; ?>"
                                href="/store-frontend.php?store=<?php echo $storeSlug; ?>&page=products">
                                <i class="fas fa-box me-1"></i>Produits
                            </a>
                        </li>
                    <?php endif; ?>

                    <?php if ($pageSettings['about'] !== false): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo $currentPage === 'about' ? 'active' : ''; ?>"
                                href="/store-frontend.php?store=<?php echo $storeSlug; ?>&page=about">
                                <i class="fas fa-info-circle me-1"></i>À propos
                            </a>
                        </li>
                    <?php endif; ?>

                    <?php if ($pageSettings['contact'] !== false): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo $currentPage === 'contact' ? 'active' : ''; ?>"
                                href="/store-frontend.php?store=<?php echo $storeSlug; ?>&page=contact">
                                <i class="fas fa-envelope me-1"></i>Contact
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>

                <!-- Search Bar -->
                <?php if ($customization['show_search_bar']): ?>
                    <div class="d-flex me-3">
                        <form class="d-flex" action="/store-frontend.php" method="GET">
                            <input type="hidden" name="store" value="medical-software-solutions">
                            <input type="hidden" name="page" value="search">
                            <input class="form-control search-bar" type="search" name="q" placeholder="Rechercher des produits..." style="border-right: none;">
                            <button class="btn btn-primary search-btn" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </form>
                    </div>
                <?php endif; ?>

                <!-- Cart and Account -->
                <div class="d-flex align-items-center">
                    <button class="btn btn-outline-primary me-2" onclick="toggleCart()">
                        <i class="fas fa-shopping-cart me-1"></i>
                        Panier <span class="badge bg-primary" id="cartCount">0</span>
                    </button>
                    <a href="/login.html" class="btn btn-primary">
                        <i class="fas fa-user me-1"></i>Connexion
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Store Header -->
    <div class="store-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <?php if ($customization['show_store_name']): ?>
                        <h1 class="display-4 fw-bold mb-2"><?php echo htmlspecialchars($store['store_name']); ?></h1>
                    <?php endif; ?>

                    <?php if ($customization['show_store_description'] && !empty($store['description'])): ?>
                        <p class="lead mb-3"><?php echo htmlspecialchars($store['description']); ?></p>
                    <?php endif; ?>

                    <!-- Store Rating -->
                    <?php if ($store['average_rating'] > 0): ?>
                        <div class="d-flex align-items-center mb-3">
                            <?php echo generateStarRating($store['average_rating']); ?>
                            <span class="ms-2 fs-5">
                                <?php echo number_format($store['average_rating'], 1); ?>/5
                                (<?php echo $store['total_ratings']; ?> avis)
                            </span>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="col-md-4">
                    <div class="store-stats text-center">
                        <div class="row">
                            <div class="col-6">
                                <div class="fs-3 fw-bold"><?php echo $store['product_count']; ?></div>
                                <div class="small">Produits</div>
                            </div>
                            <div class="col-6">
                                <div class="fs-3 fw-bold"><?php echo $store['category_count']; ?></div>
                                <div class="small">Catégories</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="hero-title">
                        <?php echo htmlspecialchars($store['store_name']); ?>
                    </h1>
                    <p class="hero-subtitle">
                        <?php echo htmlspecialchars($store['description'] ?? 'Solutions logicielles et équipements médicaux professionnels'); ?>
                    </p>
                    <div class="d-flex gap-3 flex-wrap">
                        <a href="/store-frontend.php?store=medical-software-solutions&page=products" class="btn btn-light btn-lg">
                            <i class="fas fa-box me-2"></i>Voir nos produits
                        </a>
                        <a href="/store-frontend.php?store=medical-software-solutions&page=contact" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-envelope me-2"></i>Nous contacter
                        </a>
                    </div>
                </div>
                <div class="col-lg-4 text-center">
                    <div class="hero-stats">
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="stat-item">
                                    <h3 class="fw-bold"><?php echo $store['product_count'] ?? count($products); ?>+</h3>
                                    <p class="mb-0">Produits</p>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-item">
                                    <h3 class="fw-bold"><?php echo $store['category_count'] ?? 7; ?>+</h3>
                                    <p class="mb-0">Catégories</p>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-item">
                                    <h3 class="fw-bold">5★</h3>
                                    <p class="mb-0">Qualité</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <div class="container my-5">
        <div class="row">
            <!-- Sidebar Categories (if enabled) -->
            <?php if ($customization['show_category_filter'] && $customization['category_position'] === 'sidebar'): ?>
                <div class="col-lg-3 mb-4">
                    <div class="category-filter p-4">
                        <h5 class="fw-bold mb-3">
                            <i class="fas fa-list me-2" style="color: var(--primary-color);"></i>
                            Catégories
                        </h5>

                        <?php
                        $categories = getStoreCategories($store['id']);
                        foreach ($categories as $category):
                        ?>
                            <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                                <a href="/store-frontend.php?store=<?php echo $store['slug']; ?>&page=category&category=<?php echo urlencode($category['name']); ?>"
                                    class="text-decoration-none text-dark">
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </a>
                                <span class="badge bg-light text-dark"><?php echo $category['product_count']; ?></span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Main Content Area -->
            <div class="<?php echo ($customization['show_category_filter'] && $customization['category_position'] === 'sidebar') ? 'col-lg-9' : 'col-12'; ?>">

                <!-- Top Categories (if enabled) -->
                <?php if ($customization['show_category_filter'] && $customization['category_position'] === 'top'): ?>
                    <div class="mb-5">
                        <h3 class="fw-bold mb-4">
                            <i class="fas fa-th-large me-2" style="color: var(--primary-color);"></i>
                            Nos Catégories
                        </h3>

                        <div class="row">
                            <?php
                            $categories = getStoreCategories($store['id']);
                            foreach ($categories as $category):
                            ?>
                                <div class="col-md-4 col-lg-3 mb-3">
                                    <div class="card category-card h-100">
                                        <?php if (!empty($category['image_url'])): ?>
                                            <img src="<?php echo htmlspecialchars($category['image_url']); ?>"
                                                class="card-img-top" style="height: 150px; object-fit: cover;"
                                                alt="<?php echo htmlspecialchars($category['name']); ?>">
                                        <?php else: ?>
                                            <div class="card-img-top d-flex align-items-center justify-content-center bg-light"
                                                style="height: 150px;">
                                                <i class="fas fa-folder fa-3x text-muted"></i>
                                            </div>
                                        <?php endif; ?>

                                        <div class="card-body text-center">
                                            <h6 class="card-title"><?php echo htmlspecialchars($category['name']); ?></h6>
                                            <p class="text-muted small"><?php echo $category['product_count']; ?> produits</p>
                                            <a href="/store-frontend.php?store=<?php echo $store['slug']; ?>&page=category&category=<?php echo urlencode($category['name']); ?>"
                                                class="btn btn-primary btn-sm">
                                                Voir les produits
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Featured Products -->
                <div class="mb-5">
                    <div class="text-center mb-5">
                        <h2 class="section-title">
                            Produits Vedettes
                        </h2>
                        <p class="text-muted mb-4">Découvrez notre sélection de produits les plus populaires</p>
                    </div>

                    <div class="row">
                        <?php
                        $featuredProducts = getStoreProducts($store['id'], ['per_page' => 8, 'sort' => 'popular']);
                        foreach ($featuredProducts as $product):
                        ?>
                            <div class="col-md-6 col-lg-<?php echo 12 / ($customization['products_per_row'] ?? 3); ?> mb-4">
                                <div class="card product-card">
                                    <?php
                                    // Handle both image_url and images fields
                                    $imageUrl = '';
                                    if (!empty($product['image_url'])) {
                                        $imageUrl = $product['image_url'];
                                    } elseif (!empty($product['images'])) {
                                        // If images is JSON, decode and get first image
                                        $images = json_decode($product['images'], true);
                                        if (is_array($images) && !empty($images)) {
                                            $imageUrl = $images[0];
                                        } elseif (is_string($product['images'])) {
                                            $imageUrl = $product['images'];
                                        }
                                    }
                                    ?>
                                    <?php if (!empty($imageUrl)): ?>
                                        <img src="<?php echo htmlspecialchars($imageUrl); ?>"
                                            class="product-image" alt="<?php echo htmlspecialchars($product['name']); ?>">
                                    <?php else: ?>
                                        <div class="product-image d-flex align-items-center justify-content-center bg-light">
                                            <i class="fas fa-image fa-3x text-muted"></i>
                                        </div>
                                    <?php endif; ?>

                                    <div class="card-body">
                                        <!-- Compact Product Info -->
                                        <h6 class="card-title mb-2"><?php echo htmlspecialchars($product['name']); ?></h6>

                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <span class="h6 text-primary mb-0"><?php echo number_format($product['price'], 2); ?> DZD</span>
                                        </div>

                                        <!-- Compact Action Buttons -->
                                        <div class="d-flex gap-2">
                                            <button class="btn btn-outline-primary btn-sm flex-grow-1"
                                                onclick="showProductDetails(<?php echo $product['id']; ?>)">
                                                <i class="fas fa-eye me-1"></i>Voir détail
                                            </button>
                                            <button class="btn btn-primary btn-sm"
                                                onclick="addToCart(<?php echo $product['id']; ?>)"
                                                title="Ajouter au panier">
                                                <i class="fas fa-shopping-cart"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- View All Products Button -->
                    <div class="text-center mt-5">
                        <a href="/store-frontend.php?store=medical-software-solutions&page=products" class="btn btn-primary btn-lg">
                            <i class="fas fa-box me-2"></i>Voir tous nos produits
                            <i class="fas fa-arrow-right ms-2"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-light py-5 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5><?php echo htmlspecialchars($store['store_name']); ?></h5>
                    <p><?php echo htmlspecialchars($store['description'] ?? ''); ?></p>
                </div>
                <div class="col-md-4">
                    <h6>Contact</h6>
                    <?php if (!empty($store['phone'])): ?>
                        <p><i class="fas fa-phone me-2"></i><?php echo htmlspecialchars($store['phone']); ?></p>
                    <?php endif; ?>
                    <?php if (!empty($store['email'])): ?>
                        <p><i class="fas fa-envelope me-2"></i><?php echo htmlspecialchars($store['email']); ?></p>
                    <?php endif; ?>
                    <?php if (!empty($store['address'])): ?>
                        <p><i class="fas fa-map-marker-alt me-2"></i><?php echo htmlspecialchars($store['address']); ?></p>
                    <?php endif; ?>
                </div>
                <div class="col-md-4">
                    <h6>Liens rapides</h6>
                    <ul class="list-unstyled">
                        <?php if ($pageSettings['home'] !== false): ?>
                            <li><a href="/store-frontend.php?store=medical-software-solutions" class="text-light text-decoration-none">Accueil</a></li>
                        <?php endif; ?>
                        <?php if ($pageSettings['products'] !== false): ?>
                            <li><a href="/store-frontend.php?store=medical-software-solutions&page=products" class="text-light text-decoration-none">Produits</a></li>
                        <?php endif; ?>
                        <?php if ($pageSettings['about'] !== false): ?>
                            <li><a href="/store-frontend.php?store=medical-software-solutions&page=about" class="text-light text-decoration-none">À propos</a></li>
                        <?php endif; ?>
                        <?php if ($pageSettings['contact'] !== false): ?>
                            <li><a href="/store-frontend.php?store=medical-software-solutions&page=contact" class="text-light text-decoration-none">Contact</a></li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p class="mb-0">&copy; <?php echo date('Y'); ?> <?php echo htmlspecialchars($store['store_name']); ?>. Tous droits réservés.</p>
            </div>
        </div>
    </footer>

    <!-- Shopping Cart Sidebar -->
    <div class="offcanvas offcanvas-end" tabindex="-1" id="cartSidebar">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title">Panier</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas"></button>
        </div>
        <div class="offcanvas-body">
            <div id="cartItems">
                <p class="text-muted text-center">Votre panier est vide</p>
            </div>
            <div class="mt-auto">
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" onclick="proceedToCheckout()">
                        <i class="fas fa-credit-card me-1"></i>Commander
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Store JavaScript -->
    <script>
        // Shopping cart functionality
        let cart = JSON.parse(localStorage.getItem('cart_<?php echo $store['id']; ?>') || '[]');

        function updateCartCount() {
            document.getElementById('cartCount').textContent = cart.length;
        }

        function addToCart(productId) {
            // Add product to cart
            cart.push({
                id: productId,
                quantity: 1,
                store_id: <?php echo $store['id']; ?>
            });

            localStorage.setItem('cart_<?php echo $store['id']; ?>', JSON.stringify(cart));
            updateCartCount();

            // Show success message
            showNotification('Produit ajouté au panier!', 'success');
        }

        function toggleCart() {
            const cartSidebar = new bootstrap.Offcanvas(document.getElementById('cartSidebar'));
            cartSidebar.show();
        }

        function proceedToCheckout() {
            if (cart.length === 0) {
                showNotification('Votre panier est vide', 'warning');
                return;
            }

            // Redirect to checkout
            window.location.href = '/templates/checkout/guest-checkout.html';
        }

        function showNotification(message, type = 'info') {
            // Create notification
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // Initialize cart count on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateCartCount();
        });

        // Product details functionality
        let currentProductId = null;
        let productDetailsModal = null;

        function showProductDetails(productId) {
            currentProductId = productId;

            // Initialize modal if not already done
            if (!productDetailsModal) {
                const modalElement = document.getElementById('productDetailsModal');
                if (modalElement) {
                    productDetailsModal = new bootstrap.Modal(modalElement);
                } else {
                    console.error('Modal element not found');
                    return;
                }
            }

            // Show modal with loading state
            productDetailsModal.show();

            // Load product details
            loadProductDetails(productId);
        }

        async function loadProductDetails(productId) {
            try {
                // Find product in current featured products array
                const featuredProducts = <?php echo json_encode($featuredProducts ?? []); ?>;
                const product = featuredProducts.find(p => p.id == productId);

                if (!product) {
                    throw new Error('Produit non trouvé');
                }

                // Handle image URL
                let imageUrl = '';
                if (product.image_url) {
                    imageUrl = product.image_url;
                } else if (product.images) {
                    const images = typeof product.images === 'string' ? JSON.parse(product.images) : product.images;
                    if (Array.isArray(images) && images.length > 0) {
                        imageUrl = images[0];
                    } else if (typeof product.images === 'string') {
                        imageUrl = product.images;
                    }
                }

                // Build product details HTML
                const detailsHtml = `
                    <div class="row">
                        <div class="col-md-6">
                            ${imageUrl ?
                                `<img src="${imageUrl}" class="img-fluid rounded" alt="${product.name}">` :
                                `<div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 300px;">
                                    <i class="fas fa-image fa-4x text-muted"></i>
                                </div>`
                            }
                        </div>
                        <div class="col-md-6">
                            <h4>${product.name}</h4>
                            <p class="text-muted">${product.categories || 'Aucune catégorie'}</p>
                            <h5 class="text-primary">${parseFloat(product.price).toLocaleString('fr-FR', {minimumFractionDigits: 2})} DZD</h5>

                            <div class="mt-3">
                                <h6>Description:</h6>
                                <div class="description-content">
                                    ${product.description || 'Aucune description disponible'}
                                </div>
                            </div>

                            ${product.sku ? `<p class="mt-3"><strong>SKU:</strong> ${product.sku}</p>` : ''}
                            ${product.stock ? `<p><strong>Stock:</strong> ${product.stock} unités disponibles</p>` : ''}
                        </div>
                    </div>
                `;

                document.getElementById('productDetailsContent').innerHTML = detailsHtml;

                // Update add to cart button in modal
                document.getElementById('addToCartFromModal').onclick = () => {
                    addToCart(productId);
                    productDetailsModal.hide();
                };

            } catch (error) {
                console.error('Error loading product details:', error);
                document.getElementById('productDetailsContent').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Erreur lors du chargement des détails du produit: ${error.message}
                    </div>
                `;
            }
        }
    </script>

    <!-- Product Details Modal -->
    <div class="modal fade" id="productDetailsModal" tabindex="-1" aria-labelledby="productDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="productDetailsModalLabel">Détails du produit</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="productDetailsContent">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                    <button type="button" class="btn btn-primary" id="addToCartFromModal">
                        <i class="fas fa-shopping-cart me-1"></i>Ajouter au panier
                    </button>
                </div>
            </div>
        </div>
    </div>

</body>

</html>
