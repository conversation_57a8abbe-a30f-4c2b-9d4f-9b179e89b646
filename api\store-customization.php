<?php
/**
 * Store Customization API
 * Handles store customization settings including page management
 */

header('Content-Type: application/json');
require_once '../dashboard/includes/database.php';

// Authentication check
session_start();
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'merchant') {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

$action = $_GET['action'] ?? $_POST['action'] ?? '';
$storeId = $_GET['store_id'] ?? $_POST['store_id'] ?? '';

if (!$storeId) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Store ID required']);
    exit;
}

try {
    $db = connectDB();
    
    switch ($action) {
        case 'save_customization':
            saveStoreCustomization($db, $storeId);
            break;
            
        case 'get_customization':
            getStoreCustomization($db, $storeId);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}

function saveStoreCustomization($db, $storeId) {
    try {
        // Get current store settings
        $query = "SELECT settings FROM stores WHERE id = ? AND merchant_id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$storeId, $_SESSION['user']['id']]);
        $store = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$store) {
            throw new Exception('Store not found');
        }
        
        // Parse existing settings
        $currentSettings = json_decode($store['settings'] ?? '{}', true) ?: [];
        
        // Update with new customization data
        if (isset($_POST['page_settings'])) {
            $pageSettings = json_decode($_POST['page_settings'], true);
            $currentSettings['pages'] = $pageSettings;
        }
        
        // Update branding settings
        $currentSettings['branding'] = [
            'primary_color' => $_POST['primary_color'] ?? '#007bff',
            'secondary_color' => $_POST['secondary_color'] ?? '#6c757d',
            'font_family' => $_POST['font_family'] ?? 'system'
        ];
        
        // Update layout settings
        $currentSettings['layout'] = [
            'style' => $_POST['layout_style'] ?? 'grid',
            'show_store_name' => isset($_POST['show_store_name']) ? (bool)$_POST['show_store_name'] : true,
            'show_store_description' => isset($_POST['show_store_description']) ? (bool)$_POST['show_store_description'] : true,
            'show_search_bar' => isset($_POST['show_search_bar']) ? (bool)$_POST['show_search_bar'] : true,
            'show_category_filter' => isset($_POST['show_category_filter']) ? (bool)$_POST['show_category_filter'] : true,
            'category_position' => $_POST['category_position'] ?? 'sidebar',
            'products_per_page' => (int)($_POST['products_per_page'] ?? 12),
            'products_per_row' => (int)($_POST['products_per_row'] ?? 3)
        ];
        
        // Update product settings
        $currentSettings['products'] = [
            'show_price' => isset($_POST['show_price']) ? (bool)$_POST['show_price'] : true,
            'show_description' => isset($_POST['show_description']) ? (bool)$_POST['show_description'] : true,
            'show_add_to_cart' => isset($_POST['show_add_to_cart']) ? (bool)$_POST['show_add_to_cart'] : true,
            'show_quick_view' => isset($_POST['show_quick_view']) ? (bool)$_POST['show_quick_view'] : false,
            'default_sort' => $_POST['default_sort'] ?? 'newest'
        ];
        
        // Update SEO settings
        $currentSettings['seo'] = [
            'meta_title' => $_POST['meta_title'] ?? '',
            'meta_description' => $_POST['meta_description'] ?? '',
            'meta_keywords' => $_POST['meta_keywords'] ?? '',
            'og_title' => $_POST['og_title'] ?? '',
            'og_description' => $_POST['og_description'] ?? ''
        ];
        
        // Save to database
        $updateQuery = "UPDATE stores SET settings = ?, updated_at = NOW() WHERE id = ? AND merchant_id = ?";
        $updateStmt = $db->prepare($updateQuery);
        $result = $updateStmt->execute([
            json_encode($currentSettings),
            $storeId,
            $_SESSION['user']['id']
        ]);
        
        if ($result) {
            echo json_encode(['success' => true, 'message' => 'Customization saved successfully']);
        } else {
            throw new Exception('Failed to save customization');
        }
        
    } catch (Exception $e) {
        throw new Exception('Error saving customization: ' . $e->getMessage());
    }
}

function getStoreCustomization($db, $storeId) {
    try {
        $query = "SELECT settings FROM stores WHERE id = ? AND merchant_id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$storeId, $_SESSION['user']['id']]);
        $store = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$store) {
            throw new Exception('Store not found');
        }
        
        $settings = json_decode($store['settings'] ?? '{}', true) ?: [];
        
        echo json_encode(['success' => true, 'settings' => $settings]);
        
    } catch (Exception $e) {
        throw new Exception('Error getting customization: ' . $e->getMessage());
    }
}
?>
