# Redirection automatique vers index.html
DirectoryIndex index.html

# Redirection de la racine vers index.html
RewriteEngine On

# Redirection depuis store-landing.php vers /store/
RewriteCond %{QUERY_STRING} ^store=(.*)$ [NC]
RewriteCond %1 ([^&]+)
RewriteRule ^store-landing\.php$ /store/%1? [B,NE,R=301,L]

# Redirection pour les URLs de store frontend (medical-software-solutions)
RewriteRule ^store/medical-software-solutions/category/(.+)$ store-frontend.php?store=medical-software-solutions&page=category&category=$1 [QSA,L]
RewriteRule ^store/medical-software-solutions/(.*)$ store-frontend.php?store=medical-software-solutions&page=$1 [QSA,L]
RewriteRule ^store/medical-software-solutions/?$ store-frontend.php?store=medical-software-solutions [QSA,L]

# Redirection pour les URLs de stores avec espaces
RewriteCond %{REQUEST_URI} ^/store/([^/]+)\ ([^/]+) [NC]
RewriteRule ^store/(.*)$ store-landing.php?store=%1+%2 [QSA,L]

# Redirection pour les URLs de stores sans espaces (landing pages)
RewriteRule ^store/([^/]+)/?(.*)$ store-landing.php?store=$1 [QSA,L]

# Redirection pour les subdomains personnalisés
# Format: /{subdomain} ou /{subdomain}/{landing_page_slug}
# Exclure les fichiers et dossiers existants
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/(api|js|css|images|assets|admin|dashboard|preview\.php|store-landing\.php|index\.html)
RewriteRule ^([^/]+)/?(.*)$ store-landing.php?store=$1 [QSA,L]

# Redirection pour l'ancien système de preview
RewriteRule ^preview\.php\?id=([0-9]+)$ /api/landing-pages.php?action=preview&id=$1 [R=301,L]

# Redirection de la racine vers index.html (doit être après les autres règles)
RewriteRule ^$ index.html [L]

# Headers pour éviter le cache
<FilesMatch "\.(html|htm|js|css)$">
    Header set Cache-Control "no-cache, no-store, must-revalidate"
    Header set Pragma "no-cache"
    Header set Expires 0
</FilesMatch>

# Support UTF-8
AddDefaultCharset UTF-8

# Compression GZIP
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>
