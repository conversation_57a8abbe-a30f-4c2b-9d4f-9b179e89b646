<!DOCTYPE html>
<html lang="fr" dir="ltr">
<?php
// Définir le slug pour ce store (medical-software-solutions)
$store['slug'] = 'medical-software-solutions';
?>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact - <?php echo htmlspecialchars($store['store_name']); ?></title>
    <meta name="description" content="Contactez <?php echo htmlspecialchars($store['store_name']); ?> - Nous sommes là pour vous aider">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Custom Store Styles -->
    <style>
        :root {
            --primary-color: <?php echo $customization['primary_color'] ?? '#2c5aa0'; ?>;
            --secondary-color: <?php echo $customization['secondary_color'] ?? '#6c757d'; ?>;
            --text-dark: #2d3748;
            --text-light: #718096;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        body {
            font-family: system-ui, -apple-system, sans-serif;
            color: var(--text-dark);
            line-height: 1.6;
        }

        .navbar {
            box-shadow: var(--shadow-sm);
            border-bottom: 1px solid var(--border-color);
        }

        .nav-link {
            font-weight: 500;
            transition: all 0.3s ease;
            color: var(--text-dark) !important;
        }

        .nav-link:hover {
            color: var(--primary-color) !important;
        }

        .nav-link.active {
            color: var(--primary-color) !important;
        }

        .hero-section {
            background: linear-gradient(135deg, var(--primary-color), color-mix(in srgb, var(--primary-color) 80%, white));
            color: white;
            padding: 100px 0;
        }

        .hero-title {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 1rem;
        }

        .contact-card {
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            height: 100%;
            background: white;
        }

        .contact-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-md);
            border-color: var(--primary-color);
        }

        .contact-icon {
            width: 80px;
            height: 80px;
            background: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 2rem;
        }

        .form-control {
            border-radius: 8px;
            border: 1px solid var(--border-color);
            padding: 12px 16px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
        }

        .btn-primary {
            background: var(--primary-color);
            border-color: var(--primary-color);
            font-weight: 600;
            padding: 12px 24px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: color-mix(in srgb, var(--primary-color) 85%, black);
            border-color: color-mix(in srgb, var(--primary-color) 85%, black);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 3rem;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 4px;
            background: var(--primary-color);
            border-radius: 2px;
        }

        .breadcrumb {
            background: none;
            padding: 0;
        }
    </style>
</head>

<body>
    <!-- Navigation (same as home page) -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm sticky-top">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="/store-frontend.php?store=medical-software-solutions">
                <?php if (!empty($store['logo_url'])): ?>
                    <img src="<?php echo htmlspecialchars($store['logo_url']); ?>" alt="Logo" class="store-logo me-2" style="height: 40px;">
                <?php endif; ?>
                <span class="fw-bold" style="color: var(--primary-color);">
                    <?php echo htmlspecialchars($store['store_name']); ?>
                </span>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <?php 
                    $currentPage = $_GET['page'] ?? 'home';
                    $storeSlug = 'medical-software-solutions';
                    $storeSettings = json_decode($store['settings'] ?? '{}', true);
                    $pageSettings = $storeSettings['pages'] ?? ['home' => true, 'products' => true, 'about' => true, 'contact' => true];
                    ?>
                    
                    <?php if ($pageSettings['home'] !== false): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $currentPage === 'home' ? 'active' : ''; ?>" 
                           href="/store-frontend.php?store=<?php echo $storeSlug; ?>">
                            <i class="fas fa-home me-1"></i>Accueil
                        </a>
                    </li>
                    <?php endif; ?>
                    
                    <?php if ($pageSettings['products'] !== false): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $currentPage === 'products' ? 'active' : ''; ?>" 
                           href="/store-frontend.php?store=<?php echo $storeSlug; ?>&page=products">
                            <i class="fas fa-box me-1"></i>Produits
                        </a>
                    </li>
                    <?php endif; ?>
                    
                    <?php if ($pageSettings['about'] !== false): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $currentPage === 'about' ? 'active' : ''; ?>" 
                           href="/store-frontend.php?store=<?php echo $storeSlug; ?>&page=about">
                            <i class="fas fa-info-circle me-1"></i>À propos
                        </a>
                    </li>
                    <?php endif; ?>
                    
                    <?php if ($pageSettings['contact'] !== false): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $currentPage === 'contact' ? 'active' : ''; ?>" 
                           href="/store-frontend.php?store=<?php echo $storeSlug; ?>&page=contact">
                            <i class="fas fa-envelope me-1"></i>Contact
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="hero-title">Contactez-nous</h1>
                    <p class="lead">Nous sommes là pour répondre à toutes vos questions et vous accompagner</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <div class="container my-5">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/store-frontend.php?store=medical-software-solutions">Accueil</a></li>
                <li class="breadcrumb-item active">Contact</li>
            </ol>
        </nav>

        <!-- Contact Methods -->
        <div class="mb-5">
            <h2 class="section-title">Nos Coordonnées</h2>
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="contact-card">
                        <div class="contact-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <h5 class="fw-bold mb-3">Téléphone</h5>
                        <p class="mb-0"><?php echo htmlspecialchars($store['phone'] ?? '+213 XX XX XX XX'); ?></p>
                        <small class="text-muted">Lun-Ven: 8h-18h</small>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="contact-card">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <h5 class="fw-bold mb-3">Email</h5>
                        <p class="mb-0"><?php echo htmlspecialchars($store['email'] ?? '<EMAIL>'); ?></p>
                        <small class="text-muted">Réponse sous 24h</small>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="contact-card">
                        <div class="contact-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <h5 class="fw-bold mb-3">Adresse</h5>
                        <p class="mb-0"><?php echo htmlspecialchars($store['address'] ?? 'Alger, Algérie'); ?></p>
                        <small class="text-muted">Visite sur RDV</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Form -->
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-5">
                        <h3 class="text-center mb-4">Envoyez-nous un message</h3>
                        <form id="contactForm">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="firstName" class="form-label">Prénom *</label>
                                    <input type="text" class="form-control" id="firstName" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="lastName" class="form-label">Nom *</label>
                                    <input type="text" class="form-control" id="lastName" required>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">Email *</label>
                                    <input type="email" class="form-control" id="email" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">Téléphone</label>
                                    <input type="tel" class="form-control" id="phone">
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="subject" class="form-label">Sujet *</label>
                                <select class="form-control" id="subject" required>
                                    <option value="">Choisissez un sujet</option>
                                    <option value="info">Demande d'information</option>
                                    <option value="quote">Demande de devis</option>
                                    <option value="support">Support technique</option>
                                    <option value="other">Autre</option>
                                </select>
                            </div>
                            <div class="mb-4">
                                <label for="message" class="form-label">Message *</label>
                                <textarea class="form-control" id="message" rows="5" required placeholder="Décrivez votre demande en détail..."></textarea>
                            </div>
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-paper-plane me-2"></i>Envoyer le message
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-light py-5 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5><?php echo htmlspecialchars($store['store_name']); ?></h5>
                    <p><?php echo htmlspecialchars($store['description'] ?? ''); ?></p>
                </div>
                <div class="col-md-4">
                    <h6>Contact</h6>
                    <?php if (!empty($store['phone'])): ?>
                        <p><i class="fas fa-phone me-2"></i><?php echo htmlspecialchars($store['phone']); ?></p>
                    <?php endif; ?>
                    <?php if (!empty($store['email'])): ?>
                        <p><i class="fas fa-envelope me-2"></i><?php echo htmlspecialchars($store['email']); ?></p>
                    <?php endif; ?>
                </div>
                <div class="col-md-4">
                    <h6>Liens rapides</h6>
                    <ul class="list-unstyled">
                        <li><a href="/store-frontend.php?store=medical-software-solutions" class="text-light text-decoration-none">Accueil</a></li>
                        <li><a href="/store-frontend.php?store=medical-software-solutions&page=products" class="text-light text-decoration-none">Produits</a></li>
                        <li><a href="/store-frontend.php?store=medical-software-solutions&page=about" class="text-light text-decoration-none">À propos</a></li>
                        <li><a href="/store-frontend.php?store=medical-software-solutions&page=contact" class="text-light text-decoration-none">Contact</a></li>
                    </ul>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p class="mb-0">&copy; <?php echo date('Y'); ?> <?php echo htmlspecialchars($store['store_name']); ?>. Tous droits réservés.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        document.getElementById('contactForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Show success message
            alert('Merci pour votre message ! Nous vous répondrons dans les plus brefs délais.');
            
            // Reset form
            this.reset();
        });
    </script>
</body>
</html>
