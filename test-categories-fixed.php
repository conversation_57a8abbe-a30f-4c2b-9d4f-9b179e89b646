<?php
require_once 'dashboard/includes/database.php';

try {
    $db = connectDB();
    echo 'Testing categories API logic...' . PHP_EOL;
    
    // Test the logic from getUserCategories function
    $merchantEmail = '<EMAIL>';
    $storeId = 7;
    
    // Get merchant ID from store_id
    $storeQuery = "SELECT merchant_id FROM stores WHERE id = ?";
    $storeStmt = $db->prepare($storeQuery);
    $storeStmt->execute([$storeId]);
    $storeData = $storeStmt->fetch(PDO::FETCH_ASSOC);
    $merchantId = $storeData ? $storeData['merchant_id'] : null;
    
    echo "Store ID: $storeId" . PHP_EOL;
    echo "Merchant ID: $merchantId" . PHP_EOL;
    
    if ($merchantId) {
        // Test the categories query
        $categoriesQuery = "
            SELECT
                c.id,
                COALESCE(c.name_fr, c.name) as name,
                c.slug,
                c.description,
                NULL as parent_id,
                c.status,
                c.created_at,
                COUNT(DISTINCT pc.product_id) as products_count
            FROM categories c
            LEFT JOIN product_categories pc ON c.id = pc.category_id
            WHERE c.merchant_id = ? AND c.status = 'active'
            GROUP BY c.id, c.name, c.name_fr, c.slug, c.description, c.status, c.created_at
            ORDER BY c.name ASC
        ";
        
        $stmt = $db->prepare($categoriesQuery);
        $stmt->execute([$merchantId]);
        $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo 'Found ' . count($categories) . ' categories for merchant ' . $merchantId . ':' . PHP_EOL;
        foreach ($categories as $cat) {
            echo "- ID: {$cat['id']}, Name: {$cat['name']}, Slug: {$cat['slug']}, Products: {$cat['products_count']}" . PHP_EOL;
        }
        
        // If no categories found, check if merchant has any categories at all
        if (empty($categories)) {
            echo PHP_EOL . 'Checking all categories for this merchant:' . PHP_EOL;
            $allCatQuery = "SELECT id, name, status FROM categories WHERE merchant_id = ?";
            $allCatStmt = $db->prepare($allCatQuery);
            $allCatStmt->execute([$merchantId]);
            $allCats = $allCatStmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (empty($allCats)) {
                echo 'No categories found for merchant ' . $merchantId . PHP_EOL;
                echo 'Creating sample categories...' . PHP_EOL;
                
                // Create sample categories
                $sampleCategories = [
                    ['name' => 'Électronique', 'name_fr' => 'Électronique', 'slug' => 'electronique'],
                    ['name' => 'Vêtements', 'name_fr' => 'Vêtements', 'slug' => 'vetements'],
                    ['name' => 'Maison', 'name_fr' => 'Maison', 'slug' => 'maison']
                ];
                
                foreach ($sampleCategories as $cat) {
                    $insertQuery = "INSERT INTO categories (merchant_id, store_id, name, name_fr, slug, status) VALUES (?, ?, ?, ?, ?, 'active')";
                    $insertStmt = $db->prepare($insertQuery);
                    $insertStmt->execute([$merchantId, $storeId, $cat['name'], $cat['name_fr'], $cat['slug']]);
                    echo "Created category: {$cat['name']}" . PHP_EOL;
                }
            } else {
                foreach ($allCats as $cat) {
                    echo "- ID: {$cat['id']}, Name: {$cat['name']}, Status: {$cat['status']}" . PHP_EOL;
                }
            }
        }
    } else {
        echo 'Merchant not found for store ID ' . $storeId . PHP_EOL;
    }
    
} catch (Exception $e) {
    echo 'Error: ' . $e->getMessage() . PHP_EOL;
}
?>
