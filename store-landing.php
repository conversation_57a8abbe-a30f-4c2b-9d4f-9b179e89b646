<?php

/**
 * Landing page pour les stores avec URLs personnalisées
 * Format: /store/{store_name} ou /{subdomain}
 */

require_once 'php/config/database.php';

// Configuration de la base de données
$host = 'localhost';
$port = 3307;
$dbname = 'landingpage_new';
$username = 'root';
$password = '';

try {
    // Connexion à la base de données
    $pdo = new PDO(
        "mysql:host={$host};port={$port};dbname={$dbname};charset=utf8mb4",
        $username,
        $password,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );

    // Récupérer l'URL demandée
    $requestUri = $_SERVER['REQUEST_URI'];
    $path = parse_url($requestUri, PHP_URL_PATH);
    $query = parse_url($requestUri, PHP_URL_QUERY);
    parse_str($query ?? '', $queryParams);

    // Si on a un paramètre store dans l'URL, l'utiliser directement
    if (!empty($queryParams['store'])) {
        $storeIdentifier = $queryParams['store'];
        $identifierType = 'store_name';
    } else {
        $pathParts = explode('/', trim($path, '/'));

        $storeIdentifier = null;
        $landingPageSlug = null;

        // Analyser l'URL pour déterminer le store et la landing page
        if (count($pathParts) >= 2 && $pathParts[0] === 'store') {
            // Format: /store/{store_name}/{landing_page_slug}
            $storeIdentifier = $pathParts[1];
            $landingPageSlug = $pathParts[2] ?? null;
            $identifierType = 'store_name';
        } else if (count($pathParts) >= 1) {
            // Format: /{subdomain}/{landing_page_slug}
            $storeIdentifier = $pathParts[0];
            $landingPageSlug = $pathParts[1] ?? null;
            $identifierType = 'subdomain';
        }

        if (!$storeIdentifier) {
            http_response_code(404);
            echo "Store non trouvé";
            exit;
        }
    } // Closing brace for the else block

    // Récupérer le store
    if ($identifierType === 'store_name') {
        $storeQuery = "SELECT s.*, COUNT(p.id) as product_count
                       FROM stores s
                       LEFT JOIN products p ON s.id = p.store_id
                       WHERE (s.store_name = ? OR s.store_name_en = ? OR s.store_name_ar = ?)
                       AND s.status = 'active'
                       GROUP BY s.id";
        $storeStmt = $pdo->prepare($storeQuery);
        $storeStmt->execute([$storeIdentifier, $storeIdentifier, $storeIdentifier]);
    } else {
        $storeQuery = "SELECT s.*, COUNT(p.id) as product_count
                       FROM stores s
                       LEFT JOIN products p ON s.id = p.store_id
                       WHERE s.subdomain = ? AND s.status = 'active'
                       GROUP BY s.id";
        $storeStmt = $pdo->prepare($storeQuery);
        $storeStmt->execute([$storeIdentifier]);
    }

    $store = $storeStmt->fetch();

    if (!$store) {
        http_response_code(404);
        echo "Store non trouvé";
        exit;
    }

    // Si pas de landing page spécifiée, récupérer la landing page principale du store
    if (!$landingPageSlug) {
        $landingQuery = "SELECT * FROM landing_pages WHERE merchant_id = ? AND status = 'published' ORDER BY created_at DESC LIMIT 1";
        $landingStmt = $pdo->prepare($landingQuery);
        $landingStmt->execute([$store['merchant_id']]);
    } else {
        $landingQuery = "SELECT * FROM landing_pages WHERE merchant_id = ? AND slug = ? AND status = 'published'";
        $landingStmt = $pdo->prepare($landingQuery);
        $landingStmt->execute([$store['merchant_id'], $landingPageSlug]);
    }

    $landingPage = $landingStmt->fetch();

    if (!$landingPage) {
        // Si pas de landing page trouvée, rediriger vers le store frontend
        $storeSlug = $store['slug'] ?? $store['store_name'];
        header("Location: /store-frontend.php?store=" . urlencode($storeSlug));
        exit;
    }

    // Récupérer le template
    $templateQuery = "SELECT * FROM templates WHERE id = ?";
    $templateStmt = $pdo->prepare($templateQuery);
    $templateStmt->execute([$landingPage['template_id']]);
    $template = $templateStmt->fetch();

    // Générer la page
    $pageContent = renderLandingPage($landingPage, $template, $store);
    echo $pageContent;
} catch (Exception $e) {
    http_response_code(500);
    echo "Erreur: " . $e->getMessage();
}

/**
 * Génère une landing page par défaut pour un store
 */
function generateDefaultLandingPage($store)
{
    global $pdo;

    // Apply store customization settings
    $showStoreName = $store['show_store_name'] ?? true;
    $showDescription = $store['show_description'] ?? true;
    $showCategories = $store['show_categories'] ?? true;
    $themeColors = json_decode($store['theme_colors'] ?? '{}', true);

    $primaryColor = $themeColors['primary'] ?? '#667eea';
    $secondaryColor = $themeColors['secondary'] ?? '#764ba2';

    $storeName = $showStoreName ? ($store['store_name'] ?? $store['store_name_en'] ?? $store['store_name_ar'] ?? 'Mon Store') : '';
    $description = $showDescription ? ($store['description'] ?? $store['description_en'] ?? $store['description_ar'] ?? 'Bienvenue dans notre store') : '';
    $productCount = $store['product_count'] ?? 0;

    // Récupérer les catégories du store
    $categoriesHtml = '';
    $categoriesQuery = "SELECT * FROM categories WHERE store_id = ? AND status = 'active' ORDER BY name";
    $categoriesStmt = $pdo->prepare($categoriesQuery);
    $categoriesStmt->execute([$store['id']]);
    $categories = $categoriesStmt->fetchAll();

    if ($categories) {
        $categoriesHtml = '<div class="categories-filter">
            <button class="category-btn active" data-category="all">Tous les produits</button>';

        foreach ($categories as $category) {
            $categoriesHtml .= "<button class='category-btn' data-category='{$category['slug']}'>{$category['name']}</button>";
        }

        $categoriesHtml .= '</div>';
    }

    // Récupérer tous les produits du store avec leurs catégories
    $productsHtml = '';
    if ($productCount > 0) {
        $productsQuery = "
            SELECT p.*, GROUP_CONCAT(c.slug) as category_slugs, GROUP_CONCAT(c.name) as category_names
            FROM products p
            LEFT JOIN product_categories pc ON p.id = pc.product_id
            LEFT JOIN categories c ON pc.category_id = c.id
            WHERE p.store_id = ? AND p.status = 'active'
            GROUP BY p.id
            ORDER BY p.created_at DESC
        ";
        $productsStmt = $pdo->prepare($productsQuery);
        $productsStmt->execute([$store['id']]);
        $products = $productsStmt->fetchAll();

        if ($products) {
            $productsHtml = '<div class="products-section">
                <div class="products-grid" id="products-grid">';

            foreach ($products as $product) {
                $productName = $product['name'] ?? 'Produit';
                $productPrice = $product['price'] ?? 0;
                $productDescription = $product['description'] ?? $product['short_description'] ?? '';

                // Gérer les images (JSON ou URL simple)
                $productImage = 'https://via.placeholder.com/300x300?text=Produit';
                if (!empty($product['images'])) {
                    $images = json_decode($product['images'], true);
                    if (is_array($images) && !empty($images)) {
                        $productImage = $images[0];
                    } elseif (is_string($product['images'])) {
                        $productImage = $product['images'];
                    }
                } elseif (!empty($product['image_url'])) {
                    $productImage = $product['image_url'];
                }

                $categoryClasses = $product['category_slugs'] ? str_replace(',', ' ', $product['category_slugs']) : 'uncategorized';

                $productsHtml .= "
                    <div class='product-card' data-categories='{$categoryClasses}'>
                        <div class='product-image'>
                            <img src='{$productImage}' alt='{$productName}' onerror='this.src=\"https://via.placeholder.com/300x300?text=Produit\"'>
                        </div>
                        <div class='product-info'>
                            <h3>{$productName}</h3>
                            <p class='product-description'>" . substr($productDescription, 0, 100) . (strlen($productDescription) > 100 ? '...' : '') . "</p>
                            <p class='price'>" . number_format($productPrice, 0, ',', ' ') . " DA</p>
                            <button class='btn-contact'>Contacter</button>
                        </div>
                    </div>";
            }

            $productsHtml .= '</div></div>';
        }
    }

    return "<!DOCTYPE html>
<html lang='fr'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>{$storeName}</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f8f9fa; }

        .header { background: linear-gradient(135deg, {$primaryColor} 0%, {$secondaryColor} 100%); color: white; padding: 60px 0; text-align: center; }
        .header h1 { font-size: 3rem; margin-bottom: 10px; font-weight: 300; }
        .header p { font-size: 1.2rem; opacity: 0.9; max-width: 600px; margin: 0 auto; }

        .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }

        .stats-bar { background: white; padding: 20px 0; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .stats { display: flex; justify-content: center; align-items: center; gap: 40px; }
        .stat-item { text-align: center; }
        .stat-number { font-size: 2rem; font-weight: bold; color: {$primaryColor}; }
        .stat-label { color: #666; font-size: 0.9rem; }

        .main-content { padding: 40px 0; }

        .categories-filter { display: flex; flex-wrap: wrap; justify-content: center; gap: 10px; margin-bottom: 40px; }
        .category-btn {
            background: white; border: 2px solid #e9ecef; color: #495057; padding: 12px 24px;
            border-radius: 25px; cursor: pointer; transition: all 0.3s ease; font-weight: 500;
        }
        .category-btn:hover, .category-btn.active {
            background: {$primaryColor}; color: white; border-color: {$primaryColor}; transform: translateY(-2px);
        }

        .products-grid {
            display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px; margin-top: 30px;
        }
        .product-card {
            background: white; border-radius: 15px; overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08); transition: all 0.3s ease;
            opacity: 1; transform: scale(1);
        }
        .product-card:hover { transform: translateY(-10px); box-shadow: 0 15px 35px rgba(0,0,0,0.15); }
        .product-card.hidden { opacity: 0; transform: scale(0.8); pointer-events: none; }

        .product-image { position: relative; overflow: hidden; height: 250px; }
        .product-image img { width: 100%; height: 100%; object-fit: cover; transition: transform 0.3s ease; }
        .product-card:hover .product-image img { transform: scale(1.1); }

        .product-info { padding: 25px; }
        .product-info h3 { font-size: 1.3rem; margin-bottom: 10px; color: #333; font-weight: 600; }
        .product-description { color: #666; line-height: 1.5; margin-bottom: 15px; font-size: 0.9rem; }
        .price { font-size: 1.5rem; font-weight: bold; color: {$primaryColor}; margin-bottom: 15px; }
        .btn-contact {
            background: linear-gradient(135deg, {$primaryColor} 0%, {$secondaryColor} 100%); color: white;
            border: none; padding: 12px 30px; border-radius: 25px; cursor: pointer;
            font-weight: 600; transition: all 0.3s ease; width: 100%;
        }
        .btn-contact:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4); }

        .cta-section { text-align: center; padding: 60px 0; background: white; margin-top: 40px; }
        .cta {
            background: linear-gradient(135deg, {$primaryColor} 0%, {$secondaryColor} 100%); color: white;
            padding: 18px 40px; border: none; border-radius: 30px; font-size: 1.1rem;
            cursor: pointer; font-weight: 600; transition: all 0.3s ease;
        }
        .cta:hover { transform: translateY(-3px); box-shadow: 0 10px 25px {$primaryColor}66; }

        @media (max-width: 768px) {
            .header h1 { font-size: 2rem; }
            .stats { flex-direction: column; gap: 20px; }
            .categories-filter { justify-content: flex-start; }
            .products-grid { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; }
        }
    </style>
</head>
<body>
    " . ($showStoreName || $showDescription ? "<div class='header'>
        <div class='container'>
            " . ($showStoreName ? "<h1>{$storeName}</h1>" : "") . "
            " . ($showDescription ? "<p>{$description}</p>" : "") . "
        </div>
    </div>" : "") . "

    <div class='stats-bar'>
        <div class='container'>
            <div class='stats'>
                <div class='stat-item'>
                    <div class='stat-number'>{$productCount}</div>
                    <div class='stat-label'>Produits disponibles</div>
                </div>
                <div class='stat-item'>
                    <div class='stat-number'>24/7</div>
                    <div class='stat-label'>Support client</div>
                </div>
                <div class='stat-item'>
                    <div class='stat-number'>✓</div>
                    <div class='stat-label'>Livraison rapide</div>
                </div>
            </div>
        </div>
    </div>

    <div class='main-content'>
        <div class='container'>
            " . ($showCategories ? $categoriesHtml : "") . "
            {$productsHtml}
        </div>
    </div>

    <div class='cta-section'>
        <div class='container'>
            <h2>Intéressé par nos produits ?</h2>
            <p>Contactez-nous pour plus d'informations et pour passer commande</p>
            <button class='cta' onclick='alert(\"Contactez-nous au: +213 XXX XXX XXX\")'>Nous contacter</button>
        </div>
    </div>

    <script>
        // Filtrage par catégories
        document.addEventListener('DOMContentLoaded', function() {
            const categoryBtns = document.querySelectorAll('.category-btn');
            const productCards = document.querySelectorAll('.product-card');

            categoryBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // Retirer la classe active de tous les boutons
                    categoryBtns.forEach(b => b.classList.remove('active'));
                    // Ajouter la classe active au bouton cliqué
                    this.classList.add('active');

                    const selectedCategory = this.getAttribute('data-category');

                    productCards.forEach(card => {
                        const cardCategories = card.getAttribute('data-categories');

                        if (selectedCategory === 'all' || cardCategories.includes(selectedCategory)) {
                            card.classList.remove('hidden');
                        } else {
                            card.classList.add('hidden');
                        }
                    });
                });
            });
        });
    </script>
</body>
</html>";
}

/**
 * Rend une landing page avec son template
 */
function renderLandingPage($landingPage, $template, $store)
{
    $content = $landingPage['content'];

    // Remplacer les variables du store dans le contenu
    $storeName = $store['store_name'] ?? $store['store_name_en'] ?? $store['store_name_ar'] ?? 'Mon Store';
    $content = str_replace('{{store_name}}', $storeName, $content);
    $content = str_replace('{{store_description}}', $store['description'] ?? '', $content);

    if ($template) {
        // Si un template existe, l'utiliser
        $templateContent = $template['content'];
        $content = str_replace('{{content}}', $content, $templateContent);
    }

    return $content;
}
