<?php
// Load orders data server-side for reliable display
require_once __DIR__ . '/../includes/database.php';

$ordersData = [];
$ordersStats = [
    'total_orders' => 0,
    'pending_orders' => 0,
    'completed_orders' => 0,
    'total_revenue' => 0
];

try {
    $db = connectDB();
    $storeId = $_GET['store_id'] ?? 7;
    
    // Get merchant_id for this store
    $merchantQuery = "SELECT merchant_id FROM stores WHERE id = ?";
    $merchantStmt = $db->prepare($merchantQuery);
    $merchantStmt->execute([$storeId]);
    $storeData = $merchantStmt->fetch(PDO::FETCH_ASSOC);
    $merchantId = $storeData['merchant_id'] ?? 3;
    
    // Get orders for this merchant
    $ordersQuery = "
        SELECT 
            o.*,
            u.name as customer_name,
            u.email as customer_email,
            COUNT(oi.id) as items_count,
            SUM(oi.quantity * oi.price) as order_total
        FROM orders o
        LEFT JOIN users u ON o.customer_id = u.id
        LEFT JOIN order_items oi ON o.id = oi.order_id
        WHERE o.merchant_id = ?
        GROUP BY o.id
        ORDER BY o.created_at DESC
    ";
    
    $stmt = $db->prepare($ordersQuery);
    $stmt->execute([$merchantId]);
    $ordersData = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Calculate statistics
    foreach ($ordersData as $order) {
        $ordersStats['total_orders']++;
        if ($order['status'] === 'pending') {
            $ordersStats['pending_orders']++;
        } elseif ($order['status'] === 'completed') {
            $ordersStats['completed_orders']++;
        }
        $ordersStats['total_revenue'] += floatval($order['order_total']);
    }
    
} catch (Exception $e) {
    error_log("Orders page error: " . $e->getMessage());
}
?>

<div class="orders-section">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3><i class="fas fa-shopping-cart me-2"></i>Gestion des Commandes</h3>
        <div class="btn-group">
            <button class="btn btn-outline-primary" onclick="exportOrders()">
                <i class="fas fa-download me-2"></i>Exporter
            </button>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createOrderModal">
                <i class="fas fa-plus me-2"></i>Nouvelle commande
            </button>
        </div>
    </div>

    <!-- Statistiques rapides -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-primary">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="stat-content">
                    <h4>Total Commandes</h4>
                    <p><?php echo $ordersStats['total_orders']; ?></p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-warning">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                    <h4>En attente</h4>
                    <p><?php echo $ordersStats['pending_orders']; ?></p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-content">
                    <h4>Complétées</h4>
                    <p><?php echo $ordersStats['completed_orders']; ?></p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon bg-info">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="stat-content">
                    <h4>Chiffre d'affaires</h4>
                    <p><?php echo number_format($ordersStats['total_revenue'], 2); ?> DZD</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtres -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="input-group">
                <span class="input-group-text"><i class="fas fa-search"></i></span>
                <input type="text" class="form-control" placeholder="Rechercher une commande..." id="searchOrders">
            </div>
        </div>
        <div class="col-md-2">
            <select class="form-select" id="filterStatus">
                <option value="">Tous statuts</option>
                <option value="pending">En attente</option>
                <option value="processing">En cours</option>
                <option value="completed">Complétée</option>
                <option value="cancelled">Annulée</option>
            </select>
        </div>
        <div class="col-md-2">
            <select class="form-select" id="sortBy">
                <option value="date_desc">Plus récentes</option>
                <option value="date_asc">Plus anciennes</option>
                <option value="amount_desc">Montant décroissant</option>
                <option value="amount_asc">Montant croissant</option>
            </select>
        </div>
    </div>

    <!-- Liste des commandes -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">Mes Commandes</h5>
        </div>
        <div class="card-body">
            <?php if (!empty($ordersData)): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>N° Commande</th>
                                <th>Client</th>
                                <th>Date</th>
                                <th>Articles</th>
                                <th>Montant</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($ordersData as $order): ?>
                                <tr>
                                    <td>
                                        <strong>#<?php echo htmlspecialchars($order['id']); ?></strong>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($order['customer_name'] ?? 'Client inconnu'); ?></strong>
                                            <?php if (!empty($order['customer_email'])): ?>
                                                <br><small class="text-muted"><?php echo htmlspecialchars($order['customer_email']); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <small><?php echo date('d/m/Y H:i', strtotime($order['created_at'])); ?></small>
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark"><?php echo $order['items_count']; ?> articles</span>
                                    </td>
                                    <td>
                                        <strong class="text-primary"><?php echo number_format($order['order_total'], 2); ?> DZD</strong>
                                    </td>
                                    <td>
                                        <?php
                                        $statusClass = match($order['status']) {
                                            'pending' => 'warning',
                                            'processing' => 'info',
                                            'completed' => 'success',
                                            'cancelled' => 'danger',
                                            default => 'secondary'
                                        };
                                        $statusText = match($order['status']) {
                                            'pending' => 'En attente',
                                            'processing' => 'En cours',
                                            'completed' => 'Complétée',
                                            'cancelled' => 'Annulée',
                                            default => 'Inconnu'
                                        };
                                        ?>
                                        <span class="badge bg-<?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" onclick="viewOrder(<?php echo $order['id']; ?>)" title="Voir détails">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-success" onclick="updateOrderStatus(<?php echo $order['id']; ?>)" title="Modifier statut">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-info" onclick="printOrder(<?php echo $order['id']; ?>)" title="Imprimer">
                                                <i class="fas fa-print"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Aucune commande pour le moment</h5>
                    <p class="text-muted">Les commandes de vos clients apparaîtront ici.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
    .stat-card {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        height: 100%;
        display: flex;
        align-items: center;
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        color: white;
        font-size: 1.5rem;
    }

    .stat-content h4 {
        font-size: 0.9rem;
        color: #6c757d;
        margin-bottom: 0.5rem;
        font-weight: 600;
    }

    .stat-content p {
        font-size: 2rem;
        font-weight: bold;
        margin: 0;
        color: #495057;
    }
</style>
