<?php
require_once __DIR__ . '/includes/database.php';
require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/includes/translation.php';
require_once __DIR__ . '/includes/header.php';

// Vérification de l'authentification
if (!isAuthenticated()) {
    header('Location: /login.html');
    exit;
}

// Récupération des données de l'utilisateur
$user = getCurrentUser();
?>

<!DOCTYPE html>
<?php
$userLang = $user['language'] ?? 'fr';
$isRTL = $userLang === 'ar';
?>
<html lang="<?php echo htmlspecialchars($userLang); ?>" dir="<?php echo $isRTL ? 'rtl' : 'ltr'; ?>">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - صفحات هبوط للجميع</title>

    <!-- CSS -->
    <link rel="stylesheet" href="/dashboard/assets/css/main.css">
    <link rel="stylesheet" href="/dashboard/assets/css/components.css">
    <link rel="stylesheet" href="/dashboard/assets/css/responsive.css">

    <!-- Bibliothèques externes -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>

<body class="dashboard-body">
    <!-- Overlay de chargement -->
    <?php include __DIR__ . '/components/loading-overlay.php'; ?>

    <div class="dashboard-container">
        <!-- Sidebar -->
        <?php include __DIR__ . '/components/sidebar.php'; ?>

        <!-- Contenu principal -->
        <main class="main-content">
            <!-- Topbar -->
            <?php include __DIR__ . '/components/topbar.php'; ?>

            <!-- Contenu dynamique -->
            <div class="content-wrapper" id="mainContent">
                <?php
                // Déterminer quelle vue afficher selon le rôle
                $currentPage = $_GET['page'] ?? null;
                $userRole = getUserRole();
                $currentUser = getCurrentUser();

                // Debug information (visible in development)
                if (isset($_GET['debug'])) {
                    echo "<div class='alert alert-info'>";
                    echo "<h5>🔧 Debug Information</h5>";
                    echo "<p><strong>User Role:</strong> $userRole</p>";
                    echo "<p><strong>Current Page:</strong> " . ($currentPage ?? 'null') . "</p>";
                    echo "<p><strong>GET Parameters:</strong> " . json_encode($_GET) . "</p>";
                    echo "<p><strong>User Data:</strong> " . json_encode($currentUser) . "</p>";
                    echo "</div>";
                }

                // Si aucune page spécifiée, rediriger vers le dashboard approprié
                if (!$currentPage) {
                    switch ($userRole) {
                        case 'admin':
                            $currentPage = 'overview'; // Admin overview
                            break;
                        case 'merchant':
                            $currentPage = 'merchant-dashboard';
                            break;
                        case 'customer':
                            $currentPage = 'customer-dashboard';
                            break;
                        default:
                            $currentPage = 'overview';
                    }

                    if (isset($_GET['debug'])) {
                        echo "<div class='alert alert-success'>Auto-selected page: <strong>$currentPage</strong> for role: <strong>$userRole</strong></div>";
                    }
                }

                // Pages autorisées selon le rôle
                $allowedPages = [
                    'admin' => [
                        'overview',
                        'pages',
                        'store-pages',
                        'products',
                        'categories',
                        'analytics',
                        'settings',
                        'users',
                        'stores',
                        'subscriptions',
                        'roles',
                        'ai-config',
                        'smtp-config',
                        'email-send',
                        'messages',
                        'contact',
                        'landing-page-builder',
                        'page-builder',
                        'merchant-dashboard',
                        'customer-dashboard',
                        'shipping-zones',
                        'google-sheets-integration',
                        'ratings',
                        'store-customization'
                    ],
                    'merchant' => [
                        'merchant-dashboard',
                        'store-pages',
                        'pages',
                        'products',
                        'categories',
                        'orders',
                        'analytics',
                        'settings',
                        'landing-page-builder',
                        'page-builder',
                        'messages',
                        'merchant-messages',
                        'contact',
                        'shipping-zones',
                        'google-sheets-integration',
                        'ratings',
                        'store-customization'
                    ],
                    'customer' => [
                        'customer-dashboard',
                        'orders',
                        'profile',
                        'contact',
                        'messages',
                        'ratings'
                    ]
                ];

                $userAllowedPages = $allowedPages[$userRole] ?? $allowedPages['customer'];

                // Special handling for merchants accessing overview page
                if ($currentPage === 'overview' && $userRole === 'merchant') {
                    // Redirect merchants from overview to their dashboard
                    $currentPage = 'merchant-dashboard';
                }

                // Vérifier si la page est autorisée pour ce rôle
                if (in_array($currentPage, $userAllowedPages)) {
                    // Special handling for orders page - merchants get merchant-orders-page
                    if ($currentPage === 'orders' && $userRole === 'merchant') {
                        $viewFile = __DIR__ . '/views/merchant-orders-page.php';
                    } else {
                        $viewFile = __DIR__ . '/views/' . $currentPage . '.php';
                    }

                    if (file_exists($viewFile)) {
                        include $viewFile;
                    } else {
                        // Page par défaut si le fichier n'existe pas
                        $defaultPage = $userRole === 'admin' ? 'overview' : ($userRole === 'merchant' ? 'merchant-dashboard' : 'customer-dashboard');
                        include __DIR__ . '/views/' . $defaultPage . '.php';
                    }
                } else {
                    // Rediriger vers la page par défaut du rôle
                    $defaultPage = $userRole === 'admin' ? 'overview' : ($userRole === 'merchant' ? 'merchant-dashboard' : 'customer-dashboard');
                    include __DIR__ . '/views/' . $defaultPage . '.php';
                }
                ?>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="/js/error-suppression.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="/dashboard/assets/js/image-handler.js"></script>
    <script src="/dashboard/assets/js/language-switcher.js"></script>
    <script src="/dashboard/assets/js/diagnostic.js"></script>
    <script type="module" src="/dashboard/assets/js/main.js"></script>
</body>

</html>
