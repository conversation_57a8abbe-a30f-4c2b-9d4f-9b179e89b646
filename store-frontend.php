<?php

/**
 * Professional Merchant Store Frontend
 * Public-facing store pages with full e-commerce functionality
 */

require_once 'dashboard/includes/database.php';

/**
 * Helper function to get store products with filters
 */
function getStoreProducts($storeId, $filters = [])
{
    global $db;

    $where = ["p.store_id = ?"];
    $params = [$storeId];

    // Category filter
    if (!empty($filters['category_id'])) {
        $where[] = "pc.category_id = ?";
        $params[] = $filters['category_id'];
    }

    // Search filter
    if (!empty($filters['search'])) {
        $where[] = "(p.name LIKE ? OR p.description LIKE ?)";
        $params[] = '%' . $filters['search'] . '%';
        $params[] = '%' . $filters['search'] . '%';
    }

    // Price range filter
    if (!empty($filters['min_price'])) {
        $where[] = "p.price >= ?";
        $params[] = $filters['min_price'];
    }

    if (!empty($filters['max_price'])) {
        $where[] = "p.price <= ?";
        $params[] = $filters['max_price'];
    }

    // Build query (removed product_reviews table that doesn't exist)
    $query = "
        SELECT DISTINCT p.*,
               GROUP_CONCAT(DISTINCT c.name) as categories
        FROM products p
        LEFT JOIN product_categories pc ON p.id = pc.product_id
        LEFT JOIN categories c ON pc.category_id = c.id
        WHERE " . implode(' AND ', $where) . " AND p.status = 'active'
        GROUP BY p.id
    ";

    // Add sorting
    $sortBy = $filters['sort'] ?? 'newest';
    switch ($sortBy) {
        case 'name':
            $query .= " ORDER BY p.name ASC";
            break;
        case 'price_asc':
            $query .= " ORDER BY p.price ASC";
            break;
        case 'price_desc':
            $query .= " ORDER BY p.price DESC";
            break;
        case 'popular':
            $query .= " ORDER BY p.created_at DESC"; // Fallback since review_count doesn't exist
            break;
        case 'newest':
        default:
            $query .= " ORDER BY p.created_at DESC";
            break;
    }

    // Add pagination
    $page = (int)($filters['page'] ?? 1);
    $perPage = (int)($filters['per_page'] ?? 24);
    $offset = ($page - 1) * $perPage;
    $query .= " LIMIT ? OFFSET ?";
    $params[] = $perPage;
    $params[] = $offset;

    $stmt = $db->prepare($query);
    $stmt->execute($params);

    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Get store identifier from URL
$requestUri = $_SERVER['REQUEST_URI'] ?? '';
$path = parse_url($requestUri, PHP_URL_PATH) ?? '';
$query = parse_url($requestUri, PHP_URL_QUERY) ?? '';
parse_str($query, $queryParams);

$storeIdentifier = null;
$page = 'home'; // Default page

// Check if store parameter is in query string
if (!empty($queryParams['store'])) {
    $storeIdentifier = $queryParams['store'];
    $page = $queryParams['page'] ?? 'home';
} else {
    // Parse URL path
    $pathParts = explode('/', trim($path, '/'));

    // Handle different URL formats
    if (count($pathParts) >= 2 && $pathParts[0] === 'store') {
        // Format: /store/{store_name}/{page}
        $storeIdentifier = $pathParts[1];
        $page = $pathParts[2] ?? 'home';
    } else if (count($pathParts) >= 1 && !empty($pathParts[0]) && $pathParts[0] !== 'store-frontend.php') {
        // Direct store access: /{store_name}
        $storeIdentifier = $pathParts[0];
        $page = $pathParts[1] ?? 'home';
    }
}

if (!$storeIdentifier) {
    http_response_code(404);
    echo "Store not found";
    exit;
}

try {
    $db = connectDB();

    // Get store information
    // Mapping des slugs vers les stores
    $storeId = null;
    if ($storeIdentifier === 'medical-software-solutions') {
        $storeId = 7; // Store <NAME_EMAIL>
    } else {
        // Essayer de trouver par nom de store
        $findQuery = "SELECT id FROM stores WHERE store_name = ? AND status = 'active'";
        $findStmt = $db->prepare($findQuery);
        $findStmt->execute([$storeIdentifier]);
        $result = $findStmt->fetch(PDO::FETCH_ASSOC);
        $storeId = $result ? $result['id'] : null;
    }

    if (!$storeId) {
        http_response_code(404);
        echo "Store not found: " . htmlspecialchars($storeIdentifier);
        exit;
    }

    $storeQuery = "
        SELECT s.*,
               COUNT(DISTINCT p.id) as product_count,
               COUNT(DISTINCT c.id) as category_count,
               AVG(sr.rating) as average_rating,
               COUNT(DISTINCT sr.id) as total_ratings
        FROM stores s
        LEFT JOIN products p ON s.id = p.store_id AND p.status = 'active'
        LEFT JOIN categories c ON s.id = c.store_id AND c.status = 'active'
        LEFT JOIN seller_ratings sr ON s.merchant_id = sr.seller_id
        WHERE s.id = ? AND s.status = 'active'
        GROUP BY s.id
    ";

    $stmt = $db->prepare($storeQuery);
    $stmt->execute([$storeId]);
    $store = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$store) {
        http_response_code(404);
        include 'templates/store/404.php';
        exit;
    }

    // Get store customization settings (skip if table doesn't exist)
    $customization = null;
    try {
        $customizationQuery = "SELECT * FROM store_customization WHERE store_id = ?";
        $customStmt = $db->prepare($customizationQuery);
        $customStmt->execute([$store['id']]);
        $customization = $customStmt->fetch(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        // Table doesn't exist, use defaults
        $customization = null;
    }

    // Default customization if none exists
    if (!$customization) {
        $customization = [
            'primary_color' => '#007bff',
            'secondary_color' => '#6c757d',
            'font_family' => 'system',
            'layout_style' => 'grid',
            'show_store_name' => 1,
            'show_store_description' => 1,
            'show_search_bar' => 1,
            'show_category_filter' => 1,
            'category_position' => 'sidebar',
            'products_per_page' => 24,
            'products_per_row' => 3
        ];
    }

    // Initialize products array
    $products = [];

    // Load products for products page
    if ($page === 'products') {
        // Simple products query for now
        $productsQuery = "SELECT * FROM products WHERE store_id = ? AND status = 'active' ORDER BY created_at DESC LIMIT 24";
        $productsStmt = $db->prepare($productsQuery);
        $productsStmt->execute([$store['id']]);
        $products = $productsStmt->fetchAll(PDO::FETCH_ASSOC);

        // Debug: log the products count
        error_log("Products loaded for store {$store['id']}: " . count($products));
        // Add debug output to HTML
        echo "<!-- DEBUG: Products loaded: " . count($products) . " -->";
    }

    // Route to appropriate page
    switch ($page) {
        case 'home':
        case '':
            include 'templates/store/home.php';
            break;
        case 'products':
            include 'templates/store/products.php';
            break;
        case 'product':
            include 'templates/store/product-detail.php';
            break;
        case 'category':
            include 'templates/store/category.php';
            break;
        case 'search':
            include 'templates/store/search.php';
            break;
        case 'contact':
            include 'templates/store/contact.php';
            break;
        case 'about':
            include 'templates/store/about.php';
            break;
        default:
            http_response_code(404);
            include 'templates/store/404.php';
            break;
    }
} catch (Exception $e) {
    error_log("Store Frontend Error: " . $e->getMessage());
    http_response_code(500);
    echo "Une erreur est survenue. Veuillez réessayer plus tard.";
}



/**
 * Helper function to get store categories
 */
function getStoreCategories($storeId)
{
    global $db;

    // Get merchant_id for this store
    $merchantQuery = "SELECT merchant_id FROM stores WHERE id = ?";
    $merchantStmt = $db->prepare($merchantQuery);
    $merchantStmt->execute([$storeId]);
    $storeData = $merchantStmt->fetch(PDO::FETCH_ASSOC);

    if (!$storeData) {
        return [];
    }

    $merchantId = $storeData['merchant_id'];

    $query = "
        SELECT c.*, COUNT(DISTINCT pc.product_id) as product_count
        FROM categories c
        LEFT JOIN product_categories pc ON c.id = pc.category_id
        LEFT JOIN products p ON pc.product_id = p.id AND p.merchant_id = ? AND p.status = 'active'
        WHERE c.merchant_id = ? AND c.status = 'active'
        GROUP BY c.id
        HAVING product_count > 0
        ORDER BY c.name ASC
    ";

    $stmt = $db->prepare($query);
    $stmt->execute([$merchantId, $merchantId]);

    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Helper function to format price
 */
function formatPrice($price, $currency = 'DZD')
{
    return number_format($price, 2, ',', ' ') . ' ' . $currency;
}

/**
 * Helper function to generate star rating HTML
 */
function generateStarRating($rating, $maxStars = 5)
{
    $html = '<div class="star-rating">';

    for ($i = 1; $i <= $maxStars; $i++) {
        if ($i <= $rating) {
            $html .= '<i class="fas fa-star text-warning"></i>';
        } elseif ($i - 0.5 <= $rating) {
            $html .= '<i class="fas fa-star-half-alt text-warning"></i>';
        } else {
            $html .= '<i class="far fa-star text-muted"></i>';
        }
    }

    $html .= '</div>';
    return $html;
}

/**
 * Helper function to truncate text
 */
function truncateText($text, $length = 100)
{
    if (strlen($text) <= $length) {
        return $text;
    }

    return substr($text, 0, $length) . '...';
}

/**
 * Helper function to get breadcrumbs
 */
function getBreadcrumbs($store, $page, $additional = [])
{
    $breadcrumbs = [
        ['name' => 'Accueil', 'url' => '/'],
        ['name' => $store['store_name'], 'url' => '/store/' . $store['slug']]
    ];

    switch ($page) {
        case 'products':
            $breadcrumbs[] = ['name' => 'Produits', 'url' => null];
            break;
        case 'category':
            $breadcrumbs[] = ['name' => 'Catégories', 'url' => '/store/' . $store['slug'] . '/products'];
            if (!empty($additional['category_name'])) {
                $breadcrumbs[] = ['name' => $additional['category_name'], 'url' => null];
            }
            break;
        case 'product':
            $breadcrumbs[] = ['name' => 'Produits', 'url' => '/store/' . $store['slug'] . '/products'];
            if (!empty($additional['product_name'])) {
                $breadcrumbs[] = ['name' => $additional['product_name'], 'url' => null];
            }
            break;
        case 'contact':
            $breadcrumbs[] = ['name' => 'Contact', 'url' => null];
            break;
        case 'about':
            $breadcrumbs[] = ['name' => 'À propos', 'url' => null];
            break;
    }

    return $breadcrumbs;
}
