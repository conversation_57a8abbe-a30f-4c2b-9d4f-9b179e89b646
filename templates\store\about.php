<!DOCTYPE html>
<html lang="fr" dir="ltr">
<?php
// Définir le slug pour ce store (medical-software-solutions)
$store['slug'] = 'medical-software-solutions';
?>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>À propos - <?php echo htmlspecialchars($store['store_name']); ?></title>
    <meta name="description" content="Découvrez <?php echo htmlspecialchars($store['store_name']); ?> - <?php echo htmlspecialchars($store['description'] ?? ''); ?>">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Custom Store Styles -->
    <style>
        :root {
            --primary-color: <?php echo $customization['primary_color'] ?? '#2c5aa0'; ?>;
            --secondary-color: <?php echo $customization['secondary_color'] ?? '#6c757d'; ?>;
            --text-dark: #2d3748;
            --text-light: #718096;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        body {
            font-family: system-ui, -apple-system, sans-serif;
            color: var(--text-dark);
            line-height: 1.6;
        }

        .navbar {
            box-shadow: var(--shadow-sm);
            border-bottom: 1px solid var(--border-color);
        }

        .nav-link {
            font-weight: 500;
            transition: all 0.3s ease;
            color: var(--text-dark) !important;
        }

        .nav-link:hover {
            color: var(--primary-color) !important;
        }

        .nav-link.active {
            color: var(--primary-color) !important;
        }

        .hero-section {
            background: linear-gradient(135deg, var(--primary-color), color-mix(in srgb, var(--primary-color) 80%, white));
            color: white;
            padding: 100px 0;
        }

        .hero-title {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 1rem;
        }

        .feature-card {
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            height: 100%;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-md);
            border-color: var(--primary-color);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 2rem;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 3rem;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 4px;
            background: var(--primary-color);
            border-radius: 2px;
        }

        .stats-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
        }

        .stats-number {
            font-size: 3rem;
            font-weight: 800;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .breadcrumb {
            background: none;
            padding: 0;
        }
    </style>
</head>

<body>
    <!-- Navigation (same as home page) -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm sticky-top">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="/store-frontend.php?store=medical-software-solutions">
                <?php if (!empty($store['logo_url'])): ?>
                    <img src="<?php echo htmlspecialchars($store['logo_url']); ?>" alt="Logo" class="store-logo me-2" style="height: 40px;">
                <?php endif; ?>
                <span class="fw-bold" style="color: var(--primary-color);">
                    <?php echo htmlspecialchars($store['store_name']); ?>
                </span>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <?php 
                    $currentPage = $_GET['page'] ?? 'home';
                    $storeSlug = 'medical-software-solutions';
                    $storeSettings = json_decode($store['settings'] ?? '{}', true);
                    $pageSettings = $storeSettings['pages'] ?? ['home' => true, 'products' => true, 'about' => true, 'contact' => true];
                    ?>
                    
                    <?php if ($pageSettings['home'] !== false): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $currentPage === 'home' ? 'active' : ''; ?>" 
                           href="/store-frontend.php?store=<?php echo $storeSlug; ?>">
                            <i class="fas fa-home me-1"></i>Accueil
                        </a>
                    </li>
                    <?php endif; ?>
                    
                    <?php if ($pageSettings['products'] !== false): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $currentPage === 'products' ? 'active' : ''; ?>" 
                           href="/store-frontend.php?store=<?php echo $storeSlug; ?>&page=products">
                            <i class="fas fa-box me-1"></i>Produits
                        </a>
                    </li>
                    <?php endif; ?>
                    
                    <?php if ($pageSettings['about'] !== false): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $currentPage === 'about' ? 'active' : ''; ?>" 
                           href="/store-frontend.php?store=<?php echo $storeSlug; ?>&page=about">
                            <i class="fas fa-info-circle me-1"></i>À propos
                        </a>
                    </li>
                    <?php endif; ?>
                    
                    <?php if ($pageSettings['contact'] !== false): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $currentPage === 'contact' ? 'active' : ''; ?>" 
                           href="/store-frontend.php?store=<?php echo $storeSlug; ?>&page=contact">
                            <i class="fas fa-envelope me-1"></i>Contact
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="hero-title">À propos de nous</h1>
                    <p class="lead">Découvrez notre histoire, notre mission et notre engagement envers l'excellence</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <div class="container my-5">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/store-frontend.php?store=medical-software-solutions">Accueil</a></li>
                <li class="breadcrumb-item active">À propos</li>
            </ol>
        </nav>

        <!-- About Content -->
        <div class="row mb-5">
            <div class="col-lg-8 mx-auto">
                <div class="text-center mb-5">
                    <h2 class="section-title">Notre Histoire</h2>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <h4 class="fw-bold mb-3">Notre Mission</h4>
                        <p>Chez <?php echo htmlspecialchars($store['store_name']); ?>, nous nous engageons à fournir des solutions logicielles médicales de haute qualité qui transforment la façon dont les professionnels de santé travaillent.</p>
                        <p>Notre objectif est de simplifier les processus médicaux grâce à des technologies innovantes et des équipements fiables.</p>
                    </div>
                    <div class="col-md-6 mb-4">
                        <h4 class="fw-bold mb-3">Notre Vision</h4>
                        <p>Nous aspirons à devenir le partenaire de référence pour tous les professionnels de santé en Algérie, en offrant des solutions complètes et adaptées à leurs besoins spécifiques.</p>
                        <p>Innovation, qualité et service client sont au cœur de tout ce que nous faisons.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Features -->
        <div class="mb-5">
            <h2 class="section-title">Pourquoi nous choisir ?</h2>
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-award"></i>
                        </div>
                        <h5 class="fw-bold mb-3">Qualité Premium</h5>
                        <p>Tous nos produits sont soigneusement sélectionnés et testés pour garantir la meilleure qualité.</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-headset"></i>
                        </div>
                        <h5 class="fw-bold mb-3">Support Expert</h5>
                        <p>Notre équipe d'experts est disponible pour vous accompagner et répondre à toutes vos questions.</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shipping-fast"></i>
                        </div>
                        <h5 class="fw-bold mb-3">Livraison Rapide</h5>
                        <p>Livraison rapide et sécurisée dans toute l'Algérie avec suivi en temps réel.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stats -->
        <div class="mb-5">
            <h2 class="section-title">Nos Chiffres</h2>
            <div class="row">
                <div class="col-md-3 mb-4">
                    <div class="stats-card">
                        <div class="stats-number"><?php echo $store['product_count'] ?? count($products ?? []); ?>+</div>
                        <p class="mb-0">Produits</p>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="stats-card">
                        <div class="stats-number">500+</div>
                        <p class="mb-0">Clients Satisfaits</p>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="stats-card">
                        <div class="stats-number">5</div>
                        <p class="mb-0">Années d'Expérience</p>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="stats-card">
                        <div class="stats-number">24/7</div>
                        <p class="mb-0">Support</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-light py-5 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5><?php echo htmlspecialchars($store['store_name']); ?></h5>
                    <p><?php echo htmlspecialchars($store['description'] ?? ''); ?></p>
                </div>
                <div class="col-md-4">
                    <h6>Contact</h6>
                    <?php if (!empty($store['phone'])): ?>
                        <p><i class="fas fa-phone me-2"></i><?php echo htmlspecialchars($store['phone']); ?></p>
                    <?php endif; ?>
                    <?php if (!empty($store['email'])): ?>
                        <p><i class="fas fa-envelope me-2"></i><?php echo htmlspecialchars($store['email']); ?></p>
                    <?php endif; ?>
                </div>
                <div class="col-md-4">
                    <h6>Liens rapides</h6>
                    <ul class="list-unstyled">
                        <li><a href="/store-frontend.php?store=medical-software-solutions" class="text-light text-decoration-none">Accueil</a></li>
                        <li><a href="/store-frontend.php?store=medical-software-solutions&page=products" class="text-light text-decoration-none">Produits</a></li>
                        <li><a href="/store-frontend.php?store=medical-software-solutions&page=about" class="text-light text-decoration-none">À propos</a></li>
                        <li><a href="/store-frontend.php?store=medical-software-solutions&page=contact" class="text-light text-decoration-none">Contact</a></li>
                    </ul>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p class="mb-0">&copy; <?php echo date('Y'); ?> <?php echo htmlspecialchars($store['store_name']); ?>. Tous droits réservés.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
