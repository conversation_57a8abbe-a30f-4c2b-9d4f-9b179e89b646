<?php
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/auth.php';

// Vérification de l'authentification
if (!isAuthenticated()) {
    header('Location: /login.html');
    exit;
}

$user = getCurrentUser();
$storeId = $_GET['store_id'] ?? null;

if (!$storeId) {
    echo '<div class="alert alert-danger">Store ID is required</div>';
    exit;
}

try {
    $db = connectDB();
    
    // Get store information
    $storeQuery = "SELECT * FROM stores WHERE id = ?";
    $stmt = $db->prepare($storeQuery);
    $stmt->execute([$storeId]);
    $store = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$store) {
        echo '<div class="alert alert-danger">Store not found</div>';
        exit;
    }
    
    // Parse store settings
    $settings = json_decode($store['settings'] ?? '{}', true) ?: [];
    $pageSettings = $settings['pages'] ?? [
        'home' => true,
        'products' => true,
        'about' => true,
        'contact' => true
    ];
    
} catch (Exception $e) {
    echo '<div class="alert alert-danger">Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
    exit;
}
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-file-alt text-primary me-2"></i>
                        Gestion des Pages du Store
                    </h1>
                    <p class="text-muted mb-0">
                        Gérez les pages de votre boutique: <?php echo htmlspecialchars($store['store_name']); ?>
                    </p>
                </div>
                <div class="d-flex gap-2">
                    <a href="?page=merchant-dashboard" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Retour
                    </a>
                    <button class="btn btn-outline-primary" onclick="previewStore()">
                        <i class="fas fa-eye me-1"></i>Aperçu de la boutique
                    </button>
                </div>
            </div>

            <!-- Store Pages Management -->
            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-toggle-on me-2"></i>
                                Pages de la boutique
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <!-- Home Page -->
                                <div class="col-md-6 mb-4">
                                    <div class="card border">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-home text-primary me-2"></i>
                                                    Page d'accueil
                                                </h6>
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="homePageToggle" 
                                                           <?php echo ($pageSettings['home'] ?? true) ? 'checked' : ''; ?>
                                                           onchange="togglePage('home', this.checked)">
                                                </div>
                                            </div>
                                            <p class="text-muted small mb-3">
                                                Page principale avec produits en vedette et présentation de la boutique
                                            </p>
                                            <div class="d-flex gap-2">
                                                <button class="btn btn-sm btn-outline-primary" onclick="editPageContent('home')">
                                                    <i class="fas fa-edit me-1"></i>Modifier
                                                </button>
                                                <a href="/store-frontend.php?store=medical-software-solutions" target="_blank" 
                                                   class="btn btn-sm btn-outline-secondary">
                                                    <i class="fas fa-external-link-alt me-1"></i>Voir
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Products Page -->
                                <div class="col-md-6 mb-4">
                                    <div class="card border">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-box text-success me-2"></i>
                                                    Page des produits
                                                </h6>
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="productsPageToggle" 
                                                           <?php echo ($pageSettings['products'] ?? true) ? 'checked' : ''; ?>
                                                           onchange="togglePage('products', this.checked)">
                                                </div>
                                            </div>
                                            <p class="text-muted small mb-3">
                                                Catalogue complet de tous vos produits avec filtres et recherche
                                            </p>
                                            <div class="d-flex gap-2">
                                                <button class="btn btn-sm btn-outline-primary" onclick="editPageContent('products')">
                                                    <i class="fas fa-edit me-1"></i>Modifier
                                                </button>
                                                <a href="/store-frontend.php?store=medical-software-solutions&page=products" target="_blank" 
                                                   class="btn btn-sm btn-outline-secondary">
                                                    <i class="fas fa-external-link-alt me-1"></i>Voir
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- About Page -->
                                <div class="col-md-6 mb-4">
                                    <div class="card border">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-info-circle text-info me-2"></i>
                                                    Page À propos
                                                </h6>
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="aboutPageToggle" 
                                                           <?php echo ($pageSettings['about'] ?? true) ? 'checked' : ''; ?>
                                                           onchange="togglePage('about', this.checked)">
                                                </div>
                                            </div>
                                            <p class="text-muted small mb-3">
                                                Présentation de votre entreprise, mission et valeurs
                                            </p>
                                            <div class="d-flex gap-2">
                                                <button class="btn btn-sm btn-outline-primary" onclick="editPageContent('about')">
                                                    <i class="fas fa-edit me-1"></i>Modifier
                                                </button>
                                                <a href="/store-frontend.php?store=medical-software-solutions&page=about" target="_blank" 
                                                   class="btn btn-sm btn-outline-secondary">
                                                    <i class="fas fa-external-link-alt me-1"></i>Voir
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Contact Page -->
                                <div class="col-md-6 mb-4">
                                    <div class="card border">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-envelope text-warning me-2"></i>
                                                    Page de contact
                                                </h6>
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="contactPageToggle" 
                                                           <?php echo ($pageSettings['contact'] ?? true) ? 'checked' : ''; ?>
                                                           onchange="togglePage('contact', this.checked)">
                                                </div>
                                            </div>
                                            <p class="text-muted small mb-3">
                                                Informations de contact et formulaire pour vos clients
                                            </p>
                                            <div class="d-flex gap-2">
                                                <button class="btn btn-sm btn-outline-primary" onclick="editPageContent('contact')">
                                                    <i class="fas fa-edit me-1"></i>Modifier
                                                </button>
                                                <a href="/store-frontend.php?store=medical-software-solutions&page=contact" target="_blank" 
                                                   class="btn btn-sm btn-outline-secondary">
                                                    <i class="fas fa-external-link-alt me-1"></i>Voir
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar with store info -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-store me-2"></i>
                                Informations de la boutique
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <strong>Nom:</strong><br>
                                <?php echo htmlspecialchars($store['store_name']); ?>
                            </div>
                            <div class="mb-3">
                                <strong>Description:</strong><br>
                                <?php echo htmlspecialchars($store['description'] ?? 'Aucune description'); ?>
                            </div>
                            <div class="mb-3">
                                <strong>Statut:</strong><br>
                                <span class="badge bg-<?php echo $store['status'] === 'active' ? 'success' : 'warning'; ?>">
                                    <?php echo ucfirst($store['status']); ?>
                                </span>
                            </div>
                            <div class="d-grid">
                                <a href="?page=store-customization&store_id=<?php echo $storeId; ?>" 
                                   class="btn btn-primary">
                                    <i class="fas fa-paint-brush me-1"></i>
                                    Personnaliser la boutique
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
const storeId = <?php echo json_encode($storeId); ?>;
let pageSettings = <?php echo json_encode($pageSettings); ?>;

function togglePage(pageName, enabled) {
    pageSettings[pageName] = enabled;
    savePageSettings();
}

function savePageSettings() {
    const formData = new FormData();
    formData.append('action', 'save_page_settings');
    formData.append('store_id', storeId);
    formData.append('page_settings', JSON.stringify(pageSettings));
    
    fetch('../api/store-customization.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Paramètres des pages sauvegardés!', 'success');
        } else {
            showNotification('Erreur: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Erreur lors de la sauvegarde', 'error');
    });
}

function editPageContent(pageName) {
    // For now, redirect to store customization
    window.location.href = `?page=store-customization&store_id=${storeId}`;
}

function previewStore() {
    window.open('/store-frontend.php?store=medical-software-solutions', '_blank');
}

function showNotification(message, type) {
    // Simple notification system
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(notification);
    
    // Auto remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}
</script>
