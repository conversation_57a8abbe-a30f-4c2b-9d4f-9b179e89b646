<?php
// Check if user is logged in and is a merchant
if (!isset($_SESSION['user']) || !in_array($_SESSION['user']['role'], ['merchant', 'admin'])) {
    header('Location: /login.php');
    exit;
}

$user = $_SESSION['user'];
$storeId = $_GET['store_id'] ?? null;

if (!$storeId) {
    header('Location: ?page=merchant-dashboard');
    exit;
}

// Get store information
require_once __DIR__ . '/../includes/database.php';
$db = connectDB();

try {
    $storeQuery = "SELECT * FROM stores WHERE id = ? AND merchant_id = ?";
    $stmt = $db->prepare($storeQuery);
    $stmt->execute([$storeId, $user['id']]);
    $store = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$store) {
        header('Location: ?page=merchant-dashboard');
        exit;
    }
} catch (Exception $e) {
    error_log("Error fetching store: " . $e->getMessage());
    header('Location: ?page=merchant-dashboard');
    exit;
}
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-paint-brush text-primary me-2"></i>
                        Personnalisation de la boutique
                    </h1>
                    <p class="text-muted mb-0">
                        Personnalisez l'apparence et les fonctionnalités de votre boutique
                    </p>
                </div>
                <div class="d-flex gap-2">
                    <a href="?page=merchant-dashboard" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Retour
                    </a>
                    <button class="btn btn-outline-primary" onclick="previewStore()">
                        <i class="fas fa-eye me-1"></i>Aperçu
                    </button>
                    <button class="btn btn-primary" onclick="saveCustomization()">
                        <i class="fas fa-save me-1"></i>Enregistrer
                    </button>
                </div>
            </div>

            <!-- Store Info Card -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="card-title mb-1"><?php echo htmlspecialchars($store['store_name']); ?></h5>
                            <p class="text-muted mb-0">
                                <i class="fas fa-link me-1"></i>
                                <?php
                                $storeSlug = $store['store_slug'] ?? $store['slug'] ?? $store['store_name'] ?? 'store-' . $store['id'];
                                $storeSlug = !empty($storeSlug) ? $storeSlug : 'store-' . $store['id'];
                                ?>
                                <a href="/store-frontend.php?store=<?php echo urlencode($storeSlug); ?>" target="_blank" class="text-decoration-none">
                                    <?php echo $_SERVER['HTTP_HOST']; ?>/store/<?php echo htmlspecialchars($storeSlug); ?>
                                </a>
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <span class="badge bg-<?php echo $store['status'] === 'active' ? 'success' : 'warning'; ?> fs-6">
                                <?php echo ucfirst($store['status']); ?>
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customization Tabs -->
            <div class="card">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" id="customizationTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="branding-tab" data-bs-toggle="tab" data-bs-target="#branding" type="button" role="tab">
                                <i class="fas fa-palette me-1"></i>Branding
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="layout-tab" data-bs-toggle="tab" data-bs-target="#layout" type="button" role="tab">
                                <i class="fas fa-th-large me-1"></i>Mise en page
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="products-tab" data-bs-toggle="tab" data-bs-target="#products" type="button" role="tab">
                                <i class="fas fa-box me-1"></i>Produits
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="seo-tab" data-bs-toggle="tab" data-bs-target="#seo" type="button" role="tab">
                                <i class="fas fa-search me-1"></i>SEO
                            </button>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="customizationTabsContent">
                        <!-- Branding Tab -->
                        <div class="tab-pane fade show active" id="branding" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5><i class="fas fa-image me-2"></i>Images</h5>

                                    <!-- Logo Upload -->
                                    <div class="mb-4">
                                        <label class="form-label">Logo de la boutique</label>
                                        <div class="logo-upload-area border rounded p-4 text-center">
                                            <div id="logoPreview" class="mb-3">
                                                <?php if (!empty($store['logo_url'])): ?>
                                                    <img src="<?php echo htmlspecialchars($store['logo_url']); ?>" alt="Logo" class="img-thumbnail" style="max-height: 100px;">
                                                <?php else: ?>
                                                    <i class="fas fa-image fa-3x text-muted"></i>
                                                <?php endif; ?>
                                            </div>
                                            <input type="file" id="logoInput" accept="image/*" class="d-none" onchange="handleLogoUpload(event)">
                                            <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('logoInput').click()">
                                                <i class="fas fa-upload me-1"></i>Télécharger un logo
                                            </button>
                                            <p class="text-muted small mt-2">Formats acceptés: JPG, PNG, SVG (max 2MB)</p>
                                        </div>
                                    </div>

                                    <!-- Banner Upload -->
                                    <div class="mb-4">
                                        <label class="form-label">Bannière de la boutique</label>
                                        <div class="banner-upload-area border rounded p-4 text-center">
                                            <div id="bannerPreview" class="mb-3">
                                                <?php if (!empty($store['cover_url'])): ?>
                                                    <img src="<?php echo htmlspecialchars($store['cover_url']); ?>" alt="Bannière" class="img-thumbnail" style="max-height: 150px; width: 100%; object-fit: cover;">
                                                <?php else: ?>
                                                    <div class="bg-light p-4 rounded">
                                                        <i class="fas fa-panorama fa-3x text-muted"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <input type="file" id="bannerInput" accept="image/*" class="d-none" onchange="handleBannerUpload(event)">
                                            <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('bannerInput').click()">
                                                <i class="fas fa-upload me-1"></i>Télécharger une bannière
                                            </button>
                                            <p class="text-muted small mt-2">Recommandé: 1200x400px (max 5MB)</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <h5><i class="fas fa-palette me-2"></i>Couleurs et Polices</h5>

                                    <!-- Color Scheme -->
                                    <div class="mb-4">
                                        <label class="form-label">Couleur principale</label>
                                        <div class="d-flex align-items-center gap-3">
                                            <input type="color" class="form-control form-control-color" id="primaryColor" value="#007bff" style="width: 60px;">
                                            <input type="text" class="form-control" id="primaryColorHex" value="#007bff" placeholder="#007bff">
                                        </div>
                                    </div>

                                    <div class="mb-4">
                                        <label class="form-label">Couleur secondaire</label>
                                        <div class="d-flex align-items-center gap-3">
                                            <input type="color" class="form-control form-control-color" id="secondaryColor" value="#6c757d" style="width: 60px;">
                                            <input type="text" class="form-control" id="secondaryColorHex" value="#6c757d" placeholder="#6c757d">
                                        </div>
                                    </div>

                                    <!-- Font Selection -->
                                    <div class="mb-4">
                                        <label class="form-label">Police de caractères</label>
                                        <select class="form-select" id="fontFamily">
                                            <option value="system">Police système</option>
                                            <option value="roboto">Roboto</option>
                                            <option value="open-sans">Open Sans</option>
                                            <option value="lato">Lato</option>
                                            <option value="montserrat">Montserrat</option>
                                            <option value="poppins">Poppins</option>
                                        </select>
                                    </div>

                                    <!-- Preview -->
                                    <div class="border rounded p-3 bg-light">
                                        <h6>Aperçu des couleurs</h6>
                                        <div class="d-flex gap-2 mb-2">
                                            <div id="primaryColorPreview" class="color-preview rounded" style="background-color: #007bff; width: 40px; height: 40px;"></div>
                                            <div id="secondaryColorPreview" class="color-preview rounded" style="background-color: #6c757d; width: 40px; height: 40px;"></div>
                                        </div>
                                        <p class="mb-0" id="fontPreview" style="font-family: system-ui;">
                                            Exemple de texte avec la police sélectionnée
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Layout Tab -->
                        <div class="tab-pane fade" id="layout" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5><i class="fas fa-th-large me-2"></i>Disposition générale</h5>

                                    <!-- Layout Options -->
                                    <div class="mb-4">
                                        <label class="form-label">Style de mise en page</label>
                                        <div class="layout-options">
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="radio" name="layoutStyle" id="layoutGrid" value="grid" checked>
                                                <label class="form-check-label" for="layoutGrid">
                                                    <strong>Grille</strong> - Affichage en grille des produits
                                                </label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="radio" name="layoutStyle" id="layoutList" value="list">
                                                <label class="form-check-label" for="layoutList">
                                                    <strong>Liste</strong> - Affichage en liste des produits
                                                </label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="radio" name="layoutStyle" id="layoutMasonry" value="masonry">
                                                <label class="form-check-label" for="layoutMasonry">
                                                    <strong>Mosaïque</strong> - Affichage en mosaïque dynamique
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Header Options -->
                                    <div class="mb-4">
                                        <label class="form-label">Options d'en-tête</label>
                                        <div class="form-check form-switch mb-2">
                                            <input class="form-check-input" type="checkbox" id="showStoreName" checked>
                                            <label class="form-check-label" for="showStoreName">
                                                Afficher le nom de la boutique
                                            </label>
                                        </div>
                                        <div class="form-check form-switch mb-2">
                                            <input class="form-check-input" type="checkbox" id="showStoreDescription" checked>
                                            <label class="form-check-label" for="showStoreDescription">
                                                Afficher la description
                                            </label>
                                        </div>
                                        <div class="form-check form-switch mb-2">
                                            <input class="form-check-input" type="checkbox" id="showSearchBar" checked>
                                            <label class="form-check-label" for="showSearchBar">
                                                Afficher la barre de recherche
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <h5><i class="fas fa-filter me-2"></i>Navigation et filtres</h5>

                                    <!-- Category Navigation -->
                                    <div class="mb-4">
                                        <label class="form-label">Navigation des catégories</label>
                                        <div class="form-check form-switch mb-2">
                                            <input class="form-check-input" type="checkbox" id="showCategoryFilter" checked>
                                            <label class="form-check-label" for="showCategoryFilter">
                                                Afficher les filtres de catégories
                                            </label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="radio" name="categoryPosition" id="categorySidebar" value="sidebar" checked>
                                            <label class="form-check-label" for="categorySidebar">
                                                Barre latérale
                                            </label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="radio" name="categoryPosition" id="categoryTop" value="top">
                                            <label class="form-check-label" for="categoryTop">
                                                En haut de page
                                            </label>
                                        </div>
                                    </div>

                                    <!-- Product Display -->
                                    <div class="mb-4">
                                        <label class="form-label">Affichage des produits</label>
                                        <div class="row">
                                            <div class="col-6">
                                                <label for="productsPerPage" class="form-label small">Produits par page</label>
                                                <select class="form-select" id="productsPerPage">
                                                    <option value="12">12</option>
                                                    <option value="24" selected>24</option>
                                                    <option value="36">36</option>
                                                    <option value="48">48</option>
                                                </select>
                                            </div>
                                            <div class="col-6">
                                                <label for="productsPerRow" class="form-label small">Colonnes</label>
                                                <select class="form-select" id="productsPerRow">
                                                    <option value="2">2</option>
                                                    <option value="3" selected>3</option>
                                                    <option value="4">4</option>
                                                    <option value="6">6</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Products Tab -->
                        <div class="tab-pane fade" id="products" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5><i class="fas fa-box me-2"></i>Affichage des produits</h5>

                                    <!-- Product Card Options -->
                                    <div class="mb-4">
                                        <label class="form-label">Options des cartes produits</label>
                                        <div class="form-check form-switch mb-2">
                                            <input class="form-check-input" type="checkbox" id="showProductPrice" checked>
                                            <label class="form-check-label" for="showProductPrice">
                                                Afficher les prix
                                            </label>
                                        </div>
                                        <div class="form-check form-switch mb-2">
                                            <input class="form-check-input" type="checkbox" id="showProductDescription" checked>
                                            <label class="form-check-label" for="showProductDescription">
                                                Afficher les descriptions courtes
                                            </label>
                                        </div>
                                        <div class="form-check form-switch mb-2">
                                            <input class="form-check-input" type="checkbox" id="showAddToCart" checked>
                                            <label class="form-check-label" for="showAddToCart">
                                                Bouton "Ajouter au panier"
                                            </label>
                                        </div>
                                        <div class="form-check form-switch mb-2">
                                            <input class="form-check-input" type="checkbox" id="showQuickView">
                                            <label class="form-check-label" for="showQuickView">
                                                Aperçu rapide
                                            </label>
                                        </div>
                                    </div>

                                    <!-- Sorting Options -->
                                    <div class="mb-4">
                                        <label class="form-label">Tri par défaut</label>
                                        <select class="form-select" id="defaultSort">
                                            <option value="name">Nom (A-Z)</option>
                                            <option value="price_asc">Prix croissant</option>
                                            <option value="price_desc">Prix décroissant</option>
                                            <option value="newest" selected>Plus récents</option>
                                            <option value="popular">Plus populaires</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <h5><i class="fas fa-tags me-2"></i>Catégories et filtres</h5>

                                    <!-- Category Display -->
                                    <div class="mb-4">
                                        <label class="form-label">Affichage des catégories</label>
                                        <div class="form-check form-switch mb-2">
                                            <input class="form-check-input" type="checkbox" id="showCategoryImages" checked>
                                            <label class="form-check-label" for="showCategoryImages">
                                                Images des catégories
                                            </label>
                                        </div>
                                        <div class="form-check form-switch mb-2">
                                            <input class="form-check-input" type="checkbox" id="showProductCount" checked>
                                            <label class="form-check-label" for="showProductCount">
                                                Nombre de produits par catégorie
                                            </label>
                                        </div>
                                    </div>

                                    <!-- Advanced Filters -->
                                    <div class="mb-4">
                                        <label class="form-label">Filtres avancés</label>
                                        <div class="form-check form-switch mb-2">
                                            <input class="form-check-input" type="checkbox" id="enablePriceFilter" checked>
                                            <label class="form-check-label" for="enablePriceFilter">
                                                Filtre par prix
                                            </label>
                                        </div>
                                        <div class="form-check form-switch mb-2">
                                            <input class="form-check-input" type="checkbox" id="enableBrandFilter">
                                            <label class="form-check-label" for="enableBrandFilter">
                                                Filtre par marque
                                            </label>
                                        </div>
                                        <div class="form-check form-switch mb-2">
                                            <input class="form-check-input" type="checkbox" id="enableRatingFilter">
                                            <label class="form-check-label" for="enableRatingFilter">
                                                Filtre par note
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- SEO Tab -->
                        <div class="tab-pane fade" id="seo" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5><i class="fas fa-search me-2"></i>Référencement</h5>

                                    <!-- Meta Information -->
                                    <div class="mb-3">
                                        <label for="metaTitle" class="form-label">Titre de la page (Meta Title)</label>
                                        <input type="text" class="form-control" id="metaTitle" value="<?php echo htmlspecialchars($store['store_name']); ?>" maxlength="60">
                                        <div class="form-text">Recommandé: 50-60 caractères</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="metaDescription" class="form-label">Description (Meta Description)</label>
                                        <textarea class="form-control" id="metaDescription" rows="3" maxlength="160"><?php echo htmlspecialchars($store['description'] ?? ''); ?></textarea>
                                        <div class="form-text">Recommandé: 150-160 caractères</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="metaKeywords" class="form-label">Mots-clés</label>
                                        <input type="text" class="form-control" id="metaKeywords" placeholder="mot-clé1, mot-clé2, mot-clé3">
                                        <div class="form-text">Séparez les mots-clés par des virgules</div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <h5><i class="fas fa-share-alt me-2"></i>Réseaux sociaux</h5>

                                    <!-- Social Media -->
                                    <div class="mb-3">
                                        <label for="ogTitle" class="form-label">Titre pour les réseaux sociaux</label>
                                        <input type="text" class="form-control" id="ogTitle" value="<?php echo htmlspecialchars($store['store_name']); ?>">
                                    </div>

                                    <div class="mb-3">
                                        <label for="ogDescription" class="form-label">Description pour les réseaux sociaux</label>
                                        <textarea class="form-control" id="ogDescription" rows="3"><?php echo htmlspecialchars($store['description'] ?? ''); ?></textarea>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">Image de partage</label>
                                        <div class="social-image-upload border rounded p-3 text-center">
                                            <div id="socialImagePreview" class="mb-2">
                                                <i class="fas fa-image fa-2x text-muted"></i>
                                            </div>
                                            <input type="file" id="socialImageInput" accept="image/*" class="d-none">
                                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="document.getElementById('socialImageInput').click()">
                                                <i class="fas fa-upload me-1"></i>Télécharger
                                            </button>
                                            <p class="text-muted small mt-1">Recommandé: 1200x630px</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .color-preview {
        border: 2px solid #dee2e6;
        cursor: pointer;
        transition: transform 0.2s;
    }

    .color-preview:hover {
        transform: scale(1.1);
    }

    .layout-options .form-check {
        padding: 10px;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        transition: background-color 0.2s;
    }

    .layout-options .form-check:hover {
        background-color: #f8f9fa;
    }

    .layout-options .form-check-input:checked+.form-check-label {
        color: #0d6efd;
    }

    .logo-upload-area,
    .banner-upload-area,
    .social-image-upload {
        transition: border-color 0.2s;
    }

    .logo-upload-area:hover,
    .banner-upload-area:hover,
    .social-image-upload:hover {
        border-color: #0d6efd;
    }
</style>

<script>
    // Store customization functions
    let storeId = <?php echo json_encode($storeId); ?>;
    let storeData = <?php echo json_encode($store); ?>;

    // Color picker synchronization
    document.getElementById('primaryColor').addEventListener('change', function() {
        document.getElementById('primaryColorHex').value = this.value;
        document.getElementById('primaryColorPreview').style.backgroundColor = this.value;
    });

    document.getElementById('primaryColorHex').addEventListener('input', function() {
        if (/^#[0-9A-F]{6}$/i.test(this.value)) {
            document.getElementById('primaryColor').value = this.value;
            document.getElementById('primaryColorPreview').style.backgroundColor = this.value;
        }
    });

    document.getElementById('secondaryColor').addEventListener('change', function() {
        document.getElementById('secondaryColorHex').value = this.value;
        document.getElementById('secondaryColorPreview').style.backgroundColor = this.value;
    });

    document.getElementById('secondaryColorHex').addEventListener('input', function() {
        if (/^#[0-9A-F]{6}$/i.test(this.value)) {
            document.getElementById('secondaryColor').value = this.value;
            document.getElementById('secondaryColorPreview').style.backgroundColor = this.value;
        }
    });

    // Font preview
    document.getElementById('fontFamily').addEventListener('change', function() {
        const fontMap = {
            'system': 'system-ui',
            'roboto': 'Roboto, sans-serif',
            'open-sans': 'Open Sans, sans-serif',
            'lato': 'Lato, sans-serif',
            'montserrat': 'Montserrat, sans-serif',
            'poppins': 'Poppins, sans-serif'
        };

        document.getElementById('fontPreview').style.fontFamily = fontMap[this.value] || 'system-ui';
    });

    // File upload handlers
    function handleLogoUpload(event) {
        const file = event.target.files[0];
        if (file) {
            if (file.size > 2 * 1024 * 1024) {
                alert('Le fichier est trop volumineux. Taille maximale: 2MB');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById('logoPreview').innerHTML =
                    `<img src="${e.target.result}" alt="Logo" class="img-thumbnail" style="max-height: 100px;">`;
            };
            reader.readAsDataURL(file);
        }
    }

    function handleBannerUpload(event) {
        const file = event.target.files[0];
        if (file) {
            if (file.size > 5 * 1024 * 1024) {
                alert('Le fichier est trop volumineux. Taille maximale: 5MB');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById('bannerPreview').innerHTML =
                    `<img src="${e.target.result}" alt="Bannière" class="img-thumbnail" style="max-height: 150px; width: 100%; object-fit: cover;">`;
            };
            reader.readAsDataURL(file);
        }
    }

    // Main functions
    function previewStore() {
        // Get store slug with fallbacks
        const storeSlug = storeData.store_slug || storeData.slug || storeData.store_name || `store-${storeData.id}`;

        if (!storeSlug || storeSlug === 'undefined') {
            alert('Erreur: Impossible de déterminer l\'URL de la boutique. Veuillez d\'abord enregistrer vos modifications.');
            return;
        }

        // Open store frontend
        window.open(`/store-frontend.php?store=${encodeURIComponent(storeSlug)}`, '_blank');
    }

    function saveCustomization() {
        // Collect all customization data
        const customizationData = {
            store_id: storeId,
            branding: {
                primary_color: document.getElementById('primaryColor').value,
                secondary_color: document.getElementById('secondaryColor').value,
                font_family: document.getElementById('fontFamily').value
            },
            layout: {
                style: document.querySelector('input[name="layoutStyle"]:checked').value,
                show_store_name: document.getElementById('showStoreName').checked,
                show_store_description: document.getElementById('showStoreDescription').checked,
                show_search_bar: document.getElementById('showSearchBar').checked,
                show_category_filter: document.getElementById('showCategoryFilter').checked,
                category_position: document.querySelector('input[name="categoryPosition"]:checked').value,
                products_per_page: document.getElementById('productsPerPage').value,
                products_per_row: document.getElementById('productsPerRow').value
            },
            products: {
                show_price: document.getElementById('showProductPrice').checked,
                show_description: document.getElementById('showProductDescription').checked,
                show_add_to_cart: document.getElementById('showAddToCart').checked,
                show_quick_view: document.getElementById('showQuickView').checked,
                default_sort: document.getElementById('defaultSort').value,
                show_category_images: document.getElementById('showCategoryImages').checked,
                show_product_count: document.getElementById('showProductCount').checked,
                enable_price_filter: document.getElementById('enablePriceFilter').checked,
                enable_brand_filter: document.getElementById('enableBrandFilter').checked,
                enable_rating_filter: document.getElementById('enableRatingFilter').checked
            },
            seo: {
                meta_title: document.getElementById('metaTitle').value,
                meta_description: document.getElementById('metaDescription').value,
                meta_keywords: document.getElementById('metaKeywords').value,
                og_title: document.getElementById('ogTitle').value,
                og_description: document.getElementById('ogDescription').value
            }
        };

        // Show loading state
        const saveBtn = document.querySelector('button[onclick="saveCustomization()"]');
        const originalText = saveBtn.innerHTML;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Enregistrement...';
        saveBtn.disabled = true;

        // Simulate API call
        setTimeout(() => {
            // In production, this would send data to the API
            console.log('Saving customization:', customizationData);

            // Show success message
            showNotification('Personnalisation sauvegardée avec succès!', 'success');

            // Restore button
            saveBtn.innerHTML = originalText;
            saveBtn.disabled = false;
        }, 1500);
    }

    function showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
        // Load existing customization settings if available
        // This would typically come from the database
        console.log('Store customization page loaded for store:', storeData);
    });
</script>
