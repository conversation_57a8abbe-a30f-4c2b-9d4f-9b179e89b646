<?php
$user = getCurrentUser();
$currentPage = $_GET['page'] ?? 'overview';
?>

<aside class="sidebar">
    <div class="sidebar-header">
        <h4 class="brand"><i class="fas fa-rocket"></i> Mon Espace</h4>
        <small>صفحات هبوط للجميع</small>
    </div>

    <!-- Profil utilisateur -->
    <div class="user-profile">
        <?php if (!empty($user['avatar'])): ?>
            <img src="<?php echo htmlspecialchars($user['avatar']); ?>" alt="Avatar" class="user-avatar">
        <?php else: ?>
            <div class="user-initials"><?php echo getInitials($user['name'] ?? ''); ?></div>
        <?php endif; ?>
        <h6 class="user-name"><?php echo htmlspecialchars($user['display_name'] ?? $user['name'] ?? 'Utilisateur'); ?></h6>
        <span class="user-email"><?php echo htmlspecialchars($user['email'] ?? ''); ?></span>
        <span class="user-role badge bg-<?php echo getUserRole() === 'admin' ? 'danger' : (getUserRole() === 'merchant' ? 'warning' : 'info'); ?> mt-1">
            <?php
            $roleLabels = [
                'admin' => 'Administrateur',
                'merchant' => 'Marchand',
                'customer' => 'Client'
            ];
            echo $roleLabels[getUserRole()] ?? 'Utilisateur';
            ?>
        </span>
        <div class="user-plan">
            <span class="badge bg-warning"><?php echo getUserPlanName($user['plan'] ?? 'free'); ?></span>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="sidebar-nav">
        <a href="?page=overview" class="nav-link <?php echo $currentPage === 'overview' ? 'active' : ''; ?>">
            <i class="fas fa-home"></i> Tableau de bord
        </a>



        <!-- Section Dashboards -->
        <div class="nav-section">
            <h6 class="nav-section-title">Dashboards</h6>

            <?php if (isAdmin()): ?>
                <a href="?page=overview" class="nav-link <?php echo $currentPage === 'overview' ? 'active' : ''; ?>">
                    <i class="fas fa-tachometer-alt"></i> Vue d'ensemble Admin
                </a>
                <a href="?page=merchant-dashboard" class="nav-link <?php echo $currentPage === 'merchant-dashboard' ? 'active' : ''; ?>">
                    <i class="fas fa-store"></i> Dashboard Marchand
                </a>
                <a href="?page=customer-dashboard" class="nav-link <?php echo $currentPage === 'customer-dashboard' ? 'active' : ''; ?>">
                    <i class="fas fa-user"></i> Dashboard Client
                </a>
            <?php elseif (isMerchant()): ?>
                <a href="?page=merchant-dashboard" class="nav-link <?php echo $currentPage === 'merchant-dashboard' ? 'active' : ''; ?>">
                    <i class="fas fa-store"></i> Mon Dashboard
                </a>
            <?php else: ?>
                <a href="?page=customer-dashboard" class="nav-link <?php echo $currentPage === 'customer-dashboard' ? 'active' : ''; ?>">
                    <i class="fas fa-user"></i> Mon Dashboard
                </a>
            <?php endif; ?>
        </div>

        <!-- Section Contenu (Admin et Merchant) -->
        <?php if (isAdmin() || isMerchant()): ?>
            <div class="nav-section">
                <h6 class="nav-section-title">Gestion de contenu</h6>
                <?php if (isAdmin() || isMerchant()): ?>
                    <a href="?page=landing-page-builder" class="nav-link <?php echo $currentPage === 'landing-page-builder' ? 'active' : ''; ?>">
                        <i class="fas fa-magic"></i> Landing Pages
                    </a>
                    <a href="?page=pages<?php echo isMerchant() && isset($_GET['store_id']) ? '&store_id=' . htmlspecialchars($_GET['store_id']) : ''; ?><?php echo isset($_GET['role']) ? '&role=' . htmlspecialchars($_GET['role']) : ''; ?>" class="nav-link <?php echo $currentPage === 'pages' ? 'active' : ''; ?>">
                        <i class="fas fa-copy"></i> Mes Pages
                    </a>
                    <a href="?page=products" class="nav-link <?php echo $currentPage === 'products' ? 'active' : ''; ?>">
                        <i class="fas fa-box"></i> Produits
                    </a>
                    <a href="?page=categories" class="nav-link <?php echo $currentPage === 'categories' ? 'active' : ''; ?>">
                        <i class="fas fa-tags"></i> Catégories
                    </a>
                    <a href="?page=orders" class="nav-link <?php echo $currentPage === 'orders' ? 'active' : ''; ?>">
                        <i class="fas fa-shopping-cart"></i> Commandes
                    </a>
                    <?php if (isMerchant()): ?>
                        <a href="?page=store-management<?php echo isset($_GET['email']) ? '&email=' . urlencode($_GET['email']) : ''; ?>" class="nav-link <?php echo $currentPage === 'store-management' ? 'active' : ''; ?>">
                            <i class="fas fa-store-alt"></i> Gestion de Boutique
                        </a>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <!-- Section Mon Compte pour Customer -->
        <?php if (isCustomer()): ?>
            <div class="nav-section">
                <h6 class="nav-section-title">Mon Compte</h6>
                <a href="?page=orders" class="nav-link <?php echo $currentPage === 'orders' ? 'active' : ''; ?>">
                    <i class="fas fa-shopping-bag"></i> Mes Commandes
                </a>
                <a href="?page=profile" class="nav-link <?php echo $currentPage === 'profile' ? 'active' : ''; ?>">
                    <i class="fas fa-user-edit"></i> Mon Profil
                </a>
            </div>
        <?php endif; ?>

        <!-- Section Utilisateurs & Abonnements (Admin seulement) -->
        <?php if (isAdmin()): ?>
            <div class="nav-section">
                <h6 class="nav-section-title">Administration</h6>
                <a href="?page=users" class="nav-link <?php echo $currentPage === 'users' ? 'active' : ''; ?>">
                    <i class="fas fa-users"></i> Utilisateurs
                </a>
                <a href="?page=stores" class="nav-link <?php echo $currentPage === 'stores' ? 'active' : ''; ?>">
                    <i class="fas fa-store"></i> Boutiques
                </a>
                <a href="?page=subscriptions" class="nav-link <?php echo $currentPage === 'subscriptions' ? 'active' : ''; ?>">
                    <i class="fas fa-credit-card"></i> Abonnements
                </a>
                <a href="?page=roles" class="nav-link <?php echo $currentPage === 'roles' ? 'active' : ''; ?>">
                    <i class="fas fa-user-shield"></i> Rôles
                </a>
                <a href="?page=system-settings" class="nav-link <?php echo $currentPage === 'system-settings' ? 'active' : ''; ?>">
                    <i class="fas fa-cogs"></i> Paramètres Système
                </a>
            </div>
        <?php endif; ?>

        <!-- Section Communication (Admin et Merchant) -->
        <?php if (isAdmin() || isMerchant()): ?>
            <div class="nav-section">
                <h6 class="nav-section-title">Communication</h6>
                <a href="?page=customer-support" class="nav-link <?php echo $currentPage === 'customer-support' ? 'active' : ''; ?>">
                    <i class="fas fa-headset"></i> Support Client
                </a>
                <a href="?page=ratings" class="nav-link <?php echo $currentPage === 'ratings' ? 'active' : ''; ?>">
                    <i class="fas fa-star"></i> Évaluations
                </a>
                <?php if (isAdmin()): ?>
                    <a href="?page=notifications" class="nav-link <?php echo $currentPage === 'notifications' ? 'active' : ''; ?>">
                        <i class="fas fa-bell"></i> Notifications
                    </a>
                    <a href="?page=smtp-config" class="nav-link <?php echo $currentPage === 'smtp-config' ? 'active' : ''; ?>">
                        <i class="fas fa-server"></i> Configuration SMTP
                    </a>
                    <a href="?page=email-send" class="nav-link <?php echo $currentPage === 'email-send' ? 'active' : ''; ?>">
                        <i class="fas fa-envelope"></i> Gestion Emails
                    </a>
                <?php endif; ?>
                <a href="?page=messages" class="nav-link <?php echo $currentPage === 'messages' ? 'active' : ''; ?>">
                    <i class="fas fa-comments"></i> Messages
                </a>
            </div>
        <?php endif; ?>

        <!-- Section Support pour Customer -->
        <?php if (isCustomer()): ?>
            <div class="nav-section">
                <h6 class="nav-section-title">Support</h6>
                <a href="?page=contact" class="nav-link <?php echo $currentPage === 'contact' ? 'active' : ''; ?>">
                    <i class="fas fa-envelope"></i> Nous contacter
                </a>
            </div>
        <?php endif; ?>

        <!-- Section IA & Configuration (Admin et Merchant) -->
        <?php if (isAdmin() || isMerchant()): ?>
            <div class="nav-section">
                <h6 class="nav-section-title">IA & Configuration</h6>
                <?php if (isAdmin()): ?>
                    <a href="?page=ai-config" class="nav-link <?php echo $currentPage === 'ai-config' ? 'active' : ''; ?>">
                        <i class="fas fa-robot"></i> Configuration IA
                    </a>
                <?php endif; ?>
                <a href="?page=analytics" class="nav-link <?php echo $currentPage === 'analytics' ? 'active' : ''; ?>">
                    <i class="fas fa-chart-line"></i> Statistiques
                </a>
                <a href="?page=settings" class="nav-link <?php echo $currentPage === 'settings' ? 'active' : ''; ?>">
                    <i class="fas fa-cog"></i> Paramètres
                </a>
            </div>
        <?php endif; ?>


    </nav>

    <!-- Bouton déconnexion -->
    <div class="sidebar-footer">
        <a href="/logout.php" class="btn btn-logout">
            <i class="fas fa-sign-out-alt"></i> Déconnexion
        </a>
    </div>
</aside>
