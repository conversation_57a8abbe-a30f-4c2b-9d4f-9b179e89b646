<!DOCTYPE html>
<html lang="fr" dir="ltr">
<?php
// Définir le slug pour ce store (medical-software-solutions)
$store['slug'] = 'medical-software-solutions';
?>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Produits - <?php echo htmlspecialchars($store['store_name']); ?></title>
    <meta name="description" content="Découvrez tous nos produits - <?php echo htmlspecialchars($store['description'] ?? ''); ?>">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Custom Store Styles -->
    <style>
        :root {
            --primary-color: <?php echo $customization['primary_color'] ?? '#007bff'; ?>;
            --secondary-color: <?php echo $customization['secondary_color'] ?? '#6c757d'; ?>;
        }
        
        .product-card {
            transition: transform 0.2s, box-shadow 0.2s;
            height: 100%;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .product-image {
            height: 200px;
            object-fit: cover;
            background: #f8f9fa;
        }
        
        .filter-sidebar {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }
        
        .breadcrumb {
            background: none;
            padding: 0;
        }
    </style>
</head>

<body>
    <!-- Navigation (same as home page) -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm sticky-top">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="/store-frontend.php?store=medical-software-solutions">
                <?php if (!empty($store['logo_url'])): ?>
                    <img src="<?php echo htmlspecialchars($store['logo_url']); ?>" alt="Logo" class="store-logo me-2">
                <?php endif; ?>
                <span class="fw-bold" style="color: var(--primary-color);">
                    <?php echo htmlspecialchars($store['store_name']); ?>
                </span>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <?php 
                    $currentPage = $_GET['page'] ?? 'home';
                    $storeSlug = 'medical-software-solutions';
                    $storeSettings = json_decode($store['settings'] ?? '{}', true);
                    $pageSettings = $storeSettings['pages'] ?? ['home' => true, 'products' => true, 'about' => true, 'contact' => true];
                    ?>
                    
                    <?php if ($pageSettings['home'] !== false): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $currentPage === 'home' ? 'active' : ''; ?>" 
                           href="/store-frontend.php?store=<?php echo $storeSlug; ?>">
                            <i class="fas fa-home me-1"></i>Accueil
                        </a>
                    </li>
                    <?php endif; ?>
                    
                    <?php if ($pageSettings['products'] !== false): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $currentPage === 'products' ? 'active' : ''; ?>" 
                           href="/store-frontend.php?store=<?php echo $storeSlug; ?>&page=products">
                            <i class="fas fa-box me-1"></i>Produits
                        </a>
                    </li>
                    <?php endif; ?>
                    
                    <?php if ($pageSettings['about'] !== false): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $currentPage === 'about' ? 'active' : ''; ?>" 
                           href="/store-frontend.php?store=<?php echo $storeSlug; ?>&page=about">
                            <i class="fas fa-info-circle me-1"></i>À propos
                        </a>
                    </li>
                    <?php endif; ?>
                    
                    <?php if ($pageSettings['contact'] !== false): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $currentPage === 'contact' ? 'active' : ''; ?>" 
                           href="/store-frontend.php?store=<?php echo $storeSlug; ?>&page=contact">
                            <i class="fas fa-envelope me-1"></i>Contact
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>

                <!-- Search Bar -->
                <div class="d-flex me-3">
                    <form class="d-flex" action="/store-frontend.php" method="GET">
                        <input type="hidden" name="store" value="medical-software-solutions">
                        <input type="hidden" name="page" value="search">
                        <input class="form-control search-bar" type="search" name="q" placeholder="Rechercher des produits...">
                        <button class="btn btn-primary search-btn" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>

                <!-- Cart Button -->
                <button class="btn btn-outline-primary position-relative" onclick="toggleCart()">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="cart-count">
                        0
                    </span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container my-5">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/store-frontend.php?store=medical-software-solutions">Accueil</a></li>
                <li class="breadcrumb-item active">Produits</li>
            </ol>
        </nav>

        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="display-5 fw-bold mb-3">
                    <i class="fas fa-box me-3" style="color: var(--primary-color);"></i>
                    Nos Produits
                </h1>
                <p class="lead text-muted">Découvrez notre gamme complète de solutions logicielles médicales</p>
            </div>
        </div>

        <div class="row">
            <!-- Filters Sidebar -->
            <div class="col-lg-3 mb-4">
                <div class="filter-sidebar">
                    <h5 class="mb-3"><i class="fas fa-filter me-2"></i>Filtres</h5>
                    
                    <!-- Categories Filter -->
                    <div class="mb-4">
                        <h6>Catégories</h6>
                        <?php
                        $categories = getStoreCategories($store['id']);
                        foreach ($categories as $category):
                        ?>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="cat-<?php echo $category['id']; ?>">
                            <label class="form-check-label" for="cat-<?php echo $category['id']; ?>">
                                <?php echo htmlspecialchars($category['name']); ?>
                                <span class="badge bg-light text-dark ms-1"><?php echo $category['product_count']; ?></span>
                            </label>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- Price Filter -->
                    <div class="mb-4">
                        <h6>Prix</h6>
                        <div class="row">
                            <div class="col-6">
                                <input type="number" class="form-control form-control-sm" placeholder="Min" id="price-min">
                            </div>
                            <div class="col-6">
                                <input type="number" class="form-control form-control-sm" placeholder="Max" id="price-max">
                            </div>
                        </div>
                        <button class="btn btn-sm btn-primary mt-2 w-100" onclick="applyPriceFilter()">Appliquer</button>
                    </div>
                </div>
            </div>

            <!-- Products Grid -->
            <div class="col-lg-9">
                <!-- Sort and View Options -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <span class="text-muted">Affichage de <?php echo count($products); ?> produits</span>
                    </div>
                    <div class="d-flex gap-2">
                        <select class="form-select form-select-sm" style="width: auto;" onchange="sortProducts(this.value)">
                            <option value="newest">Plus récents</option>
                            <option value="price_asc">Prix croissant</option>
                            <option value="price_desc">Prix décroissant</option>
                            <option value="name">Nom A-Z</option>
                        </select>
                    </div>
                </div>

                <!-- Products Grid -->
                <div class="row" id="products-grid">
                    <?php foreach ($products as $product): ?>
                    <div class="col-md-4 mb-4">
                        <div class="card product-card h-100">
                            <div class="position-relative">
                                <?php if (!empty($product['image_url'])): ?>
                                    <img src="<?php echo htmlspecialchars($product['image_url']); ?>" 
                                         class="card-img-top product-image" alt="<?php echo htmlspecialchars($product['name']); ?>">
                                <?php else: ?>
                                    <div class="card-img-top product-image d-flex align-items-center justify-content-center">
                                        <i class="fas fa-image fa-3x text-muted"></i>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="position-absolute top-0 end-0 m-2">
                                    <button class="btn btn-sm btn-light rounded-circle" onclick="toggleWishlist(<?php echo $product['id']; ?>)">
                                        <i class="far fa-heart"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="card-body d-flex flex-column">
                                <h6 class="card-title"><?php echo htmlspecialchars($product['name']); ?></h6>
                                <p class="card-text text-muted small flex-grow-1">
                                    <?php echo htmlspecialchars(substr($product['description'] ?? '', 0, 100)); ?>...
                                </p>
                                
                                <div class="mt-auto">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span class="h5 text-primary mb-0"><?php echo number_format($product['price'], 2); ?> DZD</span>
                                        <?php if (!empty($product['categories'])): ?>
                                            <span class="badge bg-light text-dark"><?php echo htmlspecialchars($product['categories']); ?></span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <button class="btn btn-primary w-100" onclick="addToCart(<?php echo $product['id']; ?>)">
                                        <i class="fas fa-shopping-cart me-1"></i>Ajouter au panier
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <!-- Pagination -->
                <?php if (count($products) >= 12): ?>
                <nav class="mt-4">
                    <ul class="pagination justify-content-center">
                        <li class="page-item"><a class="page-link" href="#">Précédent</a></li>
                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                        <li class="page-item"><a class="page-link" href="#">2</a></li>
                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                        <li class="page-item"><a class="page-link" href="#">Suivant</a></li>
                    </ul>
                </nav>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-light py-5 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5><?php echo htmlspecialchars($store['store_name']); ?></h5>
                    <p><?php echo htmlspecialchars($store['description'] ?? ''); ?></p>
                </div>
                <div class="col-md-4">
                    <h6>Liens rapides</h6>
                    <ul class="list-unstyled">
                        <li><a href="/store-frontend.php?store=medical-software-solutions" class="text-light text-decoration-none">Accueil</a></li>
                        <li><a href="/store-frontend.php?store=medical-software-solutions&page=products" class="text-light text-decoration-none">Produits</a></li>
                        <li><a href="/store-frontend.php?store=medical-software-solutions&page=about" class="text-light text-decoration-none">À propos</a></li>
                        <li><a href="/store-frontend.php?store=medical-software-solutions&page=contact" class="text-light text-decoration-none">Contact</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function addToCart(productId) {
            // Add to cart functionality
            console.log('Adding product to cart:', productId);
            alert('Produit ajouté au panier!');
        }
        
        function toggleWishlist(productId) {
            // Wishlist functionality
            console.log('Toggle wishlist:', productId);
        }
        
        function sortProducts(sortBy) {
            // Reload page with sort parameter
            const url = new URL(window.location);
            url.searchParams.set('sort', sortBy);
            window.location.href = url.toString();
        }
        
        function applyPriceFilter() {
            const minPrice = document.getElementById('price-min').value;
            const maxPrice = document.getElementById('price-max').value;
            
            const url = new URL(window.location);
            if (minPrice) url.searchParams.set('min_price', minPrice);
            if (maxPrice) url.searchParams.set('max_price', maxPrice);
            
            window.location.href = url.toString();
        }
    </script>
</body>
</html>
