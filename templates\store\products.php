<!DOCTYPE html>
<html lang="fr" dir="ltr">
<?php
// Définir le slug pour ce store (medical-software-solutions)
$store['slug'] = 'medical-software-solutions';
?>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Produits - <?php echo htmlspecialchars($store['store_name']); ?></title>
    <meta name="description" content="Découvrez tous nos produits - <?php echo htmlspecialchars($store['description'] ?? ''); ?>">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Custom Store Styles -->
    <style>
        :root {
            --primary-color: <?php echo $customization['primary_color'] ?? '#007bff'; ?>;
            --secondary-color: <?php echo $customization['secondary_color'] ?? '#6c757d'; ?>;
        }

        .product-card {
            transition: transform 0.2s, box-shadow 0.2s;
            height: 100%;
            border: 1px solid #e9ecef;
        }

        .product-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
            border-color: var(--primary-color);
        }

        .product-image {
            height: 180px;
            object-fit: cover;
            background: #f8f9fa;
        }

        .product-card .card-body {
            padding: 1rem;
        }

        .product-card .card-title {
            font-size: 0.95rem;
            font-weight: 600;
            line-height: 1.3;
            height: 2.6rem;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        .filter-sidebar {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }

        .breadcrumb {
            background: none;
            padding: 0;
        }
    </style>
</head>

<body>
    <!-- Navigation (same as home page) -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm sticky-top">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="/store-frontend.php?store=medical-software-solutions">
                <?php if (!empty($store['logo_url'])): ?>
                    <img src="<?php echo htmlspecialchars($store['logo_url']); ?>" alt="Logo" class="store-logo me-2">
                <?php endif; ?>
                <span class="fw-bold" style="color: var(--primary-color);">
                    <?php echo htmlspecialchars($store['store_name']); ?>
                </span>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <?php
                    $currentPage = $_GET['page'] ?? 'home';
                    $storeSlug = 'medical-software-solutions';
                    $storeSettings = json_decode($store['settings'] ?? '{}', true);
                    $pageSettings = $storeSettings['pages'] ?? ['home' => true, 'products' => true, 'about' => true, 'contact' => true];
                    ?>

                    <?php if ($pageSettings['home'] !== false): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo $currentPage === 'home' ? 'active' : ''; ?>"
                                href="/store-frontend.php?store=<?php echo $storeSlug; ?>">
                                <i class="fas fa-home me-1"></i>Accueil
                            </a>
                        </li>
                    <?php endif; ?>

                    <?php if ($pageSettings['products'] !== false): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo $currentPage === 'products' ? 'active' : ''; ?>"
                                href="/store-frontend.php?store=<?php echo $storeSlug; ?>&page=products">
                                <i class="fas fa-box me-1"></i>Produits
                            </a>
                        </li>
                    <?php endif; ?>

                    <?php if ($pageSettings['about'] !== false): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo $currentPage === 'about' ? 'active' : ''; ?>"
                                href="/store-frontend.php?store=<?php echo $storeSlug; ?>&page=about">
                                <i class="fas fa-info-circle me-1"></i>À propos
                            </a>
                        </li>
                    <?php endif; ?>

                    <?php if ($pageSettings['contact'] !== false): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo $currentPage === 'contact' ? 'active' : ''; ?>"
                                href="/store-frontend.php?store=<?php echo $storeSlug; ?>&page=contact">
                                <i class="fas fa-envelope me-1"></i>Contact
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>

                <!-- Search Bar -->
                <div class="d-flex me-3">
                    <form class="d-flex" action="/store-frontend.php" method="GET">
                        <input type="hidden" name="store" value="medical-software-solutions">
                        <input type="hidden" name="page" value="search">
                        <input class="form-control search-bar" type="search" name="q" placeholder="Rechercher des produits...">
                        <button class="btn btn-primary search-btn" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>

                <!-- Cart Button -->
                <button class="btn btn-outline-primary position-relative" onclick="toggleCart()">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="cart-count">
                        0
                    </span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container my-5">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/store-frontend.php?store=medical-software-solutions">Accueil</a></li>
                <li class="breadcrumb-item active">Produits</li>
            </ol>
        </nav>

        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="display-5 fw-bold mb-3">
                    <i class="fas fa-box me-3" style="color: var(--primary-color);"></i>
                    Nos Produits
                </h1>
                <p class="lead text-muted">Découvrez notre gamme complète de solutions logicielles médicales</p>
            </div>
        </div>

        <div class="row">
            <!-- Filters Sidebar -->
            <div class="col-lg-3 mb-4">
                <div class="filter-sidebar">
                    <h5 class="mb-3"><i class="fas fa-filter me-2"></i>Filtres</h5>

                    <!-- Categories Filter -->
                    <div class="mb-4">
                        <h6>Catégories</h6>
                        <?php
                        $categories = getStoreCategories($store['id']);
                        foreach ($categories as $category):
                        ?>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="cat-<?php echo $category['id']; ?>">
                                <label class="form-check-label" for="cat-<?php echo $category['id']; ?>">
                                    <?php echo htmlspecialchars($category['name']); ?>
                                    <span class="badge bg-light text-dark ms-1"><?php echo $category['product_count']; ?></span>
                                </label>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- Price Filter -->
                    <div class="mb-4">
                        <h6>Prix</h6>
                        <div class="row">
                            <div class="col-6">
                                <input type="number" class="form-control form-control-sm" placeholder="Min" id="price-min">
                            </div>
                            <div class="col-6">
                                <input type="number" class="form-control form-control-sm" placeholder="Max" id="price-max">
                            </div>
                        </div>
                        <button class="btn btn-sm btn-primary mt-2 w-100" onclick="applyPriceFilter()">Appliquer</button>
                    </div>
                </div>
            </div>

            <!-- Products Grid -->
            <div class="col-lg-9">
                <!-- Sort and View Options -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <span class="text-muted">Affichage de <?php echo count($products); ?> produits</span>
                    </div>
                    <div class="d-flex gap-2">
                        <select class="form-select form-select-sm" style="width: auto;" onchange="sortProducts(this.value)">
                            <option value="newest">Plus récents</option>
                            <option value="price_asc">Prix croissant</option>
                            <option value="price_desc">Prix décroissant</option>
                            <option value="name">Nom A-Z</option>
                        </select>
                    </div>
                </div>

                <!-- Products Grid -->
                <div class="row" id="products-grid">

                    <?php foreach ($products as $product): ?>
                        <div class="col-md-4 mb-4">
                            <div class="card product-card h-100">
                                <div class="position-relative">
                                    <?php
                                    // Handle both image_url and images fields
                                    $imageUrl = '';
                                    if (!empty($product['image_url'])) {
                                        $imageUrl = $product['image_url'];
                                    } elseif (!empty($product['images'])) {
                                        // If images is JSON, decode and get first image
                                        $images = json_decode($product['images'], true);
                                        if (is_array($images) && !empty($images)) {
                                            $imageUrl = $images[0];
                                        } elseif (is_string($product['images'])) {
                                            $imageUrl = $product['images'];
                                        }
                                    }
                                    ?>
                                    <?php if (!empty($imageUrl)): ?>
                                        <img src="<?php echo htmlspecialchars($imageUrl); ?>"
                                            class="card-img-top product-image" alt="<?php echo htmlspecialchars($product['name']); ?>">
                                    <?php else: ?>
                                        <div class="card-img-top product-image d-flex align-items-center justify-content-center">
                                            <i class="fas fa-image fa-3x text-muted"></i>
                                        </div>
                                    <?php endif; ?>

                                    <div class="position-absolute top-0 end-0 m-2">
                                        <button class="btn btn-sm btn-light rounded-circle" onclick="toggleWishlist(<?php echo $product['id']; ?>)">
                                            <i class="far fa-heart"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="card-body d-flex flex-column">
                                    <!-- Compact Product Info -->
                                    <h6 class="card-title mb-2"><?php echo htmlspecialchars($product['name']); ?></h6>

                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <span class="h6 text-primary mb-0"><?php echo number_format($product['price'], 2); ?> DZD</span>
                                        <?php if (!empty($product['categories'])): ?>
                                            <span class="badge bg-light text-dark small"><?php echo htmlspecialchars($product['categories']); ?></span>
                                        <?php endif; ?>
                                    </div>

                                    <!-- Compact Action Buttons -->
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-outline-primary btn-sm flex-grow-1"
                                            onclick="showProductDetails(<?php echo $product['id']; ?>)">
                                            <i class="fas fa-eye me-1"></i>Voir détail
                                        </button>
                                        <button class="btn btn-primary btn-sm"
                                            onclick="addToCart(<?php echo $product['id']; ?>)"
                                            title="Ajouter au panier">
                                            <i class="fas fa-shopping-cart"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Dynamic Pagination -->
                <?php
                // Calculate pagination
                $currentPage = (int)($_GET['page_num'] ?? 1);
                $productsPerPage = 24;

                // Get total product count for this store
                $totalQuery = "SELECT COUNT(*) as total FROM products WHERE store_id = ? AND status = 'active'";
                $totalStmt = $db->prepare($totalQuery);
                $totalStmt->execute([$store['id']]);
                $totalProducts = $totalStmt->fetch(PDO::FETCH_ASSOC)['total'];

                $totalPages = ceil($totalProducts / $productsPerPage);

                if ($totalPages > 1): ?>
                    <nav class="mt-4" aria-label="Navigation des produits">
                        <ul class="pagination justify-content-center">
                            <!-- Previous button -->
                            <li class="page-item <?php echo $currentPage <= 1 ? 'disabled' : ''; ?>">
                                <a class="page-link" href="<?php echo $currentPage > 1 ? '?store=medical-software-solutions&page=products&page_num=' . ($currentPage - 1) : '#'; ?>">
                                    Précédent
                                </a>
                            </li>

                            <!-- Page numbers -->
                            <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                <li class="page-item <?php echo $i == $currentPage ? 'active' : ''; ?>">
                                    <a class="page-link" href="?store=medical-software-solutions&page=products&page_num=<?php echo $i; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>

                            <!-- Next button -->
                            <li class="page-item <?php echo $currentPage >= $totalPages ? 'disabled' : ''; ?>">
                                <a class="page-link" href="<?php echo $currentPage < $totalPages ? '?store=medical-software-solutions&page=products&page_num=' . ($currentPage + 1) : '#'; ?>">
                                    Suivant
                                </a>
                            </li>
                        </ul>

                        <!-- Pagination info -->
                        <div class="text-center mt-2">
                            <small class="text-muted">
                                Page <?php echo $currentPage; ?> sur <?php echo $totalPages; ?>
                                (<?php echo $totalProducts; ?> produits au total)
                            </small>
                        </div>
                    </nav>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-light py-5 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5><?php echo htmlspecialchars($store['store_name']); ?></h5>
                    <p><?php echo htmlspecialchars($store['description'] ?? ''); ?></p>
                </div>
                <div class="col-md-4">
                    <h6>Liens rapides</h6>
                    <ul class="list-unstyled">
                        <li><a href="/store-frontend.php?store=medical-software-solutions" class="text-light text-decoration-none">Accueil</a></li>
                        <li><a href="/store-frontend.php?store=medical-software-solutions&page=products" class="text-light text-decoration-none">Produits</a></li>
                        <li><a href="/store-frontend.php?store=medical-software-solutions&page=about" class="text-light text-decoration-none">À propos</a></li>
                        <li><a href="/store-frontend.php?store=medical-software-solutions&page=contact" class="text-light text-decoration-none">Contact</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </footer>

    <!-- Product Details Modal -->
    <div class="modal fade" id="productDetailsModal" tabindex="-1" aria-labelledby="productDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="productDetailsModalLabel">Détails du produit</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="productDetailsContent">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                    <button type="button" class="btn btn-primary" id="addToCartFromModal">
                        <i class="fas fa-shopping-cart me-1"></i>Ajouter au panier
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function addToCart(productId) {
            // Add to cart functionality
            console.log('Adding product to cart:', productId);
            alert('Produit ajouté au panier!');
        }

        function toggleWishlist(productId) {
            // Wishlist functionality
            console.log('Toggle wishlist:', productId);
        }

        function sortProducts(sortBy) {
            // Reload page with sort parameter
            const url = new URL(window.location);
            url.searchParams.set('sort', sortBy);
            window.location.href = url.toString();
        }

        function applyPriceFilter() {
            const minPrice = document.getElementById('price-min').value;
            const maxPrice = document.getElementById('price-max').value;

            const url = new URL(window.location);
            if (minPrice) url.searchParams.set('min_price', minPrice);
            if (maxPrice) url.searchParams.set('max_price', maxPrice);

            window.location.href = url.toString();
        }

        // Product details functionality
        let currentProductId = null;
        const productDetailsModal = new bootstrap.Modal(document.getElementById('productDetailsModal'));

        function showProductDetails(productId) {
            currentProductId = productId;

            // Show modal with loading state
            productDetailsModal.show();

            // Load product details
            loadProductDetails(productId);
        }

        async function loadProductDetails(productId) {
            try {
                // Find product in current products array (passed from PHP)
                const products = <?php echo json_encode($products ?? []); ?>;
                const product = products.find(p => p.id == productId);

                if (!product) {
                    throw new Error('Produit non trouvé');
                }

                // Build product details HTML
                const detailsHtml = `
                    <div class="row">
                        <div class="col-md-6">
                            ${product.images || product.image_url ?
                                `<img src="${product.images || product.image_url}" class="img-fluid rounded" alt="${product.name}">` :
                                `<div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 300px;">
                                    <i class="fas fa-image fa-4x text-muted"></i>
                                </div>`
                            }
                        </div>
                        <div class="col-md-6">
                            <h4>${product.name}</h4>
                            <p class="text-muted">${product.categories || 'Aucune catégorie'}</p>
                            <h5 class="text-primary">${parseFloat(product.price).toLocaleString('fr-FR', {minimumFractionDigits: 2})} DZD</h5>

                            <div class="mt-3">
                                <h6>Description:</h6>
                                <div class="description-content">
                                    ${product.description || 'Aucune description disponible'}
                                </div>
                            </div>

                            ${product.sku ? `<p class="mt-3"><strong>SKU:</strong> ${product.sku}</p>` : ''}
                            ${product.stock ? `<p><strong>Stock:</strong> ${product.stock} unités disponibles</p>` : ''}
                        </div>
                    </div>
                `;

                document.getElementById('productDetailsContent').innerHTML = detailsHtml;

                // Update add to cart button in modal
                document.getElementById('addToCartFromModal').onclick = () => {
                    addToCart(productId);
                    productDetailsModal.hide();
                };

            } catch (error) {
                console.error('Error loading product details:', error);
                document.getElementById('productDetailsContent').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Erreur lors du chargement des détails du produit: ${error.message}
                    </div>
                `;
            }
        }
    </script>
</body>

</html>
