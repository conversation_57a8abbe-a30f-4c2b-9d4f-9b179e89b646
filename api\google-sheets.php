<?php
header('Content-Type: application/json');
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
  require_once '../config/database.php';
} catch (Exception $e) {
  http_response_code(500);
  echo json_encode(['success' => false, 'message' => 'Database connection error: ' . $e->getMessage()]);
  exit;
}

// Vérification de l'authentification
if (!isset($_SERVER['HTTP_AUTHORIZATION']) || $_SERVER['HTTP_AUTHORIZATION'] !== 'Bearer demo_token') {
  http_response_code(401);
  echo json_encode(['success' => false, 'message' => 'Non autorisé']);
  exit;
}

// Vérification du store_id
$store_id = isset($_GET['store_id']) ? intval($_GET['store_id']) : null;
if (!$store_id) {
  http_response_code(400);
  echo json_encode(['success' => false, 'message' => 'store_id manquant']);
  exit;
}

// Connexion à la base de données
try {
  $database = new Database();
  $pdo = $database->getConnection();
} catch (Exception $e) {
  http_response_code(500);
  echo json_encode(['success' => false, 'message' => 'Erreur de connexion à la base de données']);
  exit;
}

// Gestion des actions
$action = $_GET['action'] ?? '';

switch ($action) {
  case 'export_products':
    exportProducts($pdo, $store_id);
    break;

  case 'export_sales':
    exportSales($pdo, $store_id);
    break;

  case 'generate_script':
    generateScript($store_id);
    break;

  case 'create_integration':
    createIntegration($pdo, $store_id);
    break;

  case 'list_integrations':
    listIntegrations($pdo, $store_id);
    break;

  case 'preview_script':
    previewScript($store_id);
    break;

  default:
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Action non valide']);
    break;
}

// Fonction d'export des produits
function exportProducts($pdo, $store_id)
{
  try {
    $stmt = $pdo->prepare(
      "SELECT p.*, c.name as category_name
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            WHERE p.store_id = ?"
    );
    $stmt->execute([$store_id]);
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Simulation de création d'un Google Sheet
    $sheet_url = "https://docs.google.com/spreadsheets/d/example-" . uniqid();

    echo json_encode([
      'success' => true,
      'message' => 'Produits exportés avec succès',
      'sheet_url' => $sheet_url,
      'products_count' => count($products)
    ]);
  } catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Erreur lors de l\'export des produits']);
  }
}

// Fonction d'export des ventes
function exportSales($pdo, $store_id)
{
  try {
    // Requête pour obtenir les statistiques de vente
    $stmt = $pdo->prepare(
      "SELECT
                COUNT(*) as total_orders,
                SUM(total_amount) as total_revenue,
                DATE(created_at) as order_date
            FROM orders
            WHERE store_id = ?
            GROUP BY DATE(created_at)
            ORDER BY order_date DESC"
    );
    $stmt->execute([$store_id]);
    $sales = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Simulation de création d'un Google Sheet
    $sheet_url = "https://docs.google.com/spreadsheets/d/example-" . uniqid();

    echo json_encode([
      'success' => true,
      'message' => 'Rapport de ventes généré avec succès',
      'sheet_url' => $sheet_url,
      'sales_count' => count($sales)
    ]);
  } catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Erreur lors de la génération du rapport']);
  }
}

// Fonction de génération de script
function generateScript($store_id)
{
  // Récupération des événements sélectionnés
  $data = json_decode(file_get_contents('php://input'), true);
  $events = [
    'orders' => $data['orders'] ?? false,
    'inventory' => $data['inventory'] ?? false,
    'customers' => $data['customers'] ?? false
  ];

  // Génération du script Google Apps
  $script = generateGoogleAppsScript($store_id, $events);

  echo json_encode([
    'success' => true,
    'message' => 'Script généré avec succès',
    'script_content' => $script
  ]);
}

// Fonction de génération du script Google Apps
function generateGoogleAppsScript($store_id, $events)
{
  $api_url = "https://" . $_SERVER['HTTP_HOST'];
  $webhook_url = $api_url . "/api/google-sheets-webhook.php";

  // Convert PHP boolean values to JavaScript boolean strings
  $orders_enabled = isset($events['orders']) && $events['orders'] ? 'true' : 'false';
  $inventory_enabled = isset($events['inventory']) && $events['inventory'] ? 'true' : 'false';
  $customers_enabled = isset($events['customers']) && $events['customers'] ? 'true' : 'false';
  $events_enabled = isset($events['events']) && $events['events'] ? 'true' : 'false';

  $script = <<<EOT
// Configuration
const STORE_ID = '$store_id';
const API_URL = '$api_url';
const WEBHOOK_URL = '$webhook_url';
const API_TOKEN = 'demo_token';

// Fonction principale de synchronisation
function synchronizeData() {
  const sheet = SpreadsheetApp.getActiveSpreadsheet();

  try {
    Logger.log('🔄 Début de la synchronisation pour le magasin: ' + STORE_ID);

    // Création des onglets nécessaires
    if ($orders_enabled) {
      syncOrders(sheet);
    }
    if ($inventory_enabled) {
      syncInventory(sheet);
    }
    if ($customers_enabled) {
      syncCustomers(sheet);
    }
    if ($events_enabled) {
      syncEvents(sheet);
    }

    // Mettre à jour le timestamp de dernière sync
    updateLastSyncTime(sheet);

    Logger.log('✅ Synchronisation terminée avec succès');
  } catch (error) {
    Logger.log('❌ Erreur lors de la synchronisation: ' + error.toString());
    throw error;
  }
}

// Fonction de synchronisation automatique (à programmer)
function autoSync() {
  synchronizeData();
}

// Configuration des triggers automatiques
function setupTriggers() {
  // Supprimer les anciens triggers
  ScriptApp.getProjectTriggers().forEach(trigger => {
    if (trigger.getHandlerFunction() === 'autoSync') {
      ScriptApp.deleteTrigger(trigger);
    }
  });

  // Créer un nouveau trigger toutes les heures
  ScriptApp.newTrigger('autoSync')
    .timeBased()
    .everyHours(1)
    .create();

  Logger.log('✅ Triggers configurés pour synchronisation automatique');
}

// Synchronisation des commandes avec workflow de confirmation
function syncOrders(sheet) {
  const ordersSheet = getOrCreateSheet(sheet, 'Commandes');

  // Configurer les en-têtes spécialisés pour la confirmation d'agent
  setupOrdersHeaders(ordersSheet);

  const response = fetchAPI('/api/orders.php?store_id=' + STORE_ID);
  if (response && response.success) {
    writeOrdersData(ordersSheet, response.orders);

    // Configurer la validation des données et les formules
    setupOrdersValidation(ordersSheet);

    // Configurer les triggers pour les mises à jour
    setupOrdersOnEdit(ordersSheet);
  }
}

// Configuration des en-têtes pour les commandes
function setupOrdersHeaders(sheet) {
  const headers = [
    'ID Commande', 'Numéro', 'Date', 'Client', 'Email', 'Téléphone',
    'Produit(s)', 'Quantité', 'Prix Total', 'Wilaya', 'Commune', 'Adresse',
    'Statut Système', 'STATUT AGENT', 'CONFIRMÉ PAR', 'DATE CONFIRMATION',
    'NOTES AGENT', 'PRIORITÉ', 'SUIVI LIVRAISON', 'DATE LIVRAISON'
  ];

  const headerRange = sheet.getRange(1, 1, 1, headers.length);
  headerRange.setValues([headers]);

  // Formatage des en-têtes
  headerRange.setBackground('#4285f4');
  headerRange.setFontColor('white');
  headerRange.setFontWeight('bold');

  // Formatage spécial pour les colonnes d'agent
  const agentColumns = sheet.getRange(1, 14, 1, 7); // Colonnes N à T
  agentColumns.setBackground('#ff9800');
  agentColumns.setFontColor('white');
}

// Écriture des données de commandes
function writeOrdersData(sheet, orders) {
  if (!orders || orders.length === 0) return;

  // Préparer les données avec colonnes d'agent
  const data = orders.map(order => [
    order.id,
    order.order_number,
    order.created_at,
    order.customer_name,
    order.customer_email,
    order.customer_phone || '',
    order.items_summary || '',
    order.total_quantity || 1,
    order.total_amount,
    order.shipping_wilaya || '',
    order.shipping_commune || '',
    order.shipping_address || '',
    order.status,
    '', // STATUT AGENT - à remplir par l'agent
    '', // CONFIRMÉ PAR
    '', // DATE CONFIRMATION
    '', // NOTES AGENT
    'NORMALE', // PRIORITÉ par défaut
    '', // SUIVI LIVRAISON
    '' // DATE LIVRAISON
  ]);

  if (data.length > 0) {
    const dataRange = sheet.getRange(2, 1, data.length, data[0].length);
    dataRange.setValues(data);

    // Formatage conditionnel pour les statuts
    setupConditionalFormatting(sheet, data.length);
  }
}

// Configuration de la validation des données
function setupOrdersValidation(sheet) {
  const lastRow = sheet.getLastRow();
  if (lastRow < 2) return;

  // Validation pour la colonne STATUT AGENT (colonne N)
  const statusRule = SpreadsheetApp.newDataValidation()
    .requireValueInList(['EN ATTENTE', 'CONFIRMÉ', 'ANNULÉ', 'LIVRÉ', 'RETOURNÉ'])
    .setAllowInvalid(false)
    .setHelpText('Sélectionnez le statut de confirmation')
    .build();

  sheet.getRange(2, 14, lastRow - 1, 1).setDataValidation(statusRule);

  // Validation pour la colonne PRIORITÉ (colonne R)
  const priorityRule = SpreadsheetApp.newDataValidation()
    .requireValueInList(['NORMALE', 'URGENTE', 'TRÈS URGENTE'])
    .setAllowInvalid(false)
    .build();

  sheet.getRange(2, 18, lastRow - 1, 1).setDataValidation(priorityRule);
}

// Configuration du formatage conditionnel
function setupConditionalFormatting(sheet, dataRows) {
  // Formatage pour les statuts d'agent
  const statusRange = sheet.getRange(2, 14, dataRows, 1);

  // Vert pour CONFIRMÉ
  const confirmedRule = SpreadsheetApp.newConditionalFormatRule()
    .whenTextEqualTo('CONFIRMÉ')
    .setBackground('#d4edda')
    .setFontColor('#155724')
    .setRanges([statusRange])
    .build();

  // Rouge pour ANNULÉ
  const cancelledRule = SpreadsheetApp.newConditionalFormatRule()
    .whenTextEqualTo('ANNULÉ')
    .setBackground('#f8d7da')
    .setFontColor('#721c24')
    .setRanges([statusRange])
    .build();

  // Bleu pour LIVRÉ
  const deliveredRule = SpreadsheetApp.newConditionalFormatRule()
    .whenTextEqualTo('LIVRÉ')
    .setBackground('#cce5ff')
    .setFontColor('#004085')
    .setRanges([statusRange])
    .build();

  sheet.setConditionalFormatRules([confirmedRule, cancelledRule, deliveredRule]);
}

// Fonction appelée lors de l'édition de cellules
function onEdit(e) {
  const sheet = e.source.getActiveSheet();
  const range = e.range;

  // Vérifier si c'est une modification dans les colonnes d'agent
  if (sheet.getName() === 'Commandes' && range.getColumn() >= 14 && range.getColumn() <= 20) {
    handleOrderStatusUpdate(sheet, range);
  }

  if (sheet.getName() === 'Événements' && range.getColumn() >= 8) {
    handleEventStatusUpdate(sheet, range);
  }
}

// Gestion des mises à jour de statut de commande
function handleOrderStatusUpdate(sheet, range) {
  const row = range.getRow();
  if (row < 2) return; // Ignorer les en-têtes

  const orderId = sheet.getRange(row, 1).getValue();
  const newStatus = range.getValue();
  const column = range.getColumn();

  // Si c'est la colonne STATUT AGENT (14)
  if (column === 14 && newStatus) {
    // Mettre à jour automatiquement les colonnes de confirmation
    const currentUser = Session.getActiveUser().getEmail();
    const currentDate = new Date();

    sheet.getRange(row, 15).setValue(currentUser); // CONFIRMÉ PAR
    sheet.getRange(row, 16).setValue(currentDate); // DATE CONFIRMATION

    // Synchroniser avec l'API
    syncOrderStatusToAPI(orderId, newStatus, currentUser);
  }
}

// Synchronisation du statut vers l'API
function syncOrderStatusToAPI(orderId, status, confirmedBy) {
  try {
    const payload = {
      order_id: orderId,
      agent_status: status,
      confirmed_by: confirmedBy,
      confirmed_at: new Date().toISOString()
    };

    const options = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + API_TOKEN
      },
      payload: JSON.stringify(payload)
    };

    const response = UrlFetchApp.fetch(WEBHOOK_URL + '?action=update_order_status', options);
    const result = JSON.parse(response.getContentText());

    if (!result.success) {
      Logger.log('⚠️ Erreur lors de la synchronisation du statut: ' + result.message);
    }
  } catch (error) {
    Logger.log('❌ Erreur de synchronisation API: ' + error.toString());
  }
}

// Mise à jour du timestamp de dernière synchronisation
function updateLastSyncTime(sheet) {
  const infoSheet = getOrCreateSheet(sheet, 'Info Sync');
  infoSheet.getRange(1, 1).setValue('Dernière synchronisation:');
  infoSheet.getRange(1, 2).setValue(new Date());
  infoSheet.getRange(2, 1).setValue('Magasin ID:');
  infoSheet.getRange(2, 2).setValue(STORE_ID);
}

// Synchronisation de l'inventaire
function syncInventory(sheet) {
  const inventorySheet = getOrCreateSheet(sheet, 'Inventaire');
  const response = fetchAPI('/api/products.php?store_id=' + STORE_ID);
  if (response && response.success) {
    writeDataToSheet(inventorySheet, response.products);
  }
}

// Synchronisation des clients
function syncCustomers(sheet) {
  const customersSheet = getOrCreateSheet(sheet, 'Clients');
  const response = fetchAPI('/api/customers.php?store_id=' + STORE_ID);
  if (response && response.success) {
    writeDataToSheet(customersSheet, response.customers);
  }
}

// Synchronisation des événements et inscriptions
function syncEvents(sheet) {
  const eventsSheet = getOrCreateSheet(sheet, 'Événements');

  // Configurer les en-têtes pour les événements
  setupEventsHeaders(eventsSheet);

  const response = fetchAPI('/api/events.php?store_id=' + STORE_ID);
  if (response && response.success) {
    writeEventsData(eventsSheet, response.events);
    setupEventsValidation(eventsSheet);
  }
}

// Configuration des en-têtes pour les événements
function setupEventsHeaders(sheet) {
  const headers = [
    'ID Inscription', 'Événement', 'Date Événement', 'Nom Client',
    'Email', 'Téléphone', 'Date Inscription', 'STATUT AGENT',
    'CONFIRMÉ PAR', 'DATE CONFIRMATION', 'NOTES AGENT', 'PRÉSENT'
  ];

  const headerRange = sheet.getRange(1, 1, 1, headers.length);
  headerRange.setValues([headers]);

  // Formatage des en-têtes
  headerRange.setBackground('#673ab7');
  headerRange.setFontColor('white');
  headerRange.setFontWeight('bold');

  // Formatage spécial pour les colonnes d'agent
  const agentColumns = sheet.getRange(1, 8, 1, 5); // Colonnes H à L
  agentColumns.setBackground('#ff5722');
  agentColumns.setFontColor('white');
}

// Écriture des données d'événements
function writeEventsData(sheet, events) {
  if (!events || events.length === 0) return;

  const data = events.map(event => [
    event.id,
    event.event_name,
    event.event_date,
    event.customer_name,
    event.customer_email,
    event.customer_phone || '',
    event.registered_at,
    '', // STATUT AGENT
    '', // CONFIRMÉ PAR
    '', // DATE CONFIRMATION
    '', // NOTES AGENT
    '' // PRÉSENT
  ]);

  if (data.length > 0) {
    const dataRange = sheet.getRange(2, 1, data.length, data[0].length);
    dataRange.setValues(data);

    // Formatage conditionnel pour les événements
    setupEventsConditionalFormatting(sheet, data.length);
  }
}

// Configuration de la validation pour les événements
function setupEventsValidation(sheet) {
  const lastRow = sheet.getLastRow();
  if (lastRow < 2) return;

  // Validation pour STATUT AGENT
  const statusRule = SpreadsheetApp.newDataValidation()
    .requireValueInList(['EN ATTENTE', 'CONFIRMÉ', 'ANNULÉ', 'REPORTÉ'])
    .setAllowInvalid(false)
    .setHelpText('Statut de confirmation de l\'inscription')
    .build();

  sheet.getRange(2, 8, lastRow - 1, 1).setDataValidation(statusRule);

  // Validation pour PRÉSENT
  const presentRule = SpreadsheetApp.newDataValidation()
    .requireValueInList(['OUI', 'NON', 'ABSENT'])
    .setAllowInvalid(false)
    .build();

  sheet.getRange(2, 12, lastRow - 1, 1).setDataValidation(presentRule);
}

// Formatage conditionnel pour les événements
function setupEventsConditionalFormatting(sheet, dataRows) {
  const statusRange = sheet.getRange(2, 8, dataRows, 1);

  const confirmedRule = SpreadsheetApp.newConditionalFormatRule()
    .whenTextEqualTo('CONFIRMÉ')
    .setBackground('#d4edda')
    .setFontColor('#155724')
    .setRanges([statusRange])
    .build();

  const cancelledRule = SpreadsheetApp.newConditionalFormatRule()
    .whenTextEqualTo('ANNULÉ')
    .setBackground('#f8d7da')
    .setFontColor('#721c24')
    .setRanges([statusRange])
    .build();

  sheet.setConditionalFormatRules([confirmedRule, cancelledRule]);
}

// Gestion des mises à jour de statut d'événement
function handleEventStatusUpdate(sheet, range) {
  const row = range.getRow();
  if (row < 2) return;

  const eventId = sheet.getRange(row, 1).getValue();
  const newStatus = range.getValue();
  const column = range.getColumn();

  // Si c'est la colonne STATUT AGENT (8)
  if (column === 8 && newStatus) {
    const currentUser = Session.getActiveUser().getEmail();
    const currentDate = new Date();

    sheet.getRange(row, 9).setValue(currentUser); // CONFIRMÉ PAR
    sheet.getRange(row, 10).setValue(currentDate); // DATE CONFIRMATION

    // Synchroniser avec l'API
    syncEventStatusToAPI(eventId, newStatus, currentUser);
  }
}

// Synchronisation du statut d'événement vers l'API
function syncEventStatusToAPI(eventId, status, confirmedBy) {
  try {
    const payload = {
      event_id: eventId,
      agent_status: status,
      confirmed_by: confirmedBy,
      confirmed_at: new Date().toISOString()
    };

    const options = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + API_TOKEN
      },
      payload: JSON.stringify(payload)
    };

    const response = UrlFetchApp.fetch(WEBHOOK_URL + '?action=update_event_status', options);
    const result = JSON.parse(response.getContentText());

    if (!result.success) {
      Logger.log('⚠️ Erreur lors de la synchronisation du statut événement: ' + result.message);
    }
  } catch (error) {
    Logger.log('❌ Erreur de synchronisation événement API: ' + error.toString());
  }
}

// Utilitaires
function fetchAPI(endpoint) {
  const options = {
    'method': 'get',
    'headers': {
      'Authorization': 'Bearer ' + API_TOKEN
    }
  };
  try {
    const response = UrlFetchApp.fetch(API_URL + endpoint, options);
    return JSON.parse(response.getContentText());
  } catch (error) {
    Logger.log('Erreur API: ' + error);
    return null;
  }
}

function getOrCreateSheet(spreadsheet, sheetName) {
  let sheet = spreadsheet.getSheetByName(sheetName);
  if (!sheet) {
    sheet = spreadsheet.insertSheet(sheetName);
  }
  return sheet;
}

function writeDataToSheet(sheet, data) {
  if (!data || !data.length) return;

  // En-têtes
  const headers = Object.keys(data[0]);
  sheet.getRange(1, 1, 1, headers.length).setValues([headers]);

  // Données
  const values = data.map(row => headers.map(header => row[header]));
  sheet.getRange(2, 1, values.length, headers.length).setValues(values);

  // Formatage
  sheet.autoResizeColumns(1, headers.length);
}

// Création du menu personnalisé
function onOpen() {
  const ui = SpreadsheetApp.getUi();
  ui.createMenu('Synchronisation')
    .addItem('Synchroniser les données', 'synchronizeData')
    .addToUi();
}
EOT;

  return $script;
}

// Fonction pour créer une nouvelle intégration
function createIntegration($pdo, $store_id)
{
  $input = json_decode(file_get_contents('php://input'), true);

  // Validation
  if (!isset($input['integration_type']) || !isset($input['integration_name'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Type et nom d\'intégration requis']);
    return;
  }

  try {
    // Récupérer l'ID du marchand depuis la session
    session_start();
    if (!isset($_SESSION['user'])) {
      http_response_code(401);
      echo json_encode(['success' => false, 'message' => 'Non authentifié']);
      return;
    }
    $merchant_id = $_SESSION['user']['id'];

    // Insérer l'intégration
    $sql = "
            INSERT INTO merchant_google_sheets (
                merchant_id, store_id, integration_name, integration_type,
                sheet_url, sync_frequency, config, script_generated_at, script_version
            ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), '1.0')
        ";

    $stmt = $pdo->prepare($sql);
    $result = $stmt->execute([
      $merchant_id,
      $store_id,
      $input['integration_name'],
      $input['integration_type'],
      $input['sheet_url'] ?? null,
      $input['sync_frequency'] ?? 'manual',
      json_encode($input['options'] ?? [])
    ]);

    if ($result) {
      $integration_id = $pdo->lastInsertId();

      // Générer le script
      $events = [$input['integration_type'] => true];
      if (isset($input['options']['events'])) {
        $events['events'] = true;
      }

      $script = generateGoogleAppsScript($store_id, $events);

      echo json_encode([
        'success' => true,
        'message' => 'Intégration créée avec succès',
        'integration_id' => $integration_id,
        'script_content' => $script
      ]);
    } else {
      http_response_code(500);
      echo json_encode(['success' => false, 'message' => 'Erreur lors de la création']);
    }
  } catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Erreur de base de données']);
  }
}

// Fonction pour lister les intégrations
function listIntegrations($pdo, $store_id)
{
  try {
    session_start();
    if (!isset($_SESSION['user'])) {
      http_response_code(401);
      echo json_encode(['success' => false, 'message' => 'Non authentifié']);
      return;
    }
    $merchant_id = $_SESSION['user']['id'];

    $sql = "
            SELECT * FROM merchant_google_sheets
            WHERE merchant_id = ?
        ";
    $params = [$merchant_id];

    if ($store_id) {
      $sql .= " AND (store_id = ? OR store_id IS NULL)";
      $params[] = $store_id;
    }

    $sql .= " ORDER BY created_at DESC";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $integrations = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Décoder les configurations JSON
    foreach ($integrations as &$integration) {
      if ($integration['config']) {
        $integration['config'] = json_decode($integration['config'], true);
      }
    }

    echo json_encode([
      'success' => true,
      'data' => $integrations
    ]);
  } catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Erreur lors du chargement']);
  }
}

// Fonction pour prévisualiser un script
function previewScript($store_id)
{
  $input = json_decode(file_get_contents('php://input'), true);

  if (!isset($input['integration_type'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Type d\'intégration requis']);
    return;
  }

  // Générer le script basé sur le type
  $events = [$input['integration_type'] => true];
  if (isset($input['options']['events'])) {
    $events['events'] = true;
  }

  $script = generateGoogleAppsScript($store_id, $events);

  echo json_encode([
    'success' => true,
    'script_content' => $script
  ]);
}
