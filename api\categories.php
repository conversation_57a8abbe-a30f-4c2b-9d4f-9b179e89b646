<?php
// Disable error display for JSON API
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../dashboard/includes/database.php';

try {
    $db = connectDB();

    $method = $_SERVER['REQUEST_METHOD'];
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    $merchant_id = isset($_GET['merchant_id']) ? (int)$_GET['merchant_id'] : null;
    $lang = isset($_GET['lang']) ? $_GET['lang'] : 'en';

    // Simple auth check
    $headers = function_exists('getallheaders') ? getallheaders() : [];
    if (empty($headers) && isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $headers['Authorization'] = $_SERVER['HTTP_AUTHORIZATION'];
    }

    $token = isset($headers['Authorization']) ? str_replace('Bearer ', '', $headers['Authorization']) : null;

    switch ($method) {
        case 'GET':
            if ($action === 'all') {
                getAllCategories($db, $merchant_id, $lang);
            } elseif ($action === 'limits') {
                getCategoryLimits($db, $merchant_id);
            } elseif ($action === 'count') {
                getCategoryCount($db, $merchant_id);
            } elseif ($action === 'user-categories') {
                getUserCategories($db);
            } else {
                // Support pour le dashboard vendeur
                $storeId = $_GET['store_id'] ?? null;
                if ($storeId) {
                    getStoreCategories($db, $storeId);
                } else {
                    http_response_code(404);
                    echo json_encode(['error' => 'Action non supportée']);
                }
            }
            break;

        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            if ($action === 'create') {
                createCategory($db, $merchant_id, $input, $lang);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Action non supportée']);
            }
            break;

        case 'PUT':
            $input = json_decode(file_get_contents('php://input'), true);
            $id = isset($_GET['id']) ? (int)$_GET['id'] : null;
            if ($action === 'update' && $id) {
                updateCategory($db, $id, $merchant_id, $input, $lang);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Action non supportée']);
            }
            break;

        case 'DELETE':
            $id = isset($_GET['id']) ? (int)$_GET['id'] : null;
            if ($action === 'delete' && $id) {
                deleteCategory($db, $id, $merchant_id);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Action non supportée']);
            }
            break;

        default:
            http_response_code(405);
            echo json_encode(['error' => 'Méthode non supportée']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erreur serveur: ' . $e->getMessage()]);
}

/**
 * Get all categories with translations
 */
function getAllCategories($db, $merchant_id, $lang)
{
    try {
        $nameField = "name_$lang";
        $descField = "description_$lang";

        $query = "
            SELECT
                c.id,
                c.name,
                COALESCE(c.$nameField, c.name) as translated_name,
                COALESCE(c.$descField, c.description) as translated_description,
                c.slug,
                c.color,
                c.icon,
                c.parent_id,
                c.category_type,
                c.is_active,
                c.is_default,
                c.sort_order,
                c.created_at,
                COUNT(pc.product_id) as product_count
            FROM product_categories c
            LEFT JOIN product_categories pc ON c.id = pc.category_id
            WHERE (c.merchant_id = ? OR c.is_default = 1)
            GROUP BY c.id
            ORDER BY c.sort_order, c.translated_name
        ";

        $stmt = $db->prepare($query);
        $stmt->execute([$merchant_id]);
        $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Organiser en arbre (catégories et sous-catégories)
        $tree = [];
        $subcategories = [];

        foreach ($categories as $category) {
            if ($category['parent_id'] === null) {
                $category['subcategories'] = [];
                $tree[$category['id']] = $category;
            } else {
                $subcategories[] = $category;
            }
        }

        // Ajouter les sous-catégories à leurs parents
        foreach ($subcategories as $subcategory) {
            if (isset($tree[$subcategory['parent_id']])) {
                $tree[$subcategory['parent_id']]['subcategories'][] = $subcategory;
            }
        }

        echo json_encode([
            'success' => true,
            'data' => [
                'categories' => array_values($tree),
                'total' => count($categories),
                'language' => $lang
            ]
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération des catégories: ' . $e->getMessage()]);
    }
}

/**
 * Get category limits for merchant based on subscription
 */
function getCategoryLimits($db, $merchant_id)
{
    try {
        // Get merchant's subscription limits
        $query = "
            SELECT
                s.max_categories,
                s.max_subcategories,
                us.current_categories,
                us.current_subcategories
            FROM user_subscriptions us
            JOIN subscriptions s ON us.subscription_id = s.id
            WHERE us.user_id = ? AND us.status = 'active'
            ORDER BY us.created_at DESC
            LIMIT 1
        ";

        $stmt = $db->prepare($query);
        $stmt->execute([$merchant_id]);
        $limits = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$limits) {
            // Default limits for free plan
            $limits = [
                'max_categories' => 5,
                'max_subcategories' => 10,
                'current_categories' => 0,
                'current_subcategories' => 0
            ];
        }

        // Get current usage
        $countQuery = "
            SELECT
                COUNT(CASE WHEN parent_id IS NULL THEN 1 END) as current_categories,
                COUNT(CASE WHEN parent_id IS NOT NULL THEN 1 END) as current_subcategories
            FROM product_categories
            WHERE merchant_id = ? AND is_active = 1
        ";

        $stmt = $db->prepare($countQuery);
        $stmt->execute([$merchant_id]);
        $usage = $stmt->fetch(PDO::FETCH_ASSOC);

        $limits['current_categories'] = (int)$usage['current_categories'];
        $limits['current_subcategories'] = (int)$usage['current_subcategories'];
        $limits['can_create_category'] = $limits['current_categories'] < $limits['max_categories'];
        $limits['can_create_subcategory'] = $limits['current_subcategories'] < $limits['max_subcategories'];

        echo json_encode([
            'success' => true,
            'data' => $limits
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération des limites: ' . $e->getMessage()]);
    }
}

/**
 * Create new category with limit check
 */
function createCategory($db, $merchant_id, $data, $lang)
{
    try {
        // Check limits first
        $limitsQuery = "
            SELECT
                s.max_categories,
                s.max_subcategories
            FROM user_subscriptions us
            JOIN subscriptions s ON us.subscription_id = s.id
            WHERE us.user_id = ? AND us.status = 'active'
            ORDER BY us.created_at DESC
            LIMIT 1
        ";

        $stmt = $db->prepare($limitsQuery);
        $stmt->execute([$merchant_id]);
        $limits = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$limits) {
            $limits = ['max_categories' => 5, 'max_subcategories' => 10];
        }

        // Check current usage
        $countQuery = "
            SELECT
                COUNT(CASE WHEN parent_id IS NULL THEN 1 END) as current_categories,
                COUNT(CASE WHEN parent_id IS NOT NULL THEN 1 END) as current_subcategories
            FROM product_categories
            WHERE merchant_id = ? AND is_active = 1
        ";

        $stmt = $db->prepare($countQuery);
        $stmt->execute([$merchant_id]);
        $usage = $stmt->fetch(PDO::FETCH_ASSOC);

        $isSubcategory = !empty($data['parent_id']);

        if ($isSubcategory && $usage['current_subcategories'] >= $limits['max_subcategories']) {
            http_response_code(400);
            echo json_encode([
                'error' => 'Limite de sous-catégories atteinte',
                'limit' => $limits['max_subcategories'],
                'current' => $usage['current_subcategories']
            ]);
            return;
        }

        if (!$isSubcategory && $usage['current_categories'] >= $limits['max_categories']) {
            http_response_code(400);
            echo json_encode([
                'error' => 'Limite de catégories atteinte',
                'limit' => $limits['max_categories'],
                'current' => $usage['current_categories']
            ]);
            return;
        }

        // Create category
        $insertQuery = "
            INSERT INTO product_categories (
                merchant_id, parent_id, name, name_ar, name_en, name_fr,
                slug, description, description_ar, description_en, description_fr,
                color, icon, category_type, is_active, sort_order
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1,
                (SELECT COALESCE(MAX(sort_order), 0) + 1 FROM product_categories c2 WHERE c2.merchant_id = ?)
            )
        ";

        $slug = strtolower(str_replace(' ', '-', $data['name']));
        $categoryType = $isSubcategory ? 'subcategory' : 'category';

        $stmt = $db->prepare($insertQuery);
        $stmt->execute([
            $merchant_id,
            $data['parent_id'] ?? null,
            $data['name'],
            $data['name_ar'] ?? '',
            $data['name_en'] ?? $data['name'],
            $data['name_fr'] ?? '',
            $slug,
            $data['description'] ?? '',
            $data['description_ar'] ?? '',
            $data['description_en'] ?? $data['description'] ?? '',
            $data['description_fr'] ?? '',
            $data['color'] ?? '#007bff',
            $data['icon'] ?? 'fas fa-tag',
            $categoryType,
            $merchant_id
        ]);

        $categoryId = $db->lastInsertId();

        echo json_encode([
            'success' => true,
            'data' => [
                'id' => $categoryId,
                'message' => 'Catégorie créée avec succès'
            ]
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la création: ' . $e->getMessage()]);
    }
}

/**
 * Get categories for a specific store (for seller dashboard)
 */
function getStoreCategories($db, $storeId)
{
    try {
        // First, get the merchant_id from the store_id
        $storeQuery = "SELECT merchant_id FROM stores WHERE id = ?";
        $storeStmt = $db->prepare($storeQuery);
        $storeStmt->execute([$storeId]);
        $store = $storeStmt->fetch(PDO::FETCH_ASSOC);

        if (!$store) {
            http_response_code(404);
            echo json_encode(['error' => 'Store non trouvé']);
            return;
        }

        $merchantId = $store['merchant_id'];

        // Get categories for this merchant
        $query = "
            SELECT
                id,
                name,
                name_ar,
                name_en,
                name_fr,
                description,
                description_ar,
                description_en,
                description_fr,
                color,
                icon,
                parent_id,
                category_type,
                is_active,
                sort_order,
                created_at
            FROM product_categories
            WHERE merchant_id = ? AND is_active = 1
            ORDER BY sort_order ASC, name ASC
        ";

        $stmt = $db->prepare($query);
        $stmt->execute([$merchantId]);
        $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode([
            'success' => true,
            'data' => $categories
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors du chargement des catégories: ' . $e->getMessage()]);
    }
}

/**
 * Get category count for a merchant
 */
function getCategoryCount($db, $merchant_id)
{
    try {
        $query = "
            SELECT
                COUNT(CASE WHEN parent_id IS NULL THEN 1 END) as categories,
                COUNT(CASE WHEN parent_id IS NOT NULL THEN 1 END) as subcategories
            FROM product_categories
            WHERE merchant_id = ? AND is_active = 1
        ";

        $stmt = $db->prepare($query);
        $stmt->execute([$merchant_id]);
        $counts = $stmt->fetch(PDO::FETCH_ASSOC);

        echo json_encode([
            'success' => true,
            'data' => $counts
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération du nombre: ' . $e->getMessage()]);
    }
}

/**
 * Update a category
 */
function updateCategory($db, $id, $merchant_id, $data, $lang)
{
    try {
        // Check if category exists and belongs to merchant
        $checkQuery = "SELECT id FROM product_categories WHERE id = ? AND merchant_id = ?";
        $checkStmt = $db->prepare($checkQuery);
        $checkStmt->execute([$id, $merchant_id]);
        $category = $checkStmt->fetch();

        if (!$category) {
            http_response_code(404);
            echo json_encode(['error' => 'Catégorie non trouvée']);
            return;
        }

        $updateQuery = "
            UPDATE product_categories
            SET
                name = ?,
                name_ar = ?,
                name_en = ?,
                name_fr = ?,
                description = ?,
                description_ar = ?,
                description_en = ?,
                description_fr = ?,
                color = ?,
                icon = ?,
                is_active = ?,
                sort_order = ?
            WHERE id = ?
        ";

        $stmt = $db->prepare($updateQuery);
        $stmt->execute([
            $data['name'] ?? '',
            $data['name_ar'] ?? '',
            $data['name_en'] ?? $data['name'] ?? '',
            $data['name_fr'] ?? '',
            $data['description'] ?? '',
            $data['description_ar'] ?? '',
            $data['description_en'] ?? $data['description'] ?? '',
            $data['description_fr'] ?? '',
            $data['color'] ?? '#007bff',
            $data['icon'] ?? 'fas fa-tag',
            $data['is_active'] ?? 1,
            $data['sort_order'] ?? 0,
            $id
        ]);

        echo json_encode([
            'success' => true,
            'data' => [
                'message' => 'Catégorie mise à jour avec succès'
            ]
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la mise à jour: ' . $e->getMessage()]);
    }
}

/**
 * Delete a category (soft delete)
 */
function deleteCategory($db, $id, $merchant_id)
{
    try {
        // Check if category exists and belongs to merchant
        $checkQuery = "SELECT id FROM product_categories WHERE id = ? AND merchant_id = ?";
        $checkStmt = $db->prepare($checkQuery);
        $checkStmt->execute([$id, $merchant_id]);
        $category = $checkStmt->fetch();

        if (!$category) {
            http_response_code(404);
            echo json_encode(['error' => 'Catégorie non trouvée']);
            return;
        }

        // Soft delete by setting is_active=0
        $updateQuery = "UPDATE product_categories SET is_active = 0 WHERE id = ?";
        $stmt = $db->prepare($updateQuery);
        $stmt->execute([$id]);

        echo json_encode([
            'success' => true,
            'data' => [
                'message' => 'Catégorie supprimée avec succès'
            ]
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la suppression: ' . $e->getMessage()]);
    }
}

/**
 * Get user categories for dashboard
 */
function getUserCategories($db)
{
    try {
        // Start session to get current user
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        $merchantEmail = $_GET['email'] ?? '';
        $storeId = $_GET['store_id'] ?? null;

        // Get merchant ID from multiple sources
        $merchantId = null;

        // Method 1: From email parameter
        if ($merchantEmail) {
            $merchantQuery = "SELECT id FROM users WHERE email = ? AND role = 'merchant'";
            $merchantStmt = $db->prepare($merchantQuery);
            $merchantStmt->execute([$merchantEmail]);
            $merchantData = $merchantStmt->fetch(PDO::FETCH_ASSOC);
            $merchantId = $merchantData ? $merchantData['id'] : null;
        }

        // Method 2: From store_id parameter
        if (!$merchantId && $storeId) {
            $storeQuery = "SELECT merchant_id FROM stores WHERE id = ?";
            $storeStmt = $db->prepare($storeQuery);
            $storeStmt->execute([$storeId]);
            $storeData = $storeStmt->fetch(PDO::FETCH_ASSOC);
            $merchantId = $storeData ? $storeData['merchant_id'] : null;
        }

        // Method 3: From current session user
        if (!$merchantId && isset($_SESSION['user']) && $_SESSION['user']['role'] === 'merchant') {
            $sessionEmail = $_SESSION['user']['email'];
            $merchantQuery = "SELECT id FROM users WHERE email = ? AND role = 'merchant'";
            $merchantStmt = $db->prepare($merchantQuery);
            $merchantStmt->execute([$sessionEmail]);
            $merchantData = $merchantStmt->fetch(PDO::FETCH_ASSOC);
            $merchantId = $merchantData ? $merchantData['id'] : null;
        }

        // Default fallback
        if (!$merchantId) {
            $merchantId = 3; // Default to medical software merchant
        }

        // Get categories from categories table for the specific merchant
        $categoriesQuery = "
            SELECT
                c.id,
                COALESCE(c.name_fr, c.name) as name,
                c.slug,
                c.description,
                NULL as parent_id,
                c.status,
                c.created_at,
                COUNT(DISTINCT pc.product_id) as products_count
            FROM categories c
            LEFT JOIN product_categories pc ON c.id = pc.category_id
            WHERE c.merchant_id = ? AND c.status = 'active'
            GROUP BY c.id, c.name, c.name_fr, c.slug, c.description, c.status, c.created_at
            ORDER BY c.name ASC
        ";

        $stmt = $db->prepare($categoriesQuery);
        $stmt->execute([$merchantId]);
        $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Format categories for response (only show categories with products)
        $formattedCategories = [];
        $allCategories = [];
        foreach ($categories as $category) {
            $categoryData = [
                'id' => intval($category['id']),
                'name' => $category['name'],
                'slug' => $category['slug'],
                'description' => $category['description'] ?: '',
                'parent_id' => $category['parent_id'] ? intval($category['parent_id']) : null,
                'products_count' => intval($category['products_count']),
                'status' => $category['status'],
                'created_at' => $category['created_at'],
                'subcategories' => [] // TODO: Add subcategories support if needed
            ];

            $allCategories[] = $categoryData;

            // Only show categories with products in the main list
            if ($categoryData['products_count'] > 0) {
                $formattedCategories[] = $categoryData;
            }
        }

        // Calculate statistics (use all categories for total count, but only active ones for display)
        $totalCategories = count($formattedCategories); // Only categories with products
        $totalSubcategories = 0; // TODO: Add subcategories support
        $totalProducts = array_sum(array_column($allCategories, 'products_count')); // All products from all categories

        // Find most popular category
        $mostPopularCategory = '-';
        if (!empty($formattedCategories)) {
            $maxProducts = max(array_column($formattedCategories, 'products_count'));
            foreach ($formattedCategories as $category) {
                if ($category['products_count'] == $maxProducts && $maxProducts > 0) {
                    $mostPopularCategory = $category['name'];
                    break;
                }
            }
        }

        $stats = [
            'total_categories' => $totalCategories,
            'total_subcategories' => $totalSubcategories,
            'total_products' => $totalProducts,
            'most_popular' => $mostPopularCategory
        ];

        echo json_encode([
            'success' => true,
            'categories' => $formattedCategories,
            'stats' => $stats,
            'total' => count($formattedCategories),
            'merchant_id' => $merchantId
        ]);
    } catch (Exception $e) {
        error_log('Error in getUserCategories: ' . $e->getMessage());
        echo json_encode([
            'success' => false,
            'error' => 'Erreur lors de la récupération des catégories',
            'categories' => []
        ]);
    }
}
